<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="form">
                <div class="layui-form-item">
                    <label class="layui-form-label required">等级ID</label>
                    <div class="layui-input-block">
                        <input type="number" name="level_id" lay-verify="required|number" placeholder="请输入商家等级ID (对应agent_order.order_type)" class="layui-input" value="{{d.detail.level_id || ''}}">
                        <div class="layui-form-mid layui-word-aux">必须是唯一的数字，与商家/代理购买时的订单类型(order_type)对应</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label required">等级名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="level_name" lay-verify="required" placeholder="请输入等级名称" class="layui-input" value="{{d.detail.level_name || ''}}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label required">分配总人数</label>
                    <div class="layui-input-block">
                        <input type="number" name="purchaser_total_count" lay-verify="required|number" placeholder="请输入分配的采购人员总数" class="layui-input" value="{{d.detail.purchaser_total_count || 0}}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">采购人员构成</label>
                    <div class="layui-input-block">
                        <textarea name="purchaser_composition" placeholder="请输入JSON格式的构成比例, 例如: {\"level1\": 50, \"level2\": 20, \"level3\": 30}" class="layui-textarea">{{d.detail.purchaser_composition || ''}}</textarea>
                        <div class="layui-form-mid layui-word-aux">Key为采购员活跃等级(level1, level2...), Value为百分比。总和应为100。如果留空，则不按比例分配。</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label required">每日群发消息数</label>
                    <div class="layui-input-block">
                        <input type="number" name="daily_message_count" lay-verify="required|number" placeholder="请输入每日可群发消息数" class="layui-input" value="{{d.detail.daily_message_count || 0}}">
                    </div>
                </div>

                <input type="hidden" name="id" value="{{d.detail.id || ''}}">
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="submit">保存</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'pear'], function () {
        let form = layui.form;
        let pear = layui.pear;
        let $ = layui.jquery;

        form.on('submit(submit)', function (data) {
            let formData = data.field;
            let url = formData.id ? '{:__URL("edit")}' : '{:__URL("add")}';

            // 校验JSON格式
            if(formData.purchaser_composition){
                try{
                    JSON.parse(formData.purchaser_composition);
                }catch(e){
                    layer.msg('采购人员构成不是一个有效的JSON格式', {icon: 2});
                    return false;
                }
            }

            $.post(url, formData, function (res) {
                if (res.code === 1) {
                    layer.msg(res.msg, {icon: 1}, function () {
                        pear.iframe.close();
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });

            return false;
        });
    });
</script>
