{"name": "topthink/think-queue", "description": "The ThinkPHP6 Queue Package", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "Apache-2.0", "autoload": {"psr-4": {"think\\": "src"}, "files": ["src/common.php"]}, "autoload-dev": {"psr-4": {"think\\test\\queue\\": "tests"}}, "require": {"ext-json": "*", "topthink/framework": "^6.0 || ^8.0", "symfony/process": ">=4.2", "nesbot/carbon": ">=2.16"}, "extra": {"think": {"services": ["think\\queue\\Service"], "config": {"queue": "src/config/queue.php"}}}, "require-dev": {"phpunit/phpunit": "^6.2", "mockery/mockery": "^1.2", "topthink/think-migration": "^3.0"}}