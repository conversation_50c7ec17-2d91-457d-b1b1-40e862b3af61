<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单通知测试</title>
    <link rel="stylesheet" href="/static/admin/css/layui.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #ddd;
        }
        .log-entry.success {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .log-entry.info {
            color: #2196F3;
        }
    </style>
</head>
<body>
    <h1>简单通知测试</h1>

    <div class="button-group">
        <button id="show-notification">显示通知</button>
        <button id="send-test-notification">发送测试通知</button>
        <button id="send-websocket-notification">发送WebSocket通知</button>
        <button id="show-layer-msg">显示简单消息</button>
        <button id="play-sound">播放声音</button>
        <button id="enable-sound">启用声音</button>
        <button id="disable-sound">禁用声音</button>
        <button id="clear-log">清空日志</button>
    </div>

    <div style="margin: 10px 0; padding: 10px; background-color: #ffffd0; border-radius: 4px;">
        <p><strong>声音播放说明：</strong></p>
        <p>1. 由于浏览器安全策略，必须先与页面交互（点击页面）才能播放声音</p>
        <p>2. 如果看到"播放声音失败"错误，请点击"启用声音"按钮</p>
        <p>3. 如果仍然无法播放声音，系统会自动切换到静音模式</p>
    </div>

    <div class="log">
        <div id="log-content"></div>
    </div>

    <script src="/static/admin/js/jquery.min.js"></script>
    <script src="/static/admin/js/layui.js"></script>
    <script src="/static/admin/js/notification.js"></script>
    <script>
        // 添加日志
        function addLog(message, type) {
            var logEntry = $('<div class="log-entry ' + (type || '') + '"></div>');
            logEntry.text('[' + new Date().toLocaleTimeString() + '] ' + message);
            $('#log-content').prepend(logEntry);
        }

        // 显示通知
        $('#show-notification').click(function() {
            addLog('显示通知', 'info');

            try {
                var index = showNotification(
                    '测试通知',
                    '这是一条测试通知，请查收！',
                    'admin_notification',
                    '',
                    0
                );

                addLog('通知显示成功，层索引: ' + index, 'success');
            } catch (error) {
                addLog('显示通知失败: ' + error.message, 'error');
            }
        });

        // 发送测试通知
        $('#send-test-notification').click(function() {
            addLog('发送测试通知', 'info');

            try {
                sendTestNotification(
                    '测试通知 ' + new Date().toLocaleTimeString(),
                    '这是一条通过API发送的测试通知，时间: ' + new Date().toLocaleTimeString(),
                    'admin_notification',
                    '',
                    0
                );

                addLog('测试通知发送成功', 'success');
            } catch (error) {
                addLog('发送测试通知失败: ' + error.message, 'error');
            }
        });

        // 发送WebSocket通知
        $('#send-websocket-notification').click(function() {
            addLog('发送WebSocket通知', 'info');

            try {
                sendWebSocketNotification(
                    'WebSocket通知 ' + new Date().toLocaleTimeString(),
                    '这是一条通过WebSocket API发送的测试通知，时间: ' + new Date().toLocaleTimeString(),
                    'admin_notification',
                    '',
                    0
                );

                addLog('WebSocket通知发送成功', 'success');
            } catch (error) {
                addLog('发送WebSocket通知失败: ' + error.message, 'error');
            }
        });

        // 显示简单消息
        $('#show-layer-msg').click(function() {
            addLog('显示简单消息', 'info');

            try {
                layer.msg('这是一条简单消息', {icon: 1, time: 3000});
                addLog('简单消息显示成功', 'success');
            } catch (error) {
                addLog('显示简单消息失败: ' + error.message, 'error');
            }
        });

        // 播放声音
        $('#play-sound').click(function() {
            addLog('播放声音', 'info');

            try {
                playNotificationSound(false)
                    .then(function() {
                        addLog('声音播放成功', 'success');
                    })
                    .catch(function(error) {
                        addLog('播放声音失败: ' + error.message, 'error');

                        if (error.name === 'NotAllowedError') {
                            addLog('浏览器阻止自动播放声音，请先点击"启用声音"按钮', 'error');
                        }
                    });
            } catch (error) {
                addLog('播放声音失败: ' + error.message, 'error');
            }
        });

        // 启用声音
        $('#enable-sound').click(function() {
            addLog('启用声音', 'info');

            try {
                enableNotificationSound();
                addLog('声音已启用', 'success');
            } catch (error) {
                addLog('启用声音失败: ' + error.message, 'error');
            }
        });

        // 禁用声音
        $('#disable-sound').click(function() {
            addLog('禁用声音', 'info');

            try {
                disableNotificationSound();
                addLog('声音已禁用', 'success');
            } catch (error) {
                addLog('禁用声音失败: ' + error.message, 'error');
            }
        });

        // 清空日志
        $('#clear-log').click(function() {
            $('#log-content').empty();
            addLog('日志已清空', 'info');
        });

        // 页面加载完成
        $(document).ready(function() {
            addLog('页面加载完成', 'info');

            // 初始化LayUI
            layui.use(['layer'], function() {
                var layer = layui.layer;

                // 显示欢迎消息
                layer.msg('简单通知测试工具已加载', {icon: 1, time: 2000});
                addLog('LayUI初始化完成', 'success');
            });
        });
    </script>
</body>
</html>
