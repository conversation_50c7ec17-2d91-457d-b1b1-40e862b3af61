<?php
/**
 * 测试修复后的队列功能
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 测试修复后的队列功能 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 1. 推送订单取消任务
    echo "1. 推送订单取消任务...\n";
    $orderData = [
        'order_id' => 999999,
        'user_id' => 1
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJob', $orderData, 'orderCancel');
    echo "   ✓ 订单取消任务推送成功\n";
    echo "   任务数据: " . json_encode($orderData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 2. 推送AfterCancelOrder任务
    echo "2. 推送AfterCancelOrder任务...\n";
    $afterData = [
        'type' => 1,
        'channel' => 'user_cancel_order',
        'order_id' => 999999,
        'handle_id' => 1,
    ];
    
    \think\facade\Queue::push('app\common\job\AfterCancelOrderJob', $afterData, 'orderCancel');
    echo "   ✓ AfterCancelOrder任务推送成功\n";
    echo "   任务数据: " . json_encode($afterData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "3. 修复说明:\n";
    echo "   ✓ 将事件处理改为异步队列\n";
    echo "   ✓ 增加了队列超时时间到5分钟\n";
    echo "   ✓ 分离了订单状态更新和后续处理\n\n";
    
    echo "4. 启动队列监听:\n";
    echo "   php start_queue.php\n";
    echo "   或者: php think queue:listen --queue=orderCancel --timeout=300\n\n";
    
    echo "=== 测试完成 ===\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
