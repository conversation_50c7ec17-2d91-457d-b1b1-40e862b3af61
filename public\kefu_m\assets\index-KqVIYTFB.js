function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-sessionlist-sessionlist.zoK3A1EM.js","assets/uni-app.es.elp5fm4t.js","assets/uni-app-BLmHtDXk.css","assets/zb-popover.DS7TbkBw.js","assets/zb-popover-D7F3h8RV.css","assets/z-paging.DXcm7bPn.js","assets/z-paging-8X5vRC30.css","assets/sessionlist-Da9AljzJ.css","assets/pages-index-index.C3w4cmij.js","assets/uv-button.CCU3gl96.js","assets/uv-button-CO4O5kZo.css","assets/pages-login-login.B2tPFuBA.js","assets/login-aXdU6o6a.css","assets/pages-chat-chat.BaQirvQR.js","assets/chat-Pg4QvfJD.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var e=Object.defineProperty,t=(t,n,o)=>(((t,n,o)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o})(t,"symbol"!=typeof n?n+"":n,o),o);!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n={},o=function(e,t,o){let r=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(t.map((t=>{if((t=function(e){return"/kefu_m/"+e}(t))in n)return;n[t]=!0;const r=t.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let n=e.length-1;n>=0;n--){const o=e[n];if(o.href===t&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=t,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0})))}return r.then((()=>e())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function r(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const i={},s=[],a=()=>{},l=()=>!1,c=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),u=e=>e.startsWith("onUpdate:"),d=Object.assign,f=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},p=Object.prototype.hasOwnProperty,h=(e,t)=>p.call(e,t),g=Array.isArray,m=e=>"[object Map]"===T(e),v=e=>"[object Set]"===T(e),y=e=>"function"==typeof e,b=e=>"string"==typeof e,w=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,x=e=>(_(e)||y(e))&&y(e.then)&&y(e.catch),S=Object.prototype.toString,T=e=>S.call(e),k=e=>"[object Object]"===T(e),C=e=>b(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,E=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),O=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},$=/-(\w)/g,L=O((e=>e.replace($,((e,t)=>t?t.toUpperCase():"")))),P=/\B([A-Z])/g,A=O((e=>e.replace(P,"-$1").toLowerCase())),M=O((e=>e.charAt(0).toUpperCase()+e.slice(1))),I=O((e=>e?`on${M(e)}`:"")),j=(e,t)=>!Object.is(e,t),N=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},R=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},B=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let F;const D=()=>F||(F="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function H(e){if(g(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?z(o):H(o);if(r)for(const e in r)t[e]=r[e]}return t}if(b(e)||_(e))return e}const V=/;(?![^(]*\))/g,W=/:([^]+)/,q=/\/\*[^]*?\*\//g;function z(e){const t={};return e.replace(q,"").split(V).forEach((e=>{if(e){const n=e.split(W);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function U(e){let t="";if(b(e))t=e;else if(g(e))for(let n=0;n<e.length;n++){const o=U(e[n]);o&&(t+=o+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const X=r("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Y(e){return!!e||""===e}const G=e=>b(e)?e:null==e?"":g(e)||_(e)&&(e.toString===S||!y(e.toString))?JSON.stringify(e,Z,2):String(e),Z=(e,t)=>t&&t.__v_isRef?Z(e,t.value):m(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[J(t,o)+" =>"]=n,e)),{})}:v(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>J(e)))}:w(t)?J(t):!_(t)||g(t)||k(t)?t:String(t),J=(e,t="")=>{var n;return w(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},K=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map((e=>"uni-"+e)),Q=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),ee=["list-item"].map((e=>"uni-"+e));function te(e){if(-1!==ee.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==K.indexOf(t)||-1!==Q.indexOf(t)}const ne=/^([a-z-]+:)?\/\//i,oe=/^data:.*,.*/,re="onShow",ie="onLoad",se="onReady",ae="onReachBottom";function le(e){return 0===e.indexOf("/")}function ce(e){return le(e)?e:"/"+e}function ue(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function de(e,t){e=e||{},b(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?y(e.success)&&e.success(t):y(e.fail)&&e.fail(t),y(e.complete)&&e.complete(t)}let fe;function pe(){return fe||(fe=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),fe)}function he(e){return e&&(e.appContext?e.proxy:e)}function ge(e){if(!e)return;let t=e.type.name;for(;t&&te(A(t));)t=(e=e.parent).type.name;return e.proxy}function me(e){return 1===e.nodeType}function ve(e){const t=pe();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach((t=>{n[t]=e[t]})),H(n)}if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),H(t)}if(b(e))return z(e);if(g(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?z(o):ve(o);if(r)for(const e in r)t[e]=r[e]}return t}return H(e)}function ye(e){let t="";const n=pe();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach((n=>{e[n]&&(t+=n+" ")}));else if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(g(e))for(let o=0;o<e.length;o++){const n=ye(e[o]);n&&(t+=n+" ")}else t=U(e);return t.trim()}function be(e){return L(e.substring(5))}const we=ue((e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[be(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[be(t)],o.call(this,t)}}));function _e(e){return d({},e.dataset,e.__uniDataset)}const xe=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function Se(e){return{passive:e}}function Te(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:_e(e),offsetTop:n,offsetLeft:o}}function ke(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Ce(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=ke(e[n])}catch(o){t[n]=e[n]}})),t}const Ee=/\+/g;function Oe(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Ee," ");let r=e.indexOf("="),i=ke(r<0?e:e.slice(0,r)),s=r<0?null:ke(e.slice(r+1));if(i in t){let e=t[i];g(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function $e(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Le{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Pe=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Ae=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Me=[];const Ie=ue(((e,t)=>t(e))),je=function(){};je.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Ne=je;const Re={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Be(e,t,n){if(b(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in Re?Re[o]:o}return r}var o;return t}function Fe(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach((i=>{const s=e[i];r[i]=k(s)?Fe(s,t,n):g(s)?s.map((e=>k(e)?Fe(e,t,n):Be(o,e))):Be(o,s,i)})),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let De,He;class Ve{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=De,!e&&De&&(this.index=(De.scopes||(De.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=De;try{return De=this,e()}finally{De=t}}}on(){De=this}off(){De=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function We(e){return new Ve(e)}class qe{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=De){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Je();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Ke()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=Ye,t=He;try{return Ye=!0,He=this,this._runnings++,ze(this),this.fn()}finally{Ue(this),this._runnings--,He=t,Ye=e}}stop(){var e;this.active&&(ze(this),Ue(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function ze(e){e._trackId++,e._depsLength=0}function Ue(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Xe(e.deps[t],e);e.deps.length=e._depsLength}}function Xe(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let Ye=!0,Ge=0;const Ze=[];function Je(){Ze.push(Ye),Ye=!1}function Ke(){const e=Ze.pop();Ye=void 0===e||e}function Qe(){Ge++}function et(){for(Ge--;!Ge&&nt.length;)nt.shift()()}function tt(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&Xe(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const nt=[];function ot(e,t,n){Qe();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&nt.push(o.scheduler)))}et()}const rt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},it=new WeakMap,st=Symbol(""),at=Symbol("");function lt(e,t,n){if(Ye&&He){let t=it.get(e);t||it.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=rt((()=>t.delete(n)))),tt(He,o)}}function ct(e,t,n,o,r,i){const s=it.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&g(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!w(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":g(e)?C(n)&&a.push(s.get("length")):(a.push(s.get(st)),m(e)&&a.push(s.get(at)));break;case"delete":g(e)||(a.push(s.get(st)),m(e)&&a.push(s.get(at)));break;case"set":m(e)&&a.push(s.get(st))}Qe();for(const l of a)l&&ot(l,4);et()}const ut=r("__proto__,__v_isRef,__isVue"),dt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(w)),ft=pt();function pt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=tn(this);for(let t=0,r=this.length;t<r;t++)lt(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(tn)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Je(),Qe();const n=tn(this)[t].apply(this,e);return et(),Ke(),n}})),e}function ht(e){const t=tn(this);return lt(t,0,e),t.hasOwnProperty(e)}class gt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?zt:qt:r?Wt:Vt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=g(e);if(!o){if(i&&h(ft,t))return Reflect.get(ft,t,n);if("hasOwnProperty"===t)return ht}const s=Reflect.get(e,t,n);return(w(t)?dt.has(t):ut(t))?s:(o||lt(e,0,t),r?s:cn(s)?i&&C(t)?s:s.value:_(s)?o?Gt(s):Xt(s):s)}}class mt extends gt{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Kt(r);if(Qt(n)||Kt(n)||(r=tn(r),n=tn(n)),!g(e)&&cn(r)&&!cn(n))return!t&&(r.value=n,!0)}const i=g(e)&&C(t)?Number(t)<e.length:h(e,t),s=Reflect.set(e,t,n,o);return e===tn(o)&&(i?j(n,r)&&ct(e,"set",t,n):ct(e,"add",t,n)),s}deleteProperty(e,t){const n=h(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&ct(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return w(t)&&dt.has(t)||lt(e,0,t),n}ownKeys(e){return lt(e,0,g(e)?"length":st),Reflect.ownKeys(e)}}class vt extends gt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const yt=new mt,bt=new vt,wt=new mt(!0),_t=e=>e,xt=e=>Reflect.getPrototypeOf(e);function St(e,t,n=!1,o=!1){const r=tn(e=e.__v_raw),i=tn(t);n||(j(t,i)&&lt(r,0,t),lt(r,0,i));const{has:s}=xt(r),a=o?_t:n?rn:on;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function Tt(e,t=!1){const n=this.__v_raw,o=tn(n),r=tn(e);return t||(j(e,r)&&lt(o,0,e),lt(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function kt(e,t=!1){return e=e.__v_raw,!t&&lt(tn(e),0,st),Reflect.get(e,"size",e)}function Ct(e){e=tn(e);const t=tn(this);return xt(t).has.call(t,e)||(t.add(e),ct(t,"add",e,e)),this}function Et(e,t){t=tn(t);const n=tn(this),{has:o,get:r}=xt(n);let i=o.call(n,e);i||(e=tn(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?j(t,s)&&ct(n,"set",e,t):ct(n,"add",e,t),this}function Ot(e){const t=tn(this),{has:n,get:o}=xt(t);let r=n.call(t,e);r||(e=tn(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&ct(t,"delete",e,void 0),i}function $t(){const e=tn(this),t=0!==e.size,n=e.clear();return t&&ct(e,"clear",void 0,void 0),n}function Lt(e,t){return function(n,o){const r=this,i=r.__v_raw,s=tn(i),a=t?_t:e?rn:on;return!e&&lt(s,0,st),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function Pt(e,t,n){return function(...o){const r=this.__v_raw,i=tn(r),s=m(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?_t:t?rn:on;return!t&&lt(i,0,l?at:st),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function At(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Mt(){const e={get(e){return St(this,e)},get size(){return kt(this)},has:Tt,add:Ct,set:Et,delete:Ot,clear:$t,forEach:Lt(!1,!1)},t={get(e){return St(this,e,!1,!0)},get size(){return kt(this)},has:Tt,add:Ct,set:Et,delete:Ot,clear:$t,forEach:Lt(!1,!0)},n={get(e){return St(this,e,!0)},get size(){return kt(this,!0)},has(e){return Tt.call(this,e,!0)},add:At("add"),set:At("set"),delete:At("delete"),clear:At("clear"),forEach:Lt(!0,!1)},o={get(e){return St(this,e,!0,!0)},get size(){return kt(this,!0)},has(e){return Tt.call(this,e,!0)},add:At("add"),set:At("set"),delete:At("delete"),clear:At("clear"),forEach:Lt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Pt(r,!1,!1),n[r]=Pt(r,!0,!1),t[r]=Pt(r,!1,!0),o[r]=Pt(r,!0,!0)})),[e,n,t,o]}const[It,jt,Nt,Rt]=Mt();function Bt(e,t){const n=t?e?Rt:Nt:e?jt:It;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(h(n,o)&&o in t?n:t,o,r)}const Ft={get:Bt(!1,!1)},Dt={get:Bt(!1,!0)},Ht={get:Bt(!0,!1)},Vt=new WeakMap,Wt=new WeakMap,qt=new WeakMap,zt=new WeakMap;function Ut(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>T(e).slice(8,-1))(e))}function Xt(e){return Kt(e)?e:Zt(e,!1,yt,Ft,Vt)}function Yt(e){return Zt(e,!1,wt,Dt,Wt)}function Gt(e){return Zt(e,!0,bt,Ht,qt)}function Zt(e,t,n,o,r){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=Ut(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Jt(e){return Kt(e)?Jt(e.__v_raw):!(!e||!e.__v_isReactive)}function Kt(e){return!(!e||!e.__v_isReadonly)}function Qt(e){return!(!e||!e.__v_isShallow)}function en(e){return Jt(e)||Kt(e)}function tn(e){const t=e&&e.__v_raw;return t?tn(t):e}function nn(e){return Object.isExtensible(e)&&R(e,"__v_skip",!0),e}const on=e=>_(e)?Xt(e):e,rn=e=>_(e)?Gt(e):e;class sn{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new qe((()=>e(this._value)),(()=>ln(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=tn(this);return e._cacheable&&!e.effect.dirty||!j(e._value,e._value=e.effect.run())||ln(e,4),an(e),e.effect._dirtyLevel>=2&&ln(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function an(e){var t;Ye&&He&&(e=tn(e),tt(He,null!=(t=e.dep)?t:e.dep=rt((()=>e.dep=void 0),e instanceof sn?e:void 0)))}function ln(e,t=4,n){const o=(e=tn(e)).dep;o&&ot(o,t)}function cn(e){return!(!e||!0!==e.__v_isRef)}function un(e){return fn(e,!1)}function dn(e){return fn(e,!0)}function fn(e,t){return cn(e)?e:new pn(e,t)}class pn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:tn(e),this._value=t?e:on(e)}get value(){return an(this),this._value}set value(e){const t=this.__v_isShallow||Qt(e)||Kt(e);e=t?e:tn(e),j(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:on(e),ln(this,4))}}function hn(e){return cn(e)?e.value:e}const gn={get:(e,t,n)=>hn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return cn(r)&&!cn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function mn(e){return Jt(e)?e:new Proxy(e,gn)}function vn(e,t,n,o){try{return o?e(...o):e()}catch(r){bn(r,t,n)}}function yn(e,t,n,o){if(y(e)){const r=vn(e,t,n,o);return r&&x(r)&&r.catch((e=>{bn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(yn(e[i],t,n,o));return r}function bn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void vn(s,null,10,[e,r,i])}wn(e,n,r,o)}function wn(e,t,n,o=!0){console.error(e)}let _n=!1,xn=!1;const Sn=[];let Tn=0;const kn=[];let Cn=null,En=0;const On=Promise.resolve();let $n=null;function Ln(e){const t=$n||On;return e?t.then(this?e.bind(this):e):t}function Pn(e){Sn.length&&Sn.includes(e,_n&&e.allowRecurse?Tn+1:Tn)||(null==e.id?Sn.push(e):Sn.splice(function(e){let t=Tn+1,n=Sn.length;for(;t<n;){const o=t+n>>>1,r=Sn[o],i=jn(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),An())}function An(){_n||xn||(xn=!0,$n=On.then(Rn))}function Mn(e,t,n=(_n?Tn+1:0)){for(;n<Sn.length;n++){const t=Sn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;Sn.splice(n,1),n--,t()}}}function In(e){if(kn.length){const e=[...new Set(kn)].sort(((e,t)=>jn(e)-jn(t)));if(kn.length=0,Cn)return void Cn.push(...e);for(Cn=e,En=0;En<Cn.length;En++)Cn[En]();Cn=null,En=0}}const jn=e=>null==e.id?1/0:e.id,Nn=(e,t)=>{const n=jn(e)-jn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Rn(e){xn=!1,_n=!0,Sn.sort(Nn);try{for(Tn=0;Tn<Sn.length;Tn++){const e=Sn[Tn];e&&!1!==e.active&&vn(e,null,14)}}finally{Tn=0,Sn.length=0,In(),_n=!1,$n=null,(Sn.length||kn.length)&&Rn()}}function Bn(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||i;let r=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=o[e]||i;s&&(r=n.map((e=>b(e)?e.trim():e))),t&&(r=n.map(B))}let l,c=o[l=I(t)]||o[l=I(L(t))];!c&&s&&(c=o[l=I(A(t))]),c&&yn(c,e,6,Fn(e,c,r));const u=o[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,yn(u,e,6,Fn(e,u,r))}}function Fn(e,t,n){if(1!==n.length)return n;if(y(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&h(o,"type")&&h(o,"timeStamp")&&h(o,"target")&&h(o,"currentTarget")&&h(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Dn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!y(e)){const o=e=>{const n=Dn(e,t,!0);n&&(a=!0,d(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(g(i)?i.forEach((e=>s[e]=null)):d(s,i),_(e)&&o.set(e,s),s):(_(e)&&o.set(e,null),null)}function Hn(e,t){return!(!e||!c(t))&&(t=t.slice(2).replace(/Once$/,""),h(e,t[0].toLowerCase()+t.slice(1))||h(e,A(t))||h(e,t))}let Vn=null,Wn=null;function qn(e){const t=Vn;return Vn=e,Wn=e&&e.type.__scopeId||null,t}function zn(e,t=Vn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Kr(-1);const r=qn(t);let i;try{i=e(...n)}finally{qn(r),o._d&&Kr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Un(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:l,emit:c,render:d,renderCache:f,data:p,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const b=qn(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=fi(d.call(t,e,f,i,h,p,g)),y=l}else{const e=t;0,v=fi(e.length>1?e(i,{attrs:l,slots:a,emit:c}):e(i,null)),y=t.props?l:Xn(l)}}catch(_){Yr.length=0,bn(_,e,1),v=li(Ur)}let w=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=w;e.length&&7&t&&(s&&e.some(u)&&(y=Yn(y,s)),w=ci(w,y))}return n.dirs&&(w=ci(w),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&(w.transition=n.transition),v=w,qn(b),v}const Xn=e=>{let t;for(const n in e)("class"===n||"style"===n||c(n))&&((t||(t={}))[n]=e[n]);return t},Yn=(e,t)=>{const n={};for(const o in e)u(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Gn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Hn(n,i))return!0}return!1}function Zn(e,t){return Qn("components",e,!0,t)||e}const Jn=Symbol.for("v-ndc");function Kn(e){return b(e)?Qn("components",e,!1)||e:e||Jn}function Qn(e,t,n=!0,o=!1){const r=Vn||bi;if(r){const n=r.type;if("components"===e){const e=Li(n,!1);if(e&&(e===t||e===L(t)||e===M(L(t))))return n}const i=eo(r[e]||n[e],t)||eo(r.appContext[e],t);return!i&&o?n:i}}function eo(e,t){return e&&(e[t]||e[L(t)]||e[M(L(t))])}const to=e=>e.__isSuspense;const no=Symbol.for("v-scx");function oo(e,t){return so(e,null,t)}const ro={};function io(e,t,n){return so(e,t,n)}function so(e,t,{immediate:n,deep:o,flush:r,once:s,onTrack:l,onTrigger:c}=i){if(t&&s){const e=t;t=(...t)=>{e(...t),C()}}const u=bi,d=e=>!0===o?e:co(e,!1===o?1:void 0);let p,h,m=!1,v=!1;if(cn(e)?(p=()=>e.value,m=Qt(e)):Jt(e)?(p=()=>d(e),m=!0):g(e)?(v=!0,m=e.some((e=>Jt(e)||Qt(e))),p=()=>e.map((e=>cn(e)?e.value:Jt(e)?d(e):y(e)?vn(e,u,2):void 0))):p=y(e)?t?()=>vn(e,u,2):()=>(h&&h(),yn(e,u,3,[w])):a,t&&o){const e=p;p=()=>co(e())}let b,w=e=>{h=T.onStop=()=>{vn(e,u,4),h=T.onStop=void 0}};if(Ci){if(w=a,t?n&&yn(t,u,3,[p(),v?[]:void 0,w]):p(),"sync"!==r)return a;{const e=Tr(no);b=e.__watcherHandles||(e.__watcherHandles=[])}}let _=v?new Array(e.length).fill(ro):ro;const x=()=>{if(T.active&&T.dirty)if(t){const e=T.run();(o||m||(v?e.some(((e,t)=>j(e,_[t]))):j(e,_)))&&(h&&h(),yn(t,u,3,[e,_===ro?void 0:v&&_[0]===ro?[]:_,w]),_=e)}else T.run()};let S;x.allowRecurse=!!t,"sync"===r?S=x:"post"===r?S=()=>Br(x,u&&u.suspense):(x.pre=!0,u&&(x.id=u.uid),S=()=>Pn(x));const T=new qe(p,a,S),k=De,C=()=>{T.stop(),k&&f(k.effects,T)};return t?n?x():_=T.run():"post"===r?Br(T.run.bind(T),u&&u.suspense):T.run(),b&&b.push(C),C}function ao(e,t,n){const o=this.proxy,r=b(e)?e.includes(".")?lo(o,e):()=>o[e]:e.bind(o,o);let i;y(t)?i=t:(i=t.handler,n=t);const s=Si(this),a=so(r,i.bind(o),n);return s(),a}function lo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function co(e,t,n=0,o){if(!_(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),cn(e))co(e.value,t,n,o);else if(g(e))for(let r=0;r<e.length;r++)co(e[r],t,n,o);else if(v(e)||m(e))e.forEach((e=>{co(e,t,n,o)}));else if(k(e))for(const r in e)co(e[r],t,n,o);return e}function uo(e,t){if(null===Vn)return e;const n=$i(Vn)||Vn.proxy,o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[e,s,a,l=i]=t[r];e&&(y(e)&&(e={mounted:e,updated:e}),e.deep&&co(s),o.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function fo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Je(),yn(l,n,8,[e.el,a,e,t]),Ke())}}const po=Symbol("_leaveCb"),ho=Symbol("_enterCb");const go=[Function,Array],mo={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:go,onEnter:go,onAfterEnter:go,onEnterCancelled:go,onBeforeLeave:go,onLeave:go,onAfterLeave:go,onLeaveCancelled:go,onBeforeAppear:go,onAppear:go,onAfterAppear:go,onAppearCancelled:go},vo={name:"BaseTransition",props:mo,setup(e,{slots:t}){const n=wi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return qo((()=>{e.isMounted=!0})),Xo((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&So(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==Ur){i=e;break}const s=tn(e),{mode:a}=s;if(o.isLeaving)return wo(i);const l=_o(i);if(!l)return wo(i);const c=bo(l,s,o,n);xo(l,c);const u=n.subTree,d=u&&_o(u);if(d&&d.type!==Ur&&!oi(l,d)){const e=bo(d,s,o,n);if(xo(d,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},wo(i);"in-out"===a&&l.type!==Ur&&(e.delayLeave=(e,t,n)=>{yo(o,d)[String(d.key)]=d,e[po]=()=>{t(),e[po]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function yo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function bo(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:p,onLeaveCancelled:h,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,w=String(e.key),_=yo(n,e),x=(e,t)=>{e&&yn(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),g(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=m||a}t[po]&&t[po](!0);const i=_[w];i&&oi(e,i)&&i.el[po]&&i.el[po](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let s=!1;const a=e[ho]=t=>{s||(s=!0,x(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e[ho]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[ho]&&t[ho](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const s=t[po]=n=>{i||(i=!0,o(),x(n?h:p,[t]),t[po]=void 0,_[r]===e&&delete _[r])};_[r]=e,f?S(f,[t,s]):s()},clone:e=>bo(e,t,n,o)};return T}function wo(e){if(Oo(e))return(e=ci(e)).children=null,e}function _o(e){return Oo(e)?e.children?e.children[0]:void 0:e}function xo(e,t){6&e.shapeFlag&&e.component?xo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function So(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===qr?(128&s.patchFlag&&r++,o=o.concat(So(s.children,t,a))):(t||s.type!==Ur)&&o.push(null!=a?ci(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function To(e,t){return y(e)?(()=>d({name:e.name},t,{setup:e}))():e}const ko=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function Co(e){y(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return To({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=bi;if(l)return()=>Eo(l,e);const t=t=>{c=null,bn(t,e,13,!o)};if(s&&e.suspense||Ci)return d().then((t=>()=>Eo(t,e))).catch((e=>(t(e),()=>o?li(o,{error:e}):null)));const a=un(!1),u=un(),f=un(!!r);return r&&setTimeout((()=>{f.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{a.value=!0,e.parent&&Oo(e.parent.vnode)&&(e.parent.effect.dirty=!0,Pn(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?Eo(l,e):u.value&&o?li(o,{error:u.value}):n&&!f.value?li(n):void 0}})}function Eo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=li(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const Oo=e=>e.type.__isKeepAlive;class $o{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Lo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=wi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new $o(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!oi(t,i)||"key"===e.matchBy&&t.key!==i.key?(Ro(o=t),u(o,n,a,!0)):i&&Ro(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");function p(t){r.forEach(((n,o)=>{const i=Fo(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,N(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),Br((()=>{i.isDeactivated=!1,i.a&&N(i.a);const t=e.props&&e.props.onVnodeMounted;t&&mi(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&Do(t.bda),c(e,f,null,1,a),Br((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&N(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&mi(n,t.parent,e),t.isDeactivated=!0}),a)},io((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&p((t=>Ao(e,t))),t&&p((e=>!Ao(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&r.set(h,Bo(n.subTree))};return qo(g),Uo(g),Xo((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=Bo(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&N(l.component.bda),Ro(l);const e=l.component.da;e&&Br(e,a)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!ni(o)||!(4&o.shapeFlag)&&!to(o.type))return i=null,o;let s=Bo(o);const a=s.type,l=Fo(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Ao(c,l))||u&&l&&Ao(u,l))return i=s,o;const d=null==s.key?a:s.key,f=r.get(d);return s.el&&(s=ci(s),to(o.type)&&(o.ssContent=s)),h=d,f&&(s.el=f.el,s.component=f.component,s.transition&&xo(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,to(o.type)?o:s}}},Po=Lo;function Ao(e,t){return g(e)?e.some((e=>Ao(e,t))):b(e)?e.split(",").includes(t):"[object RegExp]"===T(e)&&e.test(t)}function Mo(e,t){jo(e,"a",t)}function Io(e,t){jo(e,"da",t)}function jo(e,t,n=bi){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Ho(t,o,n),n){let e=n.parent;for(;e&&e.parent;)Oo(e.parent.vnode)&&No(o,t,n,e),e=e.parent}}function No(e,t,n,o){const r=Ho(t,e,o,!0);Yo((()=>{f(o[t],r)}),n)}function Ro(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Bo(e){return to(e.type)?e.ssContent:e}function Fo(e,t){if("name"===t){const t=e.type;return Li(ko(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Do(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Ho(e,t,n=bi,o=!1){if(n){if(r=e,Pe.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;yn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Je();const r=Si(n),i=yn(t,n,e,o);return r(),Ke(),i});return o?i.unshift(s):i.push(s),s}var r}const Vo=e=>(t,n=bi)=>(!Ci||"sp"===e)&&Ho(e,((...e)=>t(...e)),n),Wo=Vo("bm"),qo=Vo("m"),zo=Vo("bu"),Uo=Vo("u"),Xo=Vo("bum"),Yo=Vo("um"),Go=Vo("sp"),Zo=Vo("rtg"),Jo=Vo("rtc");function Ko(e,t=bi){Ho("ec",e,t)}function Qo(e,t,n,o){let r;const i=n&&n[o];if(g(e)||b(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(_(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function er(e,t,n={},o,r){if(Vn.isCE||Vn.parent&&ko(Vn.parent)&&Vn.parent.isCE)return"default"!==t&&(n.name=t),li("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Zr();const s=i&&tr(i(n)),a=ti(qr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function tr(e){return e.some((e=>!ni(e)||e.type!==Ur&&!(e.type===qr&&!tr(e.children))))?e:null}const nr=e=>{if(!e)return null;if(ki(e)){return $i(e)||e.proxy}return nr(e.parent)},or=d(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>nr(e.parent),$root:e=>nr(e.root),$emit:e=>e.emit,$options:e=>dr(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,Pn(e.update)})(e)),$nextTick:e=>e.n||(e.n=Ln.bind(e.proxy)),$watch:e=>ao.bind(e)}),rr=(e,t)=>e!==i&&!e.__isScriptSetup&&h(e,t),ir={get({_:e},t){const{ctx:n,setupState:o,data:r,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return s[t]}else{if(rr(o,t))return a[t]=1,o[t];if(r!==i&&h(r,t))return a[t]=2,r[t];if((u=e.propsOptions[0])&&h(u,t))return a[t]=3,s[t];if(n!==i&&h(n,t))return a[t]=4,n[t];ar&&(a[t]=0)}}const d=or[t];let f,p;return d?("$attrs"===t&&lt(e,0,t),d(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==i&&h(n,t)?(a[t]=4,n[t]):(p=c.config.globalProperties,h(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:s}=e;return rr(r,t)?(r[t]=n,!0):o!==i&&h(o,t)?(o[t]=n,!0):!h(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:s}},a){let l;return!!n[a]||e!==i&&h(e,a)||rr(t,a)||(l=s[0])&&h(l,a)||h(o,a)||h(or,a)||h(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:h(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function sr(e){return g(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let ar=!0;function lr(e){const t=dr(e),n=e.proxy,o=e.ctx;ar=!1,t.beforeCreate&&cr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:s,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:p,beforeUpdate:h,updated:m,activated:v,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:T,render:k,renderTracked:C,renderTriggered:E,errorCaptured:O,serverPrefetch:$,expose:L,inheritAttrs:P,components:A,directives:M,filters:I}=t;if(u&&function(e,t,n=a){g(e)&&(e=gr(e));for(const o in e){const n=e[o];let r;r=_(n)?"default"in n?Tr(n.from||o,n.default,!0):Tr(n.from||o):Tr(n),cn(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),s)for(const a in s){const e=s[a];y(e)&&(o[a]=e.bind(n))}if(r){const t=r.call(n,n);_(t)&&(e.data=Xt(t))}if(ar=!0,i)for(const g in i){const e=i[g],t=y(e)?e.bind(n,n):y(e.get)?e.get.bind(n,n):a,r=!y(e)&&y(e.set)?e.set.bind(n):a,s=Pi({get:t,set:r});Object.defineProperty(o,g,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const a in l)ur(l[a],o,n,a);if(c){const e=y(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{Sr(t,e[t])}))}function j(e,t){g(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&cr(d,e,"c"),j(Wo,f),j(qo,p),j(zo,h),j(Uo,m),j(Mo,v),j(Io,b),j(Ko,O),j(Jo,C),j(Zo,E),j(Xo,x),j(Yo,T),j(Go,$),g(L))if(L.length){const t=e.exposed||(e.exposed={});L.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===a&&(e.render=k),null!=P&&(e.inheritAttrs=P),A&&(e.components=A),M&&(e.directives=M);const N=e.appContext.config.globalProperties.$applyOptions;N&&N(t,e,n)}function cr(e,t,n){yn(g(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function ur(e,t,n,o){const r=o.includes(".")?lo(n,o):()=>n[o];if(b(e)){const n=t[e];y(n)&&io(r,n)}else if(y(e))io(r,e.bind(n));else if(_(e))if(g(e))e.forEach((e=>ur(e,t,n,o)));else{const o=y(e.handler)?e.handler.bind(n):t[e.handler];y(o)&&io(r,o,e)}}function dr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>fr(l,e,s,!0))),fr(l,t,s)):l=t,_(t)&&i.set(t,l),l}function fr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&fr(e,i,n,!0),r&&r.forEach((t=>fr(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=pr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const pr={data:hr,props:yr,emits:yr,methods:vr,computed:vr,beforeCreate:mr,created:mr,beforeMount:mr,mounted:mr,beforeUpdate:mr,updated:mr,beforeDestroy:mr,beforeUnmount:mr,destroyed:mr,unmounted:mr,activated:mr,deactivated:mr,errorCaptured:mr,serverPrefetch:mr,components:vr,directives:vr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=d(Object.create(null),e);for(const o in t)n[o]=mr(e[o],t[o]);return n},provide:hr,inject:function(e,t){return vr(gr(e),gr(t))}};function hr(e,t){return t?e?function(){return d(y(e)?e.call(this,this):e,y(t)?t.call(this,this):t)}:t:e}function gr(e){if(g(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function mr(e,t){return e?[...new Set([].concat(e,t))]:t}function vr(e,t){return e?d(Object.create(null),e,t):t}function yr(e,t){return e?g(e)&&g(t)?[...new Set([...e,...t])]:d(Object.create(null),sr(e),sr(null!=t?t:{})):t}function br(){return{app:null,config:{isNativeTag:l,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let wr=0;function _r(e,t){return function(n,o=null){y(n)||(n=d({},n)),null==o||_(o)||(o=null);const r=br(),i=new WeakSet;let s=!1;const a=r.app={_uid:wr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Mi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&y(e.install)?(i.add(e),e.install(a,...t)):y(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=li(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,$i(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=xr;xr=a;try{return e()}finally{xr=t}}};return a}}let xr=null;function Sr(e,t){if(bi){let n=bi.provides;const o=bi.parent&&bi.parent.provides;o===n&&(n=bi.provides=Object.create(o)),n[e]=t,"app"===bi.type.mpType&&bi.appContext.app.provide(e,t)}else;}function Tr(e,t,n=!1){const o=bi||Vn;if(o||xr){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:xr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&y(t)?t.call(o&&o.proxy):t}}function kr(e,t,n,o){const[r,s]=e.propsOptions;let a,l=!1;if(t)for(let i in t){if(E(i))continue;const c=t[i];let u;r&&h(r,u=L(i))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:Hn(e.emitsOptions,i)||i in o&&c===o[i]||(o[i]=c,l=!0)}if(s){const t=tn(n),o=a||i;for(let i=0;i<s.length;i++){const a=s[i];n[a]=Cr(r,t,a,o[a],e,!h(o,a))}}return l}function Cr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=h(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&y(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=Si(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==A(n)||(o=!0))}return o}function Er(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const a=e.props,l={},c=[];let u=!1;if(!y(e)){const o=e=>{u=!0;const[n,o]=Er(e,t,!0);d(l,n),o&&c.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!u)return _(e)&&o.set(e,s),s;if(g(a))for(let s=0;s<a.length;s++){const e=L(a[s]);Or(e)&&(l[e]=i)}else if(a)for(const i in a){const e=L(i);if(Or(e)){const t=a[i],n=l[e]=g(t)||y(t)?{type:t}:d({},t);if(n){const t=Pr(Boolean,n.type),o=Pr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||h(n,"default"))&&c.push(e)}}}const f=[l,c];return _(e)&&o.set(e,f),f}function Or(e){return"$"!==e[0]&&!E(e)}function $r(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Lr(e,t){return $r(e)===$r(t)}function Pr(e,t){return g(t)?t.findIndex((t=>Lr(t,e))):y(t)&&Lr(t,e)?0:-1}const Ar=e=>"_"===e[0]||"$stable"===e,Mr=e=>g(e)?e.map(fi):[fi(e)],Ir=(e,t,n)=>{if(t._n)return t;const o=zn(((...e)=>Mr(t(...e))),n);return o._c=!1,o},jr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Ar(r))continue;const n=e[r];if(y(n))t[r]=Ir(0,n,o);else if(null!=n){const e=Mr(n);t[r]=()=>e}}},Nr=(e,t)=>{const n=Mr(t);e.slots.default=()=>n};function Rr(e,t,n,o,r=!1){if(g(e))return void e.forEach(((e,i)=>Rr(e,t&&(g(t)?t[i]:t),n,o,r)));if(ko(o)&&!r)return;const s=4&o.shapeFlag?$i(o.component)||o.component.proxy:o.el,a=r?null:s,{i:l,r:c}=e,u=t&&t.r,d=l.refs===i?l.refs={}:l.refs,p=l.setupState;if(null!=u&&u!==c&&(b(u)?(d[u]=null,h(p,u)&&(p[u]=null)):cn(u)&&(u.value=null)),y(c))vn(c,l,12,[a,d]);else{const t=b(c),o=cn(c);if(t||o){const i=()=>{if(e.f){const n=t?h(p,c)?p[c]:d[c]:c.value;r?g(n)&&f(n,s):g(n)?n.includes(s)||n.push(s):t?(d[c]=[s],h(p,c)&&(p[c]=d[c])):(c.value=[s],e.k&&(d[e.k]=c.value))}else t?(d[c]=a,h(p,c)&&(p[c]=a)):o&&(c.value=a,e.k&&(d[e.k]=a))};a?(i.id=-1,Br(i,n)):i()}}}const Br=function(e,t){var n;t&&t.pendingBranch?g(e)?t.effects.push(...e):t.effects.push(e):(g(n=e)?kn.push(...n):Cn&&Cn.includes(n,n.allowRecurse?En+1:En)||kn.push(n),An())};function Fr(e){return function(e,t){D().__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:l,createElement:c,createText:u,createComment:f,setText:p,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=a,insertStaticContent:b}=e,w=(e,t,n,o=null,r=null,i=null,s,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!oi(e,t)&&(o=te(e),Z(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case zr:_(e,t,n,o);break;case Ur:S(e,t,n,o);break;case Xr:null==e&&T(t,n,o,s);break;case qr:F(e,t,n,o,r,i,s,a,l);break;default:1&d?O(e,t,n,o,r,i,s,a,l):6&d?H(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,re)}null!=u&&r&&Rr(u,e&&e.ref,i,t||e,!t)},_=(e,t,o,r)=>{if(null==e)n(t.el=u(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&p(n,t.children)}},S=(e,t,o,r)=>{null==e?n(t.el=f(t.children||""),o,r):t.el=e.el},T=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},C=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),o(e),e=n;o(t)},O=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?$(t,n,o,r,i,s,a,l):I(e,t,r,i,s,a,l)},$=(e,t,o,i,s,a,l,u)=>{let d,f;const{props:p,shapeFlag:h,transition:m,dirs:v}=e;if(d=e.el=c(e.type,a,p&&p.is,p),8&h?g(d,e.children):16&h&&M(e.children,d,null,i,s,Dr(e,a),l,u),v&&fo(e,null,i,"created"),P(d,e,e.scopeId,l,i),p){for(const t in p)"value"===t||E(t)||r(d,t,null,p[t],a,e.children,i,s,ee);"value"in p&&r(d,"value",null,p.value,a),(f=p.onVnodeBeforeMount)&&mi(f,i,e)}Object.defineProperty(d,"__vueParentComponent",{value:i,enumerable:!1}),v&&fo(e,null,i,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(s,m);y&&m.beforeEnter(d),n(d,t,o),((f=p&&p.onVnodeMounted)||y||v)&&Br((()=>{f&&mi(f,i,e),y&&m.enter(d),v&&fo(e,null,i,"mounted")}),s)},P=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;P(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},M=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?pi(e[c]):fi(e[c]);w(null,l,t,n,o,r,i,s,a)}},I=(e,t,n,o,s,a,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:p}=t;d|=16&e.patchFlag;const h=e.props||i,m=t.props||i;let v;if(n&&Hr(n,!1),(v=m.onVnodeBeforeUpdate)&&mi(v,n,t,e),p&&fo(t,e,n,"beforeUpdate"),n&&Hr(n,!0),f?j(e.dynamicChildren,f,u,n,o,Dr(t,s),a):c||U(e,t,u,null,n,o,Dr(t,s),a,!1),d>0){if(16&d)B(u,t,h,m,n,o,s);else if(2&d&&h.class!==m.class&&r(u,"class",null,m.class,s),4&d&&r(u,"style",h.style,m.style,s),8&d){const i=t.dynamicProps;for(let t=0;t<i.length;t++){const a=i[t],c=h[a],d=m[a];(d!==c||"value"===a||l&&l(u,a))&&r(u,a,c,d,s,e.children,n,o,ee)}}1&d&&e.children!==t.children&&g(u,t.children)}else c||null!=f||B(u,t,h,m,n,o,s);((v=m.onVnodeUpdated)||p)&&Br((()=>{v&&mi(v,n,t,e),p&&fo(t,e,n,"updated")}),o)},j=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===qr||!oi(l,c)||70&l.shapeFlag)?m(l.el):n;w(l,c,u,null,o,r,i,s,!0)}},B=(e,t,n,o,s,a,c)=>{if(n!==o){if(n!==i)for(const i in n)E(i)||i in o||r(e,i,n[i],null,c,t.children,s,a,ee);for(const i in o){if(E(i))continue;const u=o[i],d=n[i];(u!==d&&"value"!==i||l&&l(e,i))&&r(e,i,d,u,c,t.children,s,a,ee)}"value"in o&&r(e,"value",n.value,o.value,c)}},F=(e,t,o,r,i,s,a,l,c)=>{const d=t.el=e?e.el:u(""),f=t.anchor=e?e.anchor:u("");let{patchFlag:p,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(d,o,r),n(f,o,r),M(t.children||[],o,f,i,s,a,l,c)):p>0&&64&p&&h&&e.dynamicChildren?(j(e.dynamicChildren,h,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Vr(e,t,!0)):U(e,t,o,f,i,s,a,l,c)},H=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):V(t,n,o,r,i,s,l):W(e,t,l)},V=(e,t,n,o,r,s,a)=>{const l=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||vi,s={uid:yi++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new Ve(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Er(o,r),emitsOptions:Dn(o,r),emit:null,emitted:null,propsDefaults:i,inheritAttrs:o.inheritAttrs,ctx:i,data:i,props:i,attrs:i,slots:i,refs:i,setupState:i,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=Bn.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,o,r);if(Oo(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&xi(t);const{props:n,children:o}=e.vnode,r=ki(e);(function(e,t,n,o=!1){const r={},i={};R(i,ri,1),e.propsDefaults=Object.create(null),kr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Yt(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=tn(t),R(t,"_",n)):jr(t,e.slots={})}else e.slots={},t&&Nr(e,t);R(e.slots,ri,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=nn(new Proxy(e.ctx,ir));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(lt(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=Si(e);Je();const i=vn(o,e,0,[e.props,n]);if(Ke(),r(),x(i)){if(i.then(Ti,Ti),t)return i.then((n=>{Ei(e,n,t)})).catch((t=>{bn(t,e,0)}));e.asyncDep=i}else Ei(e,i,t)}else Oi(e,t)}(e,t):void 0;t&&xi(!1)}(l),l.asyncDep){if(r&&r.registerDep(l,q),!e.el){const e=l.subTree=li(Ur);S(null,e,t,n)}}else q(l,e,t,n,r,s,a)},W=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||Gn(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?Gn(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!Hn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void z(o,t,n);o.next=t,function(e){const t=Sn.indexOf(e);t>Tn&&Sn.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},q=(e,t,n,o,r,i,s)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:a,vnode:c}=e;{const n=Wr(e);if(n)return t&&(t.el=c.el,z(e,t,s)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,d=t;Hr(e,!1),t?(t.el=c.el,z(e,t,s)):t=c,n&&N(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&mi(u,a,t,c),Hr(e,!0);const f=Un(e),p=e.subTree;e.subTree=f,w(p,f,m(p.el),te(p),e,r,i),t.el=f.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,f.el),o&&Br(o,r),(u=t.props&&t.props.onVnodeUpdated)&&Br((()=>mi(u,a,t,c)),r)}else{let s;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,f=ko(t);if(Hr(e,!1),c&&N(c),!f&&(s=l&&l.onVnodeBeforeMount)&&mi(s,d,t),Hr(e,!0),a&&se){const n=()=>{e.subTree=Un(e),se(a,e.subTree,e,r,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const s=e.subTree=Un(e);w(null,s,n,o,e,r,i),t.el=s.el}if(u&&Br(u,r),!f&&(s=l&&l.onVnodeMounted)){const e=t;Br((()=>mi(s,d,e)),r)}(256&t.shapeFlag||d&&ko(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&Do(e.ba),e.a&&Br(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new qe(l,a,(()=>Pn(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Hr(e,!0),u()},z=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=tn(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;kr(e,t,r,i)&&(c=!0);for(const i in a)t&&(h(t,i)||(o=A(i))!==i&&h(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=Cr(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&h(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(Hn(e.emitsOptions,s))continue;const u=t[s];if(l)if(h(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=L(s);r[t]=Cr(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&ct(e,"set","$attrs")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:r}=e;let s=!0,a=i;if(32&o.shapeFlag){const e=t._;e?n&&1===e?s=!1:(d(r,t),n||1!==e||delete r._):(s=!t.$stable,jr(t,r)),a=t}else t&&(Nr(e,t),a={default:1});if(s)for(const i in r)Ar(i)||null!=a[i]||delete r[i]})(e,t.children,n),Je(),Mn(e),Ke()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(128&f)return void Y(c,d,n,o,r,i,s,a,l);if(256&f)return void X(c,d,n,o,r,i,s,a,l)}8&p?(16&u&&ee(c,r,i),d!==c&&g(n,d)):16&u?16&p?Y(c,d,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&u&&g(n,""),16&p&&M(d,n,o,r,i,s,a,l))},X=(e,t,n,o,r,i,a,l,c)=>{t=t||s;const u=(e=e||s).length,d=t.length,f=Math.min(u,d);let p;for(p=0;p<f;p++){const o=t[p]=c?pi(t[p]):fi(t[p]);w(e[p],o,n,null,r,i,a,l,c)}u>d?ee(e,r,i,!0,!1,f):M(t,n,o,r,i,a,l,c,f)},Y=(e,t,n,o,r,i,a,l,c)=>{let u=0;const d=t.length;let f=e.length-1,p=d-1;for(;u<=f&&u<=p;){const o=e[u],s=t[u]=c?pi(t[u]):fi(t[u]);if(!oi(o,s))break;w(o,s,n,null,r,i,a,l,c),u++}for(;u<=f&&u<=p;){const o=e[f],s=t[p]=c?pi(t[p]):fi(t[p]);if(!oi(o,s))break;w(o,s,n,null,r,i,a,l,c),f--,p--}if(u>f){if(u<=p){const e=p+1,s=e<d?t[e].el:o;for(;u<=p;)w(null,t[u]=c?pi(t[u]):fi(t[u]),n,s,r,i,a,l,c),u++}}else if(u>p)for(;u<=f;)Z(e[u],r,i,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=p;u++){const e=t[u]=c?pi(t[u]):fi(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const b=p-g+1;let _=!1,x=0;const S=new Array(b);for(u=0;u<b;u++)S[u]=0;for(u=h;u<=f;u++){const o=e[u];if(y>=b){Z(o,r,i,!0);continue}let s;if(null!=o.key)s=m.get(o.key);else for(v=g;v<=p;v++)if(0===S[v-g]&&oi(o,t[v])){s=v;break}void 0===s?Z(o,r,i,!0):(S[s-g]=u+1,s>=x?x=s:_=!0,w(o,t[s],n,null,r,i,a,l,c),y++)}const T=_?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):s;for(v=T.length-1,u=b-1;u>=0;u--){const e=g+u,s=t[e],f=e+1<d?t[e+1].el:o;0===S[u]?w(null,s,n,f,r,i,a,l,c):_&&(v<0||u!==T[v]?G(s,n,f,2):v--)}}},G=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void G(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,re);if(a===qr){n(s,t,o);for(let e=0;e<c.length;e++)G(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===Xr)return void k(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Br((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},Z=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=a&&Rr(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&f,h=!ko(e);let g;if(h&&(g=s&&s.onVnodeBeforeUnmount)&&mi(g,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);p&&fo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==qr||d>0&&64&d)?ee(c,t,n,!1,!0):(i===qr&&384&d||!r&&16&u)&&ee(l,t,n),o&&J(e)}(h&&(g=s&&s.onVnodeUnmounted)||p)&&Br((()=>{g&&mi(g,t,e),p&&fo(e,null,t,"unmounted")}),n)},J=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===qr)return void K(n,r);if(t===Xr)return void C(e);const s=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,s);o?o(e.el,s,r):r()}else s()},K=(e,t)=>{let n;for(;e!==t;)n=v(e),o(e),e=n;o(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&N(o),r.stop(),i&&(i.active=!1,Z(s,e,t,n)),a&&Br(a,t),Br((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)Z(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&Z(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,Mn(),In(),ne=!1),t._vnode=e},re={p:w,um:Z,m:G,r:J,mt:V,mc:M,pc:U,pbc:j,n:te,o:e};let ie,se;t&&([ie,se]=t(re));return{render:oe,hydrate:ie,createApp:_r(oe,ie)}}(e)}function Dr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Hr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Vr(e,t,n=!1){const o=e.children,r=t.children;if(g(o)&&g(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=pi(r[i]),t.el=e.el),n||Vr(e,t)),t.type===zr&&(t.el=e.el)}}function Wr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Wr(t)}const qr=Symbol.for("v-fgt"),zr=Symbol.for("v-txt"),Ur=Symbol.for("v-cmt"),Xr=Symbol.for("v-stc"),Yr=[];let Gr=null;function Zr(e=!1){Yr.push(Gr=e?null:[])}let Jr=1;function Kr(e){Jr+=e}function Qr(e){return e.dynamicChildren=Jr>0?Gr||s:null,Yr.pop(),Gr=Yr[Yr.length-1]||null,Jr>0&&Gr&&Gr.push(e),e}function ei(e,t,n,o,r,i){return Qr(ai(e,t,n,o,r,i,!0))}function ti(e,t,n,o,r){return Qr(li(e,t,n,o,r,!0))}function ni(e){return!!e&&!0===e.__v_isVNode}function oi(e,t){return e.type===t.type&&e.key===t.key}const ri="__vInternal",ii=({key:e})=>null!=e?e:null,si=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?b(e)||cn(e)||y(e)?{i:Vn,r:e,k:t,f:!!n}:e:null);function ai(e,t=null,n=null,o=0,r=null,i=(e===qr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ii(t),ref:t&&si(t),scopeId:Wn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Vn};return a?(hi(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=b(n)?8:16),Jr>0&&!s&&Gr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Gr.push(l),l}const li=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Jn||(e=Ur);if(ni(e)){const o=ci(e,t,!0);return n&&hi(o,n),Jr>0&&!i&&Gr&&(6&o.shapeFlag?Gr[Gr.indexOf(e)]=o:Gr.push(o)),o.patchFlag|=-2,o}s=e,y(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?en(e)||ri in e?d({},e):e:null}(t);let{class:e,style:n}=t;e&&!b(e)&&(t.class=ye(e)),_(n)&&(en(n)&&!g(n)&&(n=d({},n)),t.style=ve(n))}const a=b(e)?1:to(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:y(e)?2:0;return ai(e,t,n,o,r,a,i,!0)};function ci(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?gi(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&ii(a),ref:t&&t.ref?n&&r?g(r)?r.concat(si(t)):[r,si(t)]:si(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==qr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ci(e.ssContent),ssFallback:e.ssFallback&&ci(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function ui(e=" ",t=0){return li(zr,null,e,t)}function di(e="",t=!1){return t?(Zr(),ti(Ur,null,e)):li(Ur,null,e)}function fi(e){return null==e||"boolean"==typeof e?li(Ur):g(e)?li(qr,null,e.slice()):"object"==typeof e?pi(e):li(zr,null,String(e))}function pi(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ci(e)}function hi(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(g(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),hi(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||ri in t?3===o&&Vn&&(1===Vn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Vn}}else y(t)?(t={default:t,_ctx:Vn},n=32):(t=String(t),64&o?(n=16,t=[ui(t)]):n=8);e.children=t,e.shapeFlag|=n}function gi(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=ye([t.class,o.class]));else if("style"===e)t.style=ve([t.style,o.style]);else if(c(e)){const n=t[e],r=o[e];!r||n===r||g(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function mi(e,t,n,o=null){yn(e,t,7,[n,o])}const vi=br();let yi=0;let bi=null;const wi=()=>bi||Vn;let _i,xi;{const e=D(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};_i=t("__VUE_INSTANCE_SETTERS__",(e=>bi=e)),xi=t("__VUE_SSR_SETTERS__",(e=>Ci=e))}const Si=e=>{const t=bi;return _i(e),e.scope.on(),()=>{e.scope.off(),_i(t)}},Ti=()=>{bi&&bi.scope.off(),_i(null)};function ki(e){return 4&e.vnode.shapeFlag}let Ci=!1;function Ei(e,t,n){y(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=mn(t)),Oi(e,n)}function Oi(e,t,n){const o=e.type;e.render||(e.render=o.render||a);{const t=Si(e);Je();try{lr(e)}finally{Ke(),t()}}}function $i(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(mn(nn(e.exposed)),{get:(t,n)=>n in t?t[n]:n in or?or[n](e):void 0,has:(e,t)=>t in e||t in or}))}function Li(e,t=!0){return y(e)?e.displayName||e.name:e.name||t&&e.__name}const Pi=(e,t)=>{const n=function(e,t,n=!1){let o,r;const i=y(e);return i?(o=e,r=a):(o=e.get,r=e.set),new sn(o,r,i||!r,n)}(e,0,Ci);return n};function Ai(e,t,n){const o=arguments.length;return 2===o?_(t)&&!g(t)?ni(t)?li(e,null,[t]):li(e,t):li(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&ni(n)&&(n=[n]),li(e,t,n))}const Mi="3.4.21",Ii="undefined"!=typeof document?document:null,ji=Ii&&Ii.createElement("template"),Ni={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?Ii.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Ii.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Ii.createElement(e,{is:n}):Ii.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Ii.createTextNode(e),createComment:e=>Ii.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ii.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{ji.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=ji.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ri="transition",Bi=Symbol("_vtc"),Fi=(e,{slots:t})=>Ai(vo,function(e){const t={};for(const d in e)d in Di||(t[d]=e[d]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=s,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(_(e))return[Wi(e.enter),Wi(e.leave)];{const t=Wi(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:T=y,onAppear:k=b,onAppearCancelled:C=w}=t,E=(e,t,n)=>{zi(e,t?u:a),zi(e,t?c:s),n&&n()},O=(e,t)=>{e._isLeaving=!1,zi(e,f),zi(e,h),zi(e,p),t&&t()},$=e=>(t,n)=>{const r=e?k:b,s=()=>E(t,e,n);Hi(r,[t,s]),Ui((()=>{zi(t,e?l:i),qi(t,e?u:a),Vi(r)||Yi(t,o,m,s)}))};return d(t,{onBeforeEnter(e){Hi(y,[e]),qi(e,i),qi(e,s)},onBeforeAppear(e){Hi(T,[e]),qi(e,l),qi(e,c)},onEnter:$(!1),onAppear:$(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>O(e,t);qi(e,f),document.body.offsetHeight,qi(e,p),Ui((()=>{e._isLeaving&&(zi(e,f),qi(e,h),Vi(x)||Yi(e,o,v,n))})),Hi(x,[e,n])},onEnterCancelled(e){E(e,!1),Hi(w,[e])},onAppearCancelled(e){E(e,!0),Hi(C,[e])},onLeaveCancelled(e){O(e),Hi(S,[e])}})}(e),t);Fi.displayName="Transition";const Di={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Fi.props=d({},mo,Di);const Hi=(e,t=[])=>{g(e)?e.forEach((e=>e(...t))):e&&e(...t)},Vi=e=>!!e&&(g(e)?e.some((e=>e.length>1)):e.length>1);function Wi(e){const t=(e=>{const t=b(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function qi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Bi]||(e[Bi]=new Set)).add(t)}function zi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Bi];n&&(n.delete(t),n.size||(e[Bi]=void 0))}function Ui(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Xi=0;function Yi(e,t,n,o){const r=e._endId=++Xi,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=Gi(r,i),a=o("animationDelay"),l=o("animationDuration"),c=Gi(a,l);let u=null,d=0,f=0;t===Ri?s>0&&(u=Ri,d=s,f=i.length):"animation"===t?c>0&&(u="animation",d=c,f=l.length):(d=Math.max(s,c),u=d>0?s>c?Ri:"animation":null,f=u?u===Ri?i.length:l.length:0);const p=u===Ri&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:f,hasTransform:p}}(e,t);if(!s)return o();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),a+1),e.addEventListener(c,f)}function Gi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Zi(t)+Zi(e[n]))))}function Zi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const Ji=Symbol("_vod"),Ki=Symbol("_vsh"),Qi={beforeMount(e,{value:t},{transition:n}){e[Ji]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):es(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),es(e,!0),o.enter(e)):o.leave(e,(()=>{es(e,!1)})):es(e,t))},beforeUnmount(e,{value:t}){es(e,t)}};function es(e,t){e.style.display=t?e[Ji]:"none",e[Ki]=!t}const ts=Symbol(""),ns=/(^|;)\s*display\s*:/;const os=/\s*!important$/;function rs(e,t,n){if(g(n))n.forEach((n=>rs(e,t,n)));else if(null==n&&(n=""),n=hs(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=ss[t];if(n)return n;let o=L(t);if("filter"!==o&&o in e)return ss[t]=o;o=M(o);for(let r=0;r<is.length;r++){const n=is[r]+o;if(n in e)return ss[t]=n}return t}(e,t);os.test(n)?e.setProperty(A(o),n.replace(os,""),"important"):e[o]=n}}const is=["Webkit","Moz","ms"],ss={};const{unit:as,unitRatio:ls,unitPrecision:cs}={unit:"rem",unitRatio:10/320,unitPrecision:5},us=(ds=as,fs=ls,ps=cs,e=>e.replace(xe,((e,t)=>{if(!t)return e;if(1===fs)return`${t}${ds}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*fs,ps);return 0===n?"0":`${n}${ds}`})));var ds,fs,ps;const hs=e=>b(e)?us(e):e,gs="http://www.w3.org/1999/xlink";const ms=Symbol("_vei");function vs(e,t,n,o,r=null){const i=e[ms]||(e[ms]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(ys.test(e)){let n;for(t={};n=e.match(ys);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):A(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&g(i)){const n=_s(e,i);for(let o=0;o<n.length;o++){const i=n[o];yn(i,t,5,i.__wwe?[e]:r(e))}}else yn(_s(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>bs||(ws.then((()=>bs=0)),bs=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const ys=/(?:Once|Passive|Capture)$/;let bs=0;const ws=Promise.resolve();function _s(e,t){if(g(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const xs=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const Ss=["ctrl","shift","alt","meta"],Ts={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Ss.some((n=>e[`${n}Key`]&&!t.includes(n)))},ks=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=Ts[t[e]];if(o&&o(n,t))return}return e(n,...o)})},Cs=d({patchProp:(e,t,n,o,r,i,s,a,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;Ln((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[Bi];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=b(n);let i=!1;if(n&&!r){if(t)if(b(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&rs(o,t,"")}else for(const e in t)null==n[e]&&rs(o,e,"");for(const e in n)"display"===e&&(i=!0),rs(o,e,n[e])}else if(r){if(t!==n){const e=o[ts];e&&(n+=";"+e),o.cssText=n,i=ns.test(n)}}else t&&e.removeAttribute("style");Ji in e&&(e[Ji]=i?o.display:"",e[Ki]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)rs(o,a,s[a])}(e,n,o):c(t)?u(t)||vs(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&xs(t)&&y(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(xs(t)&&b(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=Y(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,a,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(gs,t.slice(6,t.length)):e.setAttributeNS(gs,t,n);else{const o=X(t);null==n||o&&!Y(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Ni);let Es;const Os=(...e)=>{const t=(Es||(Es=Fr(Cs))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(b(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;y(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const $s="undefined"!=typeof document;const Ls=Object.assign;function Ps(e,t){const n={};for(const o in t){const r=t[o];n[o]=Ms(r)?r.map(e):e(r)}return n}const As=()=>{},Ms=Array.isArray,Is=/#/g,js=/&/g,Ns=/\//g,Rs=/=/g,Bs=/\?/g,Fs=/\+/g,Ds=/%5B/g,Hs=/%5D/g,Vs=/%5E/g,Ws=/%60/g,qs=/%7B/g,zs=/%7C/g,Us=/%7D/g,Xs=/%20/g;function Ys(e){return encodeURI(""+e).replace(zs,"|").replace(Ds,"[").replace(Hs,"]")}function Gs(e){return Ys(e).replace(Fs,"%2B").replace(Xs,"+").replace(Is,"%23").replace(js,"%26").replace(Ws,"`").replace(qs,"{").replace(Us,"}").replace(Vs,"^")}function Zs(e){return null==e?"":function(e){return Ys(e).replace(Is,"%23").replace(Bs,"%3F")}(e).replace(Ns,"%2F")}function Js(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Ks=/\/$/;function Qs(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:Js(s)}}function ea(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function ta(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function na(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!oa(e[n],t[n]))return!1;return!0}function oa(e,t){return Ms(e)?ra(e,t):Ms(t)?ra(t,e):e===t}function ra(e,t){return Ms(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var ia,sa,aa,la;function ca(e){if(!e)if($s){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Ks,"")}(sa=ia||(ia={})).pop="pop",sa.push="push",(la=aa||(aa={})).back="back",la.forward="forward",la.unknown="";const ua=/^[^#]+#/;function da(e,t){return e.replace(ua,"#")+t}const fa=()=>({left:window.scrollX,top:window.scrollY});function pa(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function ha(e,t){return(history.state?history.state.position-t:-1)+e}const ga=new Map;function ma(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),ea(n,"")}return ea(n,e)+o+r}function va(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?fa():null}}function ya(e){const{history:t,location:n}=window,o={value:ma(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=Ls({},r.value,t.state,{forward:e,scroll:fa()});i(s.current,s,!0),i(e,Ls({},va(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Ls({},t.state,va(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function ba(e){const t=ya(e=ca(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=ma(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:ia.pop,direction:u?u>0?aa.forward:aa.back:aa.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Ls({},e.state,{scroll:fa()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Ls({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:da.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function wa(e){return"string"==typeof e||"symbol"==typeof e}const _a={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},xa=Symbol("");var Sa,Ta;function ka(e,t){return Ls(new Error,{type:e,[xa]:!0},t)}function Ca(e,t){return e instanceof Error&&xa in e&&(null==t||!!(e.type&t))}(Ta=Sa||(Sa={}))[Ta.aborted=4]="aborted",Ta[Ta.cancelled=8]="cancelled",Ta[Ta.duplicated=16]="duplicated";const Ea={sensitive:!1,strict:!1,start:!0,end:!0},Oa=/[.+*?^${}()[\]/\\]/g;function $a(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function La(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=$a(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(Pa(o))return 1;if(Pa(r))return-1}return r.length-o.length}function Pa(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Aa={type:0,value:""},Ma=/[a-zA-Z0-9_]/;function Ia(e,t,n){const o=function(e,t){const n=Ls({},Ea,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(Oa,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){s+=10;try{new RegExp(`(${d})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+a.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=c&&l.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),r+=f,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===d&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(Ms(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Ms(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Aa]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function f(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&d(),s()):":"===a?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===a?n=2:Ma.test(a)?f():(d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),s(),r}(e.path),n),r=Ls(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function ja(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Ra(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Da(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Ls({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,f;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=Ia(t,n,c),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),a&&e.name&&!Ba(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&s(d)}return f?()=>{i(f)}:As}function i(e){if(wa(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){let t=0;for(;t<n.length&&La(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Ha(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Ba(e)&&o.set(e.record.name,e)}return t=Da({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw ka(1,{location:e});s=r.record.name,a=Ls(Na(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&Na(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw ka(1,{location:e,currentLocation:t});s=r.record.name,a=Ls({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Fa(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Na(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Ra(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Ba(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Fa(e){return e.reduce(((e,t)=>Ls(e,t.meta)),{})}function Da(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Ha(e,t){return t.children.some((t=>t===e||Ha(e,t)))}function Va(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Fs," "),r=e.indexOf("="),i=Js(r<0?e:e.slice(0,r)),s=r<0?null:Js(e.slice(r+1));if(i in t){let e=t[i];Ms(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Wa(e){let t="";for(let n in e){const o=e[n];if(n=Gs(n).replace(Rs,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ms(o)?o.map((e=>e&&Gs(e))):[o&&Gs(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function qa(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ms(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const za=Symbol(""),Ua=Symbol(""),Xa=Symbol(""),Ya=Symbol(""),Ga=Symbol("");function Za(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Ja(e,t,n,o,r,i=(e=>e())){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(ka(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(ka(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch((e=>l(e)))}))}function Ka(e,t,n,o,r=(e=>e())){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(Ja(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Ja(c,n,o,a,e,r)()}))))}}var s;return i}function Qa(e){const t=Tr(Xa),n=Tr(Ya),o=Pi((()=>t.resolve(hn(e.to)))),r=Pi((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(ta.bind(null,r));if(s>-1)return s;const a=tl(e[t-2]);return t>1&&tl(r)===a&&i[i.length-1].path!==a?i.findIndex(ta.bind(null,e[t-2])):s})),i=Pi((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!Ms(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=Pi((()=>r.value>-1&&r.value===n.matched.length-1&&na(n.params,o.value.params)));return{route:o,href:Pi((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[hn(e.replace)?"replace":"push"](hn(e.to)).catch(As):Promise.resolve()}}}const el=To({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Qa,setup(e,{slots:t}){const n=Xt(Qa(e)),{options:o}=Tr(Xa),r=Pi((()=>({[nl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[nl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Ai("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function tl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const nl=(e,t,n)=>null!=e?e:null!=t?t:n;function ol(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const rl=To({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=Tr(Ga),r=Pi((()=>e.route||o.value)),i=Tr(Ua,0),s=Pi((()=>{let e=hn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=Pi((()=>r.value.matched[s.value]));Sr(Ua,Pi((()=>s.value+1))),Sr(za,a),Sr(Ga,r);const l=un();return io((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&ta(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return ol(n.default,{Component:c,route:o});const u=s.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,f=Ai(c,Ls({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return ol(n.default,{Component:f,route:o})||f}}});function il(e){const t=ja(e.routes,e),n=e.parseQuery||Va,o=e.stringifyQuery||Wa,r=e.history,i=Za(),s=Za(),a=Za(),l=dn(_a);let c=_a;$s&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Ps.bind(null,(e=>""+e)),d=Ps.bind(null,Zs),f=Ps.bind(null,Js);function p(e,i){if(i=Ls({},i||l.value),"string"==typeof e){const o=Qs(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return Ls(o,s,{params:f(s.params),hash:Js(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=Ls({},e,{path:Qs(n,e.path,i.path).path});else{const t=Ls({},e.params);for(const e in t)null==t[e]&&delete t[e];s=Ls({},e,{params:d(t)}),i.params=d(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(f(a.params));const p=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Ls({},e,{hash:(h=c,Ys(h).replace(qs,"{").replace(Us,"}").replace(Vs,"^")),path:a.path}));var h;const g=r.createHref(p);return Ls({fullPath:p,hash:c,query:o===Wa?qa(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Qs(n,e,l.value.path):Ls({},e)}function g(e,t){if(c!==e)return ka(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),Ls({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=p(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(Ls(h(u),{state:"object"==typeof u?Ls({},i,u.state):i,force:s,replace:a}),t||n);const d=n;let f;return d.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&ta(t.matched[o],n.matched[r])&&na(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(f=ka(16,{to:d,from:r}),P(r,r,!0,!1)),(f?Promise.resolve(f):_(d,r)).catch((e=>Ca(e)?Ca(e,2)?e:L(e):$(e,d,r))).then((e=>{if(e){if(Ca(e,2))return y(Ls({replace:a},h(e.to),{state:"object"==typeof e.to?Ls({},i,e.to.state):i,force:s}),t||d)}else e=S(d,r,!0,a,i);return x(d,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function w(e){const t=I.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function _(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>ta(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>ta(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=Ka(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Ja(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),N(n).then((()=>{n=[];for(const o of i.list())n.push(Ja(o,e,t));return n.push(l),N(n)})).then((()=>{n=Ka(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Ja(o,e,t))}));return n.push(l),N(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if(Ms(o.beforeEnter))for(const r of o.beforeEnter)n.push(Ja(r,e,t));else n.push(Ja(o.beforeEnter,e,t));return n.push(l),N(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ka(a,"beforeRouteEnter",e,t,w),n.push(l),N(n)))).then((()=>{n=[];for(const o of s.list())n.push(Ja(o,e,t));return n.push(l),N(n)})).catch((e=>Ca(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>w((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=g(e,t);if(s)return s;const a=t===_a,c=$s?history.state:{};n&&(o||a?r.replace(e.fullPath,Ls({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,P(e,t,n,a),L()}let T;function k(){T||(T=r.listen(((e,t,n)=>{if(!j.listening)return;const o=p(e),i=v(o);if(i)return void y(Ls(i,{replace:!0}),o).catch(As);c=o;const s=l.value;var a,u;$s&&(a=ha(s.fullPath,n.delta),u=fa(),ga.set(a,u)),_(o,s).catch((e=>Ca(e,12)?e:Ca(e,2)?(y(e.to,o).then((e=>{Ca(e,20)&&!n.delta&&n.type===ia.pop&&r.go(-1,!1)})).catch(As),Promise.reject()):(n.delta&&r.go(-n.delta,!1),$(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!Ca(e,8)?r.go(-n.delta,!1):n.type===ia.pop&&Ca(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(As)})))}let C,E=Za(),O=Za();function $(e,t,n){L(e);const o=O.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function L(e){return C||(C=!e,k(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function P(t,n,o,r){const{scrollBehavior:i}=e;if(!$s||!i)return Promise.resolve();const s=!o&&function(e){const t=ga.get(e);return ga.delete(e),t}(ha(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return Ln().then((()=>i(t,n,s))).then((e=>e&&pa(e))).catch((e=>$(e,t,n)))}const A=e=>r.go(e);let M;const I=new Set,j={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return wa(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:p,options:e,push:m,replace:function(e){return m(Ls(h(e),{replace:!0}))},go:A,back:()=>A(-1),forward:()=>A(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:O.add,isReady:function(){return C&&l.value!==_a?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",el),e.component("RouterView",rl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>hn(l)}),$s&&!M&&l.value===_a&&(M=!0,m(r.location).catch((e=>{})));const t={};for(const o in _a)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Xa,this),e.provide(Ya,Yt(t)),e.provide(Ga,l);const n=e.unmount;I.add(e),e.unmount=function(){I.delete(e),I.size<1&&(c=_a,T&&T(),T=null,l.value=_a,M=!1,C=!1),n()}}};function N(e){return e.reduce(((e,t)=>e.then((()=>w(t)))),Promise.resolve())}return j}function sl(){return Tr(Ya)}const al=["{","}"];const ll=/^(?:\d)+/,cl=/^(?:\w)+/;const ul=Object.prototype.hasOwnProperty,dl=(e,t)=>ul.call(e,t),fl=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=al){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=ll.test(t)?"list":a&&cl.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function pl(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class hl{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||fl,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=pl(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{dl(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=pl(t,this.messages))&&(o=this.messages[t]):n=t,dl(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function gl(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Ed?Ed():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new hl({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=kh().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}const ml=ue((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let vl;function yl(){if(!vl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,vl=gl(e),ml()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>vl.add(e,__uniConfig.locales[e]))),vl.setLocale(e)}}return vl}function bl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const wl=ue((()=>{const e="uni.async.",t=["error"];yl().add("en",bl(e,t,["The connection timed out, click the screen to try again."]),!1),yl().add("es",bl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),yl().add("fr",bl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),yl().add("zh-Hans",bl(e,t,["连接服务器超时，点击屏幕重试"]),!1),yl().add("zh-Hant",bl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),_l=ue((()=>{const e="uni.showToast.",t=["unpaired"];yl().add("en",bl(e,t,["Please note showToast must be paired with hideToast"]),!1),yl().add("es",bl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),yl().add("fr",bl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),yl().add("zh-Hans",bl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),yl().add("zh-Hant",bl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),xl=ue((()=>{const e="uni.showLoading.",t=["unpaired"];yl().add("en",bl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),yl().add("es",bl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),yl().add("fr",bl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),yl().add("zh-Hans",bl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),yl().add("zh-Hant",bl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),Sl=ue((()=>{const e="uni.chooseFile.",t=["notUserActivation"];yl().add("en",bl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),yl().add("es",bl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),yl().add("fr",bl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),yl().add("zh-Hans",bl(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),yl().add("zh-Hant",bl(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),Tl=ue((()=>{const e="uni.video.",t=["danmu","volume"];yl().add("en",bl(e,t,["Danmu","Volume"]),!1),yl().add("es",bl(e,t,["Danmu","Volumen"]),!1),yl().add("fr",bl(e,t,["Danmu","Le Volume"]),!1),yl().add("zh-Hans",bl(e,t,["弹幕","音量"]),!1),yl().add("zh-Hant",bl(e,t,["彈幕","音量"]),!1)}));function kl(e){const t=new Ne;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Cl=1;const El=Object.create(null);function Ol(e,t){return e+"."+t}function $l(e,t,n){t=Ol(e,t),El[t]||(El[t]=n)}function Ll({id:e,name:t,args:n},o){t=Ol(o,t);const r=t=>{e&&Xg.publishHandler("invokeViewApi."+e,t)},i=El[t];i?i(n,r):r({})}const Pl=d(kl("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Xg,i=n?Cl++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Al=Se(!0);let Ml;function Il(){Ml&&(clearTimeout(Ml),Ml=null)}let jl=0,Nl=0;function Rl(e){if(Il(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];jl=t,Nl=n,Ml=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Bl(e){if(!Ml)return;if(1!==e.touches.length)return Il();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-jl)>10||Math.abs(n-Nl)>10?Il():void 0}function Fl(e,t){const n=Number(e);return isNaN(n)?t:n}function Dl(){const e=__uniConfig.globalStyle||{},t=Fl(e.rpxCalcMaxDeviceWidth,960),n=Fl(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Hl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Vl,Wl,ql=["top","left","right","bottom"],zl={};function Ul(){return Wl="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Xl(){if(Wl="string"==typeof Wl?Wl:Ul()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),ql.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Vl=!0}else ql.forEach((function(e){zl[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Wl+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Gl.length||setTimeout((function(){var e={};Gl.forEach((function(t){e[t]=zl[t]})),Gl.length=0,Zl.forEach((function(t){t(e)}))}),0);Gl.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(zl,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Yl(e){return Vl||Xl(),zl[e]}var Gl=[];var Zl=[];const Jl=Hl({get support(){return 0!=("string"==typeof Wl?Wl:Ul()).length},get top(){return Yl("top")},get left(){return Yl("left")},get right(){return Yl("right")},get bottom(){return Yl("bottom")},onChange:function(e){Ul()&&(Vl||Xl(),"function"==typeof e&&Zl.push(e))},offChange:function(e){var t=Zl.indexOf(e);t>=0&&Zl.splice(t,1)}}),Kl=ks((()=>{}),["prevent"]);function Ql(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function ec(){const e=Ql(document.documentElement.style,"--window-top");return e?e+Jl.top:0}function tc(){const e=document.documentElement.style,t=ec(),n=Ql(e,"--window-bottom"),o=Ql(e,"--window-left"),r=Ql(e,"--window-right"),i=Ql(e,"--top-window-height");return{top:t,bottom:n?n+Jl.bottom:0,left:o?o+Jl.left:0,right:r?r+Jl.right:0,topWindowHeight:i||0}}function nc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function oc(e){return Symbol(e)}function rc(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function ic(e,t=!1){if(t)return function(e){if(!rc(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>td(parseFloat(t))+"px"))}(e);if(b(e)){const t=parseInt(e)||0;return rc(e)?td(t):t}return e}function sc(e){return e.$page}function ac(e){return 0===e.tagName.indexOf("UNI-")}const lc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",cc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function uc(e,t="#000",n=27){return li("svg",{width:n,height:n,viewBox:"0 0 32 32"},[li("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function dc(){{const{$pageInstance:e}=wi();return e&&bc(e.proxy)}}function fc(){const e=kf(),t=e.length;if(t)return e[t-1]}function pc(){var e;const t=null==(e=fc())?void 0:e.$page;if(t)return t.meta}function hc(){const e=pc();return e?e.id:-1}function gc(){const e=fc();if(e)return e.$vm}const mc=["navigationBar","pullToRefresh"];function vc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=d({id:t},n,e);mc.forEach((t=>{o[t]=d({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function yc(e,t,n,o,r,i){const{id:s,route:a}=o,l=Fe(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:ce(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}function bc(e){var t,n;return(null==(t=e.$page)?void 0:t.id)||(null==(n=e.$basePage)?void 0:n.id)}function wc(e,t,n){if(b(e))n=t,t=e,e=gc();else if("number"==typeof e){const t=kf().find((t=>sc(t).id===e));e=t?t.$vm:gc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function _c(e){e.preventDefault()}let xc,Sc=0;function Tc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-Sc)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(Sc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(xc=setTimeout(s,300))),o=!1};return function(){clearTimeout(xc),o||requestAnimationFrame(s),o=!0}}function kc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return kc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),ce(i.concat(n).join("/"))}function Cc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}function Ec(){Dl(),we(ac),window.addEventListener("touchstart",Rl,Al),window.addEventListener("touchmove",Bl,Al),window.addEventListener("touchend",Il,Al),window.addEventListener("touchcancel",Il,Al)}class Oc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(me(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&me(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Ac(this.$el.querySelector(e));return t?$c(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Ac(n[o]);e&&t.push($c(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||b(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:A(n);(b(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(b(e)&&(e=z(e)),k(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];y(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Xg.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function $c(e,t=!0){if(t&&e&&(e=ge(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Oc(e)),e.$el.__wxsComponentDescriptor}function Lc(e,t){return $c(e,t)}function Pc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Lc(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=ge(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Lc(r,!1)]}}function Ac(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Mc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=Te(t?r:function(e){for(;!ac(e);)e=e.parentElement;return e}(r)),a=Te(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e instanceof CustomEvent&&k(e.detail)&&(l.detail=e.detail),e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){d(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Ic(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function jc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const Nc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!ac(o);if(r)return Pc(e,t,n,!1)||[e];const i=Mc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=ec();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Ic(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=ec();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Ic(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=ec();i.touches=jc(e.touches,t),i.changedTouches=jc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Pc(i,t,n)||[i]},createNativeEvent:Mc},Symbol.toStringTag,{value:"Module"});function Rc(e){!function(e){const t=e.globalProperties;d(t,Nc),t.$gcd=Lc}(e._context.config)}let Bc=1;function Fc(e){return(e||hc())+".invokeViewApi"}const Dc=d(kl("view"),{invokeOnCallback:(e,t)=>Yg.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Yg,s=o?Bc++:0;o&&r("invokeViewApi."+s,o,!0),i(Fc(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Yg,a=Bc++,l="invokeViewApi."+a;return r(l,n),s(Fc(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Hc(e){wc(fc(),"onResize",e),Yg.invokeOnCallback("onWindowResize",e)}function Vc(e){const t=fc();wc(kh(),"onShow",e),wc(t,"onShow")}function Wc(){wc(kh(),"onHide"),wc(fc(),"onHide")}const qc=["onPageScroll","onReachBottom"];function zc(){qc.forEach((e=>Yg.subscribe(e,function(e){return(t,n)=>{wc(parseInt(n),e,t)}}(e))))}function Uc(){!function(){const{on:e}=Yg;e("onResize",Hc),e("onAppEnterForeground",Vc),e("onAppEnterBackground",Wc)}(),zc()}function Xc(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Le(this.$page.id)),e.eventChannel}}function Yc(e){e._context.config.globalProperties.getOpenerEventChannel=Xc}function Gc(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function Zc(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${td(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function Jc(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(Zc)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?Zc(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const Kc={props:["animation"],watch:{animation:{deep:!0,handler(){Jc(this)}}},mounted(){Jc(this)}},Qc=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Kc),eu(e)},eu=e=>(e.__reserved=!0,e.compatConfig={MODE:3},To(e));function tu(e){return e.__wwe=!0,e}function nu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=Te(n),{type:t.__evName||o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const ou={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function ru(e){const t=un(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:tu((function(e){e.touches.length>1||s(e)})),onMousedown:tu((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:tu((function(){a()})),onMouseup:tu((function(){r&&l()})),onTouchcancel:tu((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function iu(e,t){return b(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const su=oc("uf"),au=oc("ul");function lu(e,t,n){const o=dc();n&&!e||k(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Xg.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Xg.on(r,t[r]):e&&Xg.on(`uni-${r}-${o}-${e}`,t[r])}))}function cu(e,t,n){const o=dc();n&&!e||k(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Xg.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Xg.off(r,t[r]):e&&Xg.off(`uni-${r}-${o}-${e}`,t[r])}))}const uu=Qc({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=un(null),o=Tr(su,!1),{hovering:r,binding:i}=ru(e),s=tu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=Tr(au,!1);return a&&(a.addHandler(s),Xo((()=>{a.removeHandler(s)}))),function(e,t){lu(e.id,t),io((()=>e.id),((e,n)=>{cu(n,t,!0),lu(e,t,!0)})),Yo((()=>{cu(e.id,t)}))}(e,{"label-click":s}),()=>{const o=e.hoverClass,a=iu(e,"disabled"),l=iu(e,"loading"),c=iu(e,"plain"),u=o&&"none"!==o;return li("uni-button",gi({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}}),du=oc("upm");function fu(){return Tr(du)}function pu(e){const t=function(e){return Xt(function(e){if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==kf().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(vc(sl().meta,e)))))}(e);return Sr(du,t),t}function hu(){return sl()}function gu(){return history.state&&history.state.__id__||1}const mu=["original","compressed"],vu=["album","camera"],yu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function bu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function wu(e,t){return!g(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function _u(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let xu=1;const Su={};function Tu(e,t,n){if("number"==typeof e){const o=Su[e];if(o)return o.keepAlive||delete Su[e],o.callback(t,n)}return t}const ku="success",Cu="fail",Eu="complete";function Ou(e,t={},{beforeAll:n,beforeSuccess:o}={}){k(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];y(o)&&(t[n]=_u(o),delete e[n])}return t}(t),a=y(r),l=y(i),c=y(s),u=xu++;return function(e,t,n,o=!1){Su[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),y(n)&&n(u),u.errMsg===e+":ok"?(y(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const $u="success",Lu="fail",Pu="complete",Au={},Mu={};function Iu(e,t){return function(n){return e(n,t)||n}}function ju(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Iu(i,n));else{const e=i(t,n);if(x(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Nu(e,t={}){return[$u,Lu,Pu].forEach((n=>{const o=e[n];if(!g(o))return;const r=t[n];t[n]=function(e){ju(o,e,t).then((e=>y(r)&&r(e)||e))}})),t}function Ru(e,t){const n=[];g(Au.returnValue)&&n.push(...Au.returnValue);const o=Mu[e];return o&&g(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Bu(e){const t=Object.create(null);Object.keys(Au).forEach((e=>{"returnValue"!==e&&(t[e]=Au[e].slice())}));const n=Mu[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Fu(e,t,n,o){const r=Bu(e);if(r&&Object.keys(r).length){if(g(r.invoke)){return ju(r.invoke,n).then((n=>t(Nu(Bu(e),n),...o)))}return t(Nu(r,n),...o)}return t(n,...o)}function Du(e,t){return(n={},...o)=>function(e){return!(!k(e)||![ku,Cu,Eu].find((t=>y(e[t]))))}(n)?Ru(e,Fu(e,t,n,o)):Ru(e,new Promise(((r,i)=>{Fu(e,t,d(n,{success:r,fail:i}),o)})))}function Hu(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,Tu(e,d({errMsg:i},o))}function Vu(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(b(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!k(t.formatArgs)&&k(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(y(s)){const o=s(e[0][t],n);if(b(o))return o}else h(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Wu(e,t,n,o){return n=>{const r=Ou(e,n,o),i=Vu(0,[n],0,o);return i?Hu(r,e,i):t(n,{resolve:t=>function(e,t,n){return Tu(e,d(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Hu(r,e,function(e){return!e||b(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function qu(e,t,n,o){return Du(e,Wu(e,t,0,o))}function zu(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Vu(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Uu(e,t,n,o){return Du(e,function(e,t,n,o){return Wu(e,t,0,o)}(e,t,0,o))}let Xu=!1,Yu=0,Gu=0,Zu=960,Ju=375,Ku=750;function Qu(){let e,t,n;{const{windowWidth:o,pixelRatio:r,platform:i}=function(){const e=Yf(),t=Jf(Zf(e,Gf(e)));return{platform:Wf?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();e=o,t=r,n=i}Yu=e,Gu=t,Xu="ios"===n}function ed(e,t){const n=Number(e);return isNaN(n)?t:n}const td=zu(0,((e,t)=>{if(0===Yu&&(Qu(),function(){const e=__uniConfig.globalStyle||{};Zu=ed(e.rpxCalcMaxDeviceWidth,960),Ju=ed(e.rpxCalcBaseDeviceWidth,375),Ku=ed(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Yu;n=e===Ku||n<=Zu?n:Ju;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Gu&&Xu?.5:1),e<0?-o:o}));const nd=new class{constructor(){this.$emitter=new Ne}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}},od=zu(0,((e,t)=>(nd.on(e,t),()=>nd.off(e,t)))),rd=zu(0,((e,t)=>{g(e)||(e=e?[e]:[]),e.forEach((e=>{nd.off(e,t)}))})),id=zu(0,((e,...t)=>{nd.emit(e,...t)})),sd=[.5,.8,1,1.25,1.5,2];const ad=(e,t,n,o)=>{!function(e,t,n,o,r){Yg.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};const ld={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function cd(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(h(ld,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(ld[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class ud{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,cd(t)])}}class dd{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class fd{constructor(e){this.width=e}}const pd=["onCanplay","onPlay","onPause","onStop","onEnded","onTimeUpdate","onError","onWaiting","onSeeking","onSeeked"],hd=["offCanplay","offPlay","offPause","offStop","offEnded","offTimeUpdate","offError","offWaiting","offSeeking","offSeeked"];let gd=0,md={};const vd={canvas:class{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){console.log("initCanvasContextProperty implemented.")}setStrokeStyle(e){console.log("initCanvasContextProperty implemented.")}setShadow(e,t,n,o){console.log("initCanvasContextProperty implemented.")}addColorStop(e,t){console.log("initCanvasContextProperty implemented.")}setLineWidth(e){console.log("initCanvasContextProperty implemented.")}setLineCap(e){console.log("initCanvasContextProperty implemented.")}setLineJoin(e){console.log("initCanvasContextProperty implemented.")}setLineDash(e,t){console.log("initCanvasContextProperty implemented.")}setMiterLimit(e){console.log("initCanvasContextProperty implemented.")}fillRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}strokeRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}clearRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}fill(){console.log("initCanvasContextProperty implemented.")}stroke(){console.log("initCanvasContextProperty implemented.")}scale(e,t){console.log("initCanvasContextProperty implemented.")}rotate(e){console.log("initCanvasContextProperty implemented.")}translate(e,t){console.log("initCanvasContextProperty implemented.")}setFontSize(e){console.log("initCanvasContextProperty implemented.")}fillText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTextAlign(e){console.log("initCanvasContextProperty implemented.")}setTextBaseline(e){console.log("initCanvasContextProperty implemented.")}drawImage(e,t,n,o,r,i,s,a,l){console.log("initCanvasContextProperty implemented.")}setGlobalAlpha(e){console.log("initCanvasContextProperty implemented.")}strokeText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTransform(e,t,n,o,r,i){console.log("initCanvasContextProperty implemented.")}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],function(e,t,n,o,r){Yg.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new ud("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new ud("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new dd(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new fd(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+\.?\d*r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal","lighter","bolder"].indexOf(e)>-1||/^\d+$/.test(e)?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}},map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){ad(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){ad(this.id,this.pageId,"moveToLocation",e)}getScale(e){ad(this.id,this.pageId,"getScale",e)}getRegion(e){ad(this.id,this.pageId,"getRegion",e)}includePoints(e){ad(this.id,this.pageId,"includePoints",e)}translateMarker(e){ad(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){ad(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){ad(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){ad(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){ad(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){ad(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){ad(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){ad(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){ad(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){ad(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){ad(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){ad(this.id,this.pageId,"openMapApp",e)}on(e,t){ad(this.id,this.pageId,"on",{name:e,callback:t})}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){Kf(this.id,this.pageId,"play")}pause(){Kf(this.id,this.pageId,"pause")}stop(){Kf(this.id,this.pageId,"stop")}seek(e){Kf(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){Kf(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~sd.indexOf(e)||(e=1),Kf(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){Kf(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){Kf(this.id,this.pageId,"exitFullScreen")}showStatusBar(){Kf(this.id,this.pageId,"showStatusBar")}hideStatusBar(){Kf(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){!function(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(gd++);r.callbackId=e,md[e]=o}Yg.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(de(md[e],t),delete md[e])}))}(this.id,this.pageId,e,t)}}};function yd(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=vd[n];e.context=new r(t,o),delete e.contextInfo}}class bd{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class wd{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return function(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};e.id&&(t.id="");e.dataset&&(t.dataset={});e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0);e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight);if(e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){if(!e)return t.$el;return e.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):ep(i,n)?i:i.querySelector(n);return e?Qf(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(Qf(t,r))})),!l&&ep(i,n)&&e.unshift(Qf(i,r)),e}}(e,t,n,r,i))})),n(o)}(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{g(e)?e.forEach(yd):yd(e);const o=n[t];y(o)&&o.call(this,e)})),y(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=he(e),this}select(e){return this._nodesRef=new bd(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new bd(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new bd(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const _d=zu(0,(e=>((e=he(e))&&!function(e){const t=he(e);if(t.$page)return bc(t);if(!t.$)return;{const{$pageInstance:e}=t.$;if(e)return bc(e.proxy)}const n=t.$.root.proxy;return n&&n.$page?bc(n):void 0}(e)&&(e=null),new wd(e||gc())))),xd={formatArgs:{}},Sd={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Td{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=d({},Sd,e)}_getOption(e){const t={transition:d({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const kd=ue((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{Td.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),Cd=zu(0,(e=>(kd(),new Td(e))),0,xd),Ed=zu(0,(()=>{const e=kh();return e&&e.$vm?e.$vm.$locale:yl().getLocale()})),Od={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const $d={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=wu(e,mu)},sourceType(e,t){t.sourceType=wu(e,vu)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Ld={formatArgs:{sourceType(e,t){t.sourceType=wu(e,vu)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Pd=(Boolean,"json"),Ad=["text","arraybuffer"],Md=encodeURIComponent;ArrayBuffer,Boolean;const Id={formatArgs:{method(e,t){t.method=bu((e||"").toUpperCase(),yu)},data(e,t){t.data=e||""},url(e,t){t.method===yu[0]&&k(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(h(t,a)){let e=t[a];null==e?e="":k(e)&&(e=JSON.stringify(e)),s[Md(a)]=Md(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==yu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Pd).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Ad.indexOf(t.responseType)&&(t.responseType="text")}}},jd={formatArgs:{header(e,t){t.header=e||{}}}},Nd={formatArgs:{filePath(e,t){e&&(t.filePath=Df(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},Rd={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=bu((e||"").toUpperCase(),yu)},protocols(e,t){b(e)&&(t.protocols=[e])}}};const Bd={url:{type:String,required:!0}},Fd=(Wd(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Wd(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Ud("navigateTo")),Dd=Ud("redirectTo"),Hd=Ud("reLaunch"),Vd={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(kf().length-1,e)}}};function Wd(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let qd;function zd(){qd=""}function Ud(e){return{formatArgs:{url:Xd(e)},beforeAll:zd}}function Xd(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=kf();return n.length&&(t=sc(n[n.length-1]).route),kc(t,e)}(t)).split("?")[0],r=Cc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!b(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(qd===t&&"appLaunch"!==n.openType)return`${qd} locked`;__uniConfig.ready&&(qd=t)}else if(r.meta.isTabBar){const e=kf(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const Yd={formatArgs:{duration:300}},Gd=(Boolean,{formatArgs:{title:"",mask:!1}}),Zd=["success","loading","none","error"],Jd=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=bu(e,Zd)},image(e,t){t.image=e?Df(e):""},duration:1500,mask:!1}});function Kd(){const e=gc();if(!e)return;const t=Tf(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Ef(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,wc(e,"onHide"))}function Qd(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function ef(e){const t=Tf().values();for(const n of t){const t=yf(n);if(Qd(e,t))return n.$.__isActive=!0,t.id}}const tf=Uu("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(bf.handledBeforeEntryPageRoutes)return Kd(),af({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},ef(e)).then(o).catch(r);_f.push({args:{type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Ud("switchTab"));function nf(){const e=fc();if(!e)return;const t=yf(e);Ef(Lf(t.path,t.id))}const of=Uu("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(bf.handledBeforeEntryPageRoutes)return nf(),af({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o);xf.push({args:{type:"redirectTo",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Dd);function rf(){const e=Tf().keys();for(const t of e)Ef(t)}const sf=Uu("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(bf.handledBeforeEntryPageRoutes)return rf(),af({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o);Sf.push({args:{type:"reLaunch",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Hd);function af({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=kh().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Oe(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++Of,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(Ca(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Le(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}function lf(){if(bf.handledBeforeEntryPageRoutes)return;bf.handledBeforeEntryPageRoutes=!0;const e=[...wf];wf.length=0,e.forEach((({args:e,resolve:t,reject:n})=>af(e).then(t).catch(n)));const t=[..._f];_f.length=0,t.forEach((({args:e,resolve:t,reject:n})=>(Kd(),af(e,ef(e.url)).then(t).catch(n))));const n=[...xf];xf.length=0,n.forEach((({args:e,resolve:t,reject:n})=>(nf(),af(e).then(t).catch(n))));const o=[...Sf];Sf.length=0,o.forEach((({args:e,resolve:t,reject:n})=>(rf(),af(e).then(t).catch(n))))}function cf(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const uf=cf("--a:0"),df=cf("top:env(a)"),ff=cf("top:constant(a)"),pf={"css.var":uf,"css.env":df,"css.constant":ff,"css.backdrop-filter":cf("backdrop-filter:blur(10px)")},hf=zu(0,(e=>!h(pf,e)||pf[e])),gf=(()=>df?"env":ff?"constant":"")();function mf(e){var t,n;nc({"--window-top":(n=0,gf?`calc(${n}px + ${gf}(safe-area-inset-top))`:`${n}px`),"--window-bottom":(t=0,gf?`calc(${t}px + ${gf}(safe-area-inset-bottom))`:`${t}px`)})}const vf=new Map;function yf(e){return e.$page}const bf={handledBeforeEntryPageRoutes:!1},wf=[],_f=[],xf=[],Sf=[];function Tf(){return vf}function kf(){return Cf()}function Cf(){const e=[],t=vf.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Ef(e,t=!0){const n=vf.get(e);n.$.__isUnload=!0,wc(n,"onUnload"),vf.delete(e),t&&function(e){const t=Pf.get(e);t&&(Pf.delete(e),Af.pruneCacheEntry(t))}(e)}let Of=gu();function $f(e){const t=function(e){const t=fu();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),yc("navigateTo",n,{},t)}(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),vf.set(Lf(t.path,t.id),e),1===vf.size&&setTimeout((()=>{lf()}),0)}function Lf(e,t){return e+"$$"+t}const Pf=new Map,Af={get:e=>Pf.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;Af.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);o&&o>t&&(Af.delete(n),Af.pruneCacheEntry(e),Ln((()=>{vf.forEach(((e,t)=>{e.$.isUnmounted&&vf.delete(t)}))})))}))}(e),Pf.set(e,t)},delete(e){Pf.get(e)&&Pf.delete(e)},forEach(e){Pf.forEach(e)}};function Mf(e,t){!function(e){const t=jf(e),{body:n}=document;Nf&&n.removeAttribute(Nf),t&&n.setAttribute(t,""),Nf=t}(e),mf(),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),Bf(e,t)}function If(e){const t=jf(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function jf(e){return e.type.__scopeId}let Nf,Rf;function Bf(e,t){if(document.removeEventListener("touchmove",_c),Rf&&document.removeEventListener("scroll",Rf),t.disableScroll)return document.addEventListener("touchmove",_c);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=yf(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Xg.publishHandler("onPageScroll",{scrollTop:o},e),n&&Xg.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Xg.publishHandler("onReachBottom",{},s)),Rf=Tc(i),requestAnimationFrame((()=>document.addEventListener("scroll",Rf)))}function Ff(e){const{base:t}=__uniConfig.router;return 0===ce(e).indexOf(t)?ce(e):t+e}function Df(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Ff(e.slice(1));e="https:"+e}if(ne.test(e)||oe.test(e)||0===e.indexOf("blob:"))return e;const o=Cf();return o.length?Ff(kc(yf(o[o.length-1]).route,e).slice(1)):e}const Hf=navigator.userAgent,Vf=/android/i.test(Hf),Wf=/iphone|ipad|ipod/i.test(Hf),qf=Hf.match(/Windows NT ([\d|\d.\d]*)/i),zf=/Macintosh|Mac/i.test(Hf),Uf=/Linux|X11/i.test(Hf),Xf=zf&&navigator.maxTouchPoints>0;function Yf(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Gf(e){return e&&90===Math.abs(window.orientation)}function Zf(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Jf(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Kf(e,t,n,o){Yg.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Qf(e,t){const n={},{top:o,topWindowHeight:r}=tc();if(t.node){const t=e.tagName.split("-")[1]||e.tagName;t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=_e(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(g(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(g(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function ep(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}const tp={};function np(e,t){const n=tp[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return op(s,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function op(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function rp(e){for(const n in tp)if(h(tp,n)){if(tp[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return tp[t]=e,t}function ip(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete tp[e]}const sp=Gc(),ap=Gc();const lp=Qc({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=un(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Xt({width:-1,height:-1});return io((()=>d({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){Mo(o),qo((()=>{t.initial&&Ln(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>li("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[li("div",{onScroll:r},[li("div",null,null)],40,["onScroll"]),li("div",{onScroll:r},[li("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function cp(){}const up={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function dp(e,t,n){function o(e){const t=Pi((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",cp,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",cp,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}io((()=>t.value),(e=>e&&o(e)))}const fp={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},pp={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},hp={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},gp=Qc({name:"Image",props:fp,setup(e,{emit:t}){const n=un(null),o=function(e,t){const n=un(""),o=Pi((()=>{let e="auto",o="";const r=hp[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Xt({rootEl:e,src:Pi((()=>t.src?Df(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return qo((()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0})),r}(n,e),r=nu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=pp[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){mp&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return io((()=>t.mode),((e,t)=>{pp[t]&&r(),pp[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;a(u,d,l),Ln((()=>{o()})),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};io((()=>e.src),(e=>l(e))),io((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),qo((()=>l(e.src))),Xo((()=>c()))}(o,e,n,i,r),()=>li("uni-image",{ref:n},[li("div",{style:o.modeStyle},null,4),pp[e.mode]?li(lp,{onResize:i},null,8,["onResize"]):li("span",null,null)],512)}});const mp="Google Inc."===navigator.vendor;const vp=Se(!0),yp=[];let bp=0,wp=!1;const _p=e=>yp.forEach((t=>t.userAction=e));function xp(e={userAction:!1}){if(!wp){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!bp&&_p(!0),bp++,setTimeout((()=>{!--bp&&_p(!1)}),0)}),vp)})),wp=!0}yp.push(e)}const Sp=()=>!!bp;function Tp(){const e=Xt({userAction:!1});return qo((()=>{xp(e)})),Xo((()=>{!function(e){const t=yp.indexOf(e);t>=0&&yp.splice(t,1)}(e)})),{state:e}}function kp(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function Cp(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const Ep=["none","text","decimal","numeric","tel","search","email","url"],Op=d({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Ep.indexOf(e)},cursorColor:{type:String,default:""}},up),$p=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function Lp(e,t,n,o){let r=null;r=$e((n=>{t.value=Cp(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),io((()=>e.modelValue),r),io((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Wo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Pp(e,t){Tp();const n=Pi((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}io((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),qo((()=>{n.value&&Ln(o)}))}function Ap(e,t,n,o){$l(hc(),"getSelectedTextRange",kp);const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=un(null),r=nu(t,n),i=Pi((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=Pi((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=Pi((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Pi((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=Cp(e.modelValue,e.type)||Cp(e.value,e.type);const u=Xt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return io((()=>u.focus),(e=>n("update:focus",e))),io((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=Lp(e,i,n,s);Pp(e,r),dp(0,r);const{state:l}=function(){const e=Xt({attrs:{}});return qo((()=>{let t=wi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}();!function(e,t){const n=Tr(su,!1);if(!n)return;const o=wi(),r={submit(){const n=o.proxy;return[n[e],b(t)?n[t]:t.value]},reset(){b(t)?o.proxy[t]="":t.value=""}};n.addField(r),Xo((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}io([()=>t.selectionStart,()=>t.selectionEnd],s),io((()=>t.cursor),a),io((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),y(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Mp=d({},Op,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),Ip=ue((()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const n=e.match(/Version\/(\S*)\b/);n&&(t=n[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}}));function jp(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&Ip()&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Np=Qc({name:"Input",props:Mp,emits:["confirm",...$p],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=Pi((()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=o.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=Pi((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf(A(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=un(null!=t?t.toLocaleString():"");return io((()=>e.modelValue),(e=>{n.value=null!=e?e.toLocaleString():""})),io((()=>e.value),(e=>{n.value=null!=e?e.toLocaleString():""})),n}return un("")}(e,i),l={fn:null};const c=un(null),{fieldRef:u,state:d,scopedAttrsState:f,fixDisabledColor:p,trigger:h}=Ap(e,c,t,((t,n)=>{const o=t.target;if("number"===i.value){if(l.fn&&(o.removeEventListener("blur",l.fn),l.fn=null),o.validity&&!o.validity.valid){if((!a.value||!o.value)&&"-"===t.data||"-"===a.value[0]&&"deleteContentBackward"===t.inputType)return a.value="-",n.value="",l.fn=()=>{a.value=o.value=""},o.addEventListener("blur",l.fn),!1;const e=jp(t,a,n,o,l);return"boolean"==typeof e?e:(a.value=n.value=o.value="-"===a.value?"":a.value,!1)}{const e=jp(t,a,n,o,l);if("boolean"==typeof e)return e;a.value=o.value}const r=n.maxlength;if(r>0&&o.value.length>r){o.value=o.value.slice(0,r),n.value=o.value;return(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==o.value}}}));io((()=>d.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())}));const g=["number","digit"],m=Pi((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&p?li("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):li("input",{key:"input",ref:u,value:d.value,onInput:e=>{d.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return li("uni-input",{ref:c},[li("div",{class:"uni-input-wrapper"},[uo(li("div",gi(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Qi,!(d.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?li("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Rp=["class","style"],Bp=/^on[A-Z]+/,Fp=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=wi(),r=dn({}),i=dn({}),s=dn({}),a=n.concat(Rp);return o.attrs=Xt(o.attrs),oo((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(a.includes(n)?e.exclude[n]=o:Bp.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,s.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:s}};function Dp(e){const t=[];return g(e)&&e.forEach((e=>{ni(e)?e.type===qr?t.push(...Dp(e.children)):t.push(e):g(e)&&t.push(...Dp(e))})),t}const Hp=function(e,t,n,o){e.addEventListener(t,(e=>{y(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Vp,Wp;const qp=Qc({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=un(null),o=Pi((()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),r=Pi((()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{const{refreshState:i,refresherDefaultStyle:s,refresherThreshold:a}=e;return li("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==s?li("div",{class:"uni-scroll-view-refresh"},[li("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?li("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[li("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),li("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?li("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[li("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===s?li("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),zp=Se(!0),Up=Qc({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=un(null),i=un(null),s=un(null),a=un(null),l=nu(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=Pi((()=>Number(e.scrollTop)||0)),n=Pi((()=>Number(e.scrollLeft)||0));return{state:Xt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:p,_scrollLeftChanged:h,_scrollTopChanged:g}=function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,d=!1,f=()=>{};const p=Pi((()=>e.scrollX)),h=Pi((()=>e.scrollY)),g=Pi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),m=Pi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",f),i.removeEventListener("webkitTransitionEnd",f),f=()=>x(e,t),i.addEventListener("transitionend",f),i.addEventListener("webkitTransitionEnd",f),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+m.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),p.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+m.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){h.value&&(e.scrollWithAnimation?v(t,"y"):s.value.scrollTop=t)}function w(t){p.value&&(e.scrollWithAnimation?v(t,"x"):s.value.scrollLeft=t)}function _(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(p.value){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):s.value.scrollLeft=r}if(h.value){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):s.value.scrollTop=r}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=s.value;"x"===t?(n.style.overflowX=p.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",f),a.value.removeEventListener("webkitTransitionEnd",f)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),r("refresherrefresh",{},{dy:k.y-T.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{dy:k.y-T.y})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{dy:k.y-T.y}))}t.refreshState=n}}let T={x:0,y:0},k={x:0,y:e.refresherThreshold};return qo((()=>{Ln((()=>{b(n.value),w(o.value)})),_(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===T)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=s.value;if(Math.abs(o-T.x)>Math.abs(i-T.y))if(p.value){if(0===l.scrollLeft&&o>T.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<T.x)return void(a=!1);a=!0}else a=!1;else if(h.value)if(0===l.scrollTop&&i>T.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<T.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-T.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},f=function(e){1===e.touches.length&&(T={x:e.touches[0].pageX,y:e.touches[0].pageY})},g=function(n){k={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),T={x:0,y:0},k={x:0,y:e.refresherThreshold}};s.value.addEventListener("touchstart",f,zp),s.value.addEventListener("touchmove",l,Se(!1)),s.value.addEventListener("scroll",i,Se(!1)),s.value.addEventListener("touchend",g,zp),Xo((()=>{s.value.removeEventListener("touchstart",f),s.value.removeEventListener("touchmove",l),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",g)}))})),Mo((()=>{h.value&&(s.value.scrollTop=t.lastScrollTop),p.value&&(s.value.scrollLeft=t.lastScrollLeft)})),io(n,(e=>{b(e)})),io(o,(e=>{w(e)})),io((()=>e.scrollIntoView),(e=>{_(e)})),io((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:p,realScrollY:h,_scrollTopChanged:b,_scrollLeftChanged:w}}(e,c,u,d,l,r,i,a,t),m=Pi((()=>{let e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",p.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),v=Pi((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:d,refreshState:f}=c;return li("uni-scroll-view",{ref:r},[li("div",{ref:s,class:"uni-scroll-view"},[li("div",{ref:i,style:m.value,class:v.value},[t?li(qp,{refreshState:f,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,li("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});function Xp(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,d=0,f=1,p=null,h=!1,g=0,m="";const v=Pi((()=>n.value.length>t.displayMultipleItems)),y=Pi((()=>e.circular&&v.value));function b(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),d=Math.max(o-(l+1),l-s,0),f=Math.max(o-(c+1),c-s,0),p=Math.min(u,d,f),h=[n,l,c][[u,d,f].indexOf(p)];t.updatePosition(h,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*f+"%")+", "+(e.vertical?100*-r*f+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),d=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function w(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function _(){p=null}function x(){if(!p)return void(h=!1);const e=p,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){b(o),p=null,h=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function S(e,o,r){_();const i=t.duration,s=n.value.length;let a=d;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);p={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},h||(h=!0,l=requestAnimationFrame(x))}function T(){s();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=w(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function k(e){e?T():s()}return io([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),io([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){s(),p&&(b(p.toPos),p=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);f=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();f=e.width/t.width,f>0&&f<1||(f=1)}const a=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(a+l-g),g=l):(b(l),e.autoplay&&T())):(u=!0,b(-t.displayMultipleItems-1))})),io((()=>t.interval),(()=>{c&&(s(),T())})),io((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const s=n.value;if(!r){const t=s.length;S(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),io((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),io((()=>e.autoplay&&!t.userTracking),k),k(e.autoplay&&!t.userTracking),qo((()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=w(d+o);e?b(g):(m="touch",t.current=r,S(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}!function(e,t,n){Xo((()=>{document.removeEventListener("mousemove",Vp),document.removeEventListener("mouseup",Wp)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Hp(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),Hp(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),Hp(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const d=Vp=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",d),Hp(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const f=Wp=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),Hp(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),g=d,i=0,a=Date.now(),void _();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&T())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const d=a-s||1,f=o.value;e.vertical?u(-r.dy/f.offsetHeight,-r.ddy/d):u(-r.dx/f.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),Yo((()=>{s(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const Yp=Qc({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=un(null),r=nu(o,n),i=un(null),s=un(null),a=function(e){return Xt({interval:Pi((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:Pi((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:Pi((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=Pi((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:ic(e.previousMargin,!0),bottom:ic(e.nextMargin,!0)}:{top:0,bottom:0,left:ic(e.previousMargin,!0),right:ic(e.nextMargin,!0)}),t})),c=Pi((()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],f=un([]);function p(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(nn(o))}f.value=e}Sr("addSwiperContext",(function(e){d.push(e),p()}));Sr("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),p())}));const{onSwiperDotClick:h,circularEnabled:g,swiperEnabled:m}=Xp(e,a,f,s,n,r);let v=()=>null;return v=Gp(o,e,a,h,f,g,m),()=>{const n=t.default&&t.default();return u=Dp(n),li("uni-swiper",{ref:o},[li("div",{ref:i,class:"uni-swiper-wrapper"},[li("div",{class:"uni-swiper-slides",style:l.value},[li("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&li("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,o)=>li("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),Gp=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,c=!1,u=un(!1);function f(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}oo((()=>{a="auto"===t.navigation,u.value=!0!==t.navigation||a,b()})),oo((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,c=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,c=!0,a&&(u.value=!0))}));const p={onMouseover:e=>f(e,"over"),onMouseout:e=>f(e,"out")};function h(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const g=()=>uc("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",t.navigationColor,26);let m;const v=n=>{clearTimeout(m);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:d}=e.value.getBoundingClientRect();let f=!1;if(f=t.vertical?!(r-a<d/3||l-r<d/3):!(o-i<c/3||s-o<c/3),f)return m=setTimeout((()=>{u.value=f}),300);u.value=f},y=()=>{u.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),a&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return qo(b),function(){const e={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?li(qr,null,[li("div",gi({class:["uni-swiper-navigation uni-swiper-navigation-prev",d({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},p),[g()],16,["onClick"]),li("div",gi({class:["uni-swiper-navigation uni-swiper-navigation-next",d({"uni-swiper-navigation-disabled":c},e)],onClick:e=>h(e,"next",c)},p),[g()],16,["onClick"])]):null}},Zp=Qc({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=un(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return qo((()=>{const e=Tr("addSwiperContext");e&&e(o)})),Yo((()=>{const e=Tr("removeSwiperContext");e&&e(o)})),()=>li("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),Jp={ensp:" ",emsp:" ",nbsp:" "};function Kp(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&Jp[t]&&" "===i&&(i=Jp[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,Jp.nbsp).replace(/&ensp;/g,Jp.ensp).replace(/&emsp;/g,Jp.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const Qp=Qc({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=un(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Ur){const n=Kp(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(ui(e)),t!==r&&o.push(li("br"))}))}else o.push(t)})),li("uni-text",{ref:n,selectable:!!e.selectable||null},[li("span",null,o)],8,["selectable"])}}}),eh=Qc({name:"View",props:d({},ou),setup(e,{slots:t}){const n=un(null),{hovering:o,binding:r}=ru(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?li("uni-view",gi({class:o.value?i:"",ref:n},r),[er(t,"default")],16):li("uni-view",{ref:n},[er(t,"default")],512)}}});function th(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function nh(e,t,n){e&&$l(n||hc(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function oh(e,t){e&&function(e,t){t=Ol(e,t),delete El[t]}(t||hc(),e)}let rh=0;function ih(e,t,n,o){y(t)&&Ho(e,t.bind(n),o)}function sh(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!y(t))&&(Ae.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];g(r)?r.forEach((e=>ih(o,e,n,t))):ih(o,r,n,t)}})),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,wc(n,"onLoad",e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&wc(n,"onShow")}catch(r){console.error(r.message+"\n"+r.stack)}}}function ah(e,t,n){sh(e,t,n)}function lh(e,t,n){return e[t]=n}function ch(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function uh(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?wc(i.proxy,"onError",n):wn(n,0,o&&o.$.vnode,!1)}}function dh(e,t){return e?[...new Set([].concat(e,t))]:t}function fh(e){const t=e.config;var n;t.errorHandler=Ie(e,uh),n=t.optionMergeStrategies,Ae.forEach((e=>{n[e]=dh}));const o=t.globalProperties;o.$set=lh,o.$applyOptions=ah,o.$callMethod=ch,function(e){Me.forEach((t=>t(e)))}(e)}function ph(e){const t=il({history:mh(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:gh});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(hh[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let hh=Object.create(null);const gh=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,hh[o]);if(t)return t}return{left:0,top:0};var o};function mh(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=ba(e);return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=Cf(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=yf(t[r]);Ef(Lf(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const vh={install(e){fh(e),Rc(e),Yc(e),e.config.warnHandler||(e.config.warnHandler=yh),ph(e)}};function yh(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const bh={class:"uni-async-loading"},wh=li("i",{class:"uni-loading"},null,-1),_h=eu({name:"AsyncLoading",render:()=>(Zr(),ti("div",bh,[wh]))});function xh(){window.location.reload()}const Sh=eu({name:"AsyncError",setup(){wl();const{t:e}=yl();return()=>li("div",{class:"uni-async-error",onClick:xh},[e("uni.async.error")],8,["onClick"])}});let Th;function kh(){return Th}function Ch(e){Th=e,Object.defineProperty(Th.$.ctx,"$children",{get:()=>Cf().map((e=>e.$vm))});const t=Th.$.appContext.app;t.component(_h.name)||t.component(_h.name,_h),t.component(Sh.name)||t.component(Sh.name,Sh),function(e){e.$vm=e,e.$mpType="app";const t=un(yl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Th),function(e,t){const n=e.$options||{};n.globalData=d(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Th),Uc(),Ec()}function Eh(e,{clone:t,init:n,setup:o,before:r}){t&&(e=d({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=wi();if(n(r.proxy),o(r),i)return i(e,t)},e}function Oh(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Eh(e.default,t):Eh(e,t)}function $h(e){return Oh(e,{clone:!0,init:$f,setup(e){e.$pageInstance=e;const t=hu(),n=Ce(t.query);e.attrs.__pageQuery=n,yf(e.proxy).options=n,e.proxy.options=n;const o=fu();var r,i;return e.onReachBottom=Xt([]),e.onPageScroll=Xt([]),io([e.onReachBottom,e.onPageScroll],(()=>{const t=fc();e.proxy===t&&Bf(e,o)}),{once:!0}),Wo((()=>{Mf(e,o)})),qo((()=>{If(e);const{onReady:n}=e;n&&N(n),Mh(t)})),jo((()=>{if(!e.__isVisible){Mf(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&N(n),Ln((()=>{Mh(t)}))}}),"ba",r),function(e,t){jo(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&N(t)}}})),i=o.id,Xg.subscribe(Ol(i,"invokeViewApi"),Ll),Xo((()=>{!function(e){Xg.unsubscribe(Ol(e,"invokeViewApi")),Object.keys(El).forEach((t=>{0===t.indexOf(e+".")&&delete El[t]}))}(o.id)})),n}})}function Lh(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=Qh(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Yg.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Ph(e){k(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Yg.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function Ah(){const{emit:e}=Yg;"visible"===document.visibilityState?e("onAppEnterForeground",d({},ap)):e("onAppEnterBackground")}function Mh(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&wc("onTabItemTap",{index:n,text:t,pagePath:o})}function Ih(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),r=(t<10?"0":"")+t;let i=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==r&&(i=r+":"+i),i}function jh(e,t,n){const o=Xt({seeking:!1,gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0,toastThin:!1}),r={x:0,y:0};let i=null;let s;return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];r.x=t.pageX,r.y=t.pageY,o.gestureType="none",o.volumeOld=0},onTouchmove:function(a){function l(){a.stopPropagation(),a.preventDefault()}n.fullscreen&&l();const c=o.gestureType;if("stop"===c)return;const u=a.targetTouches[0],d=u.pageX,f=u.pageY,p=r,h=t.value;if("progress"===c?(!function(e){const n=t.value.duration;let r=e/600*n+o.currentTimeOld;r<0?r=0:r>n&&(r=n);o.currentTimeNew=r}(d-p.x),o.seeking=!0):"volume"===c&&function(e){const n=t.value,r=o.volumeOld;let i;"number"==typeof r&&(i=r-e/200,i<0?i=0:i>1&&(i=1),clearTimeout(s),s=void 0,null==s&&(s=setTimeout((()=>{o.toastThin=!1,s=void 0}),1e3)),n.volume=i,o.volumeNew=i)}(f-p.y),"none"===c)if(Math.abs(d-p.x)>Math.abs(f-p.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=h.currentTime,n.fullscreen||l()}else{if(!e.pageGesture&&!e.vslideGesture)return void(o.gestureType="stop");"none"!==o.gestureType&&null!=i||(i=setTimeout((()=>{o.toastThin=!0}),500)),o.gestureType="volume",o.volumeOld=h.volume,n.fullscreen||l()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}function Nh(e,t,n,o,r,i,s,a){const l={play:e,stop:n,pause:t,seek:o,sendDanmu:r,playbackRate:i,requestFullScreen:s,exitFullScreen:a};!function(e,t,n,o){const r=wi().proxy;qo((()=>{nh(t||th(r),e,o),!n&&t||io((()=>r.id),((t,n)=>{nh(th(r,t),e,o),oh(n&&th(r,n))}))})),Xo((()=>{oh(t||th(r),o)}))}(((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in l&&l[e](n)}),function(e){const t=dc(),n=wi().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+rh++;return qo((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}(),!0)}const Rh={id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},vslideGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},Bh=Qc({name:"Video",props:Rh,emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const r=un(null),i=un(null),s=nu(r,t),{state:a}=Tp(),{$attrs:l}=Fp({excludeListeners:!0});yl(),Tl();const{videoRef:c,state:u,play:d,pause:f,stop:p,seek:h,playbackRate:m,toggle:v,onDurationChange:y,onLoadedMetadata:b,onProgress:w,onWaiting:_,onVideoError:x,onPlay:S,onPause:T,onEnded:k,onTimeUpdate:C}=function(e,t,n){const o=un(null),r=Pi((()=>Df(e.src))),i=Pi((()=>"true"===e.muted||!0===e.muted)),s=Xt({start:!1,src:r,playing:!1,currentTime:0,duration:0,progress:0,buffered:0,muted:i,pauseUpdatingCurrentTime:!1});function a(e){const t=e.target,n=t.buffered;n.length&&(s.buffered=n.end(n.length-1)/t.duration*100)}function l(){o.value.pause()}function c(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)}return io((()=>r.value),(()=>{s.playing=!1,s.currentTime=0})),io((()=>s.buffered),(e=>{n("progress",{},{buffered:e})})),io((()=>i.value),(e=>{o.value.muted=e})),{videoRef:o,state:s,play:function(){const e=o.value;s.start=!0,e.play()},pause:l,stop:function(){c(0),l()},seek:c,playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;s.playing?e.pause():e.play()},onDurationChange:function({target:e}){s.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,r=t.target;o>0&&(r.currentTime=o),n("loadedmetadata",t,{width:r.videoWidth,height:r.videoHeight,duration:r.duration}),a(t)},onProgress:a,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){s.playing=!1,n("error",e,{})},onPlay:function(e){s.start=!0,s.playing=!0,n("play",e,{})},onPause:function(e){s.playing=!1,n("pause",e,{})},onEnded:function(e){s.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target;s.pauseUpdatingCurrentTime||(s.currentTime=t.currentTime);const o=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,s),{state:E,danmuRef:O,updateDanmu:$,toggleDanmu:L,sendDanmu:P}=function(e,t){const n=un(null),o=Xt({enable:Boolean(e.enableDanmu)});let r={time:0,index:-1};const i=g(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function s(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout((function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout((function(){t.remove()}),4e3)}),17)}return i.sort((function(e,t){return(e.time||0)-(t.time||0)})),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,a=r,l={time:n,index:a.index};if(n>a.time)for(let r=a.index+1;r<i.length;r++){const e=i[r];if(!(n>=(e.time||0)))break;l.index=r,t.playing&&o.enable&&s(e)}else if(n<a.time)for(let t=a.index-1;t>-1&&n<=(i[t].time||0);t--)l.index=t-1;r=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){i.splice(r.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,u),{state:A,onFullscreenChange:M,emitFullscreenChange:I,toggleFullscreen:j,requestFullScreen:N,exitFullScreen:R}=function(e,t,n,o,r){const i=Xt({fullscreen:!1}),s=/^Apple/.test(navigator.vendor);function a(t){i.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const i=r.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||s&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),i.appendChild(l)),u&&a(e)}function c(){l(!1)}return Xo(c),{state:i,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||a(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:a,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(s,i,c,a,r),{state:B,onTouchstart:F,onTouchend:D,onTouchmove:H}=jh(e,c,A),{state:V,progressRef:W,ballRef:q,clickProgress:z,toggleControls:U,autoHideEnd:X,autoHideStart:Y}=function(e,t,n,o){const r=un(null),i=un(null),s=Pi((()=>e.showCenterPlayBtn&&!t.start)),a=un(!0),l=Pi((()=>!s.value&&e.controls&&a.value)),c=Xt({seeking:!1,touching:!1,controlsTouching:!1,centerPlayBtnShow:s,controlsShow:l,controlsVisible:a});let u;function d(){u=setTimeout((()=>{c.controlsVisible=!1}),3e3)}function f(){u&&(clearTimeout(u),u=null)}return Xo((()=>{u&&clearTimeout(u)})),io((()=>c.controlsShow&&t.playing&&!c.controlsTouching),(e=>{e?d():f()})),qo((()=>{const e=Se(!1);let s,a,l,u=!0;const d=i.value;function f(e){const n=e.targetTouches[0],i=n.pageX,d=n.pageY;if(u&&Math.abs(i-s)<Math.abs(d-a))return void p(e);u=!1;const f=r.value.offsetWidth;let h=l+(i-s)/f*100;h<0?h=0:h>100&&(h=100),t.progress=h,null==o||o(t.duration*h/100),c.seeking=!0,e.preventDefault(),e.stopPropagation()}function p(o){c.controlsTouching=!1,c.touching&&(d.removeEventListener("touchmove",f,e),u||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),c.touching=!1)}d.addEventListener("touchstart",(n=>{c.controlsTouching=!0;const o=n.targetTouches[0];s=o.pageX,a=o.pageY,l=t.progress,u=!0,c.touching=!0,d.addEventListener("touchmove",f,e)})),d.addEventListener("touchend",p),d.addEventListener("touchcancel",p)})),{state:c,progressRef:r,ballRef:i,clickProgress:function(e){const o=r.value;let i=e.target,s=e.offsetX;for(;i&&i!==o;)s+=i.offsetLeft,i=i.parentNode;const a=o.offsetWidth;let l=0;s>=0&&s<=a&&(l=s/a,n(t.duration*l))},toggleControls:function(){c.controlsVisible=!c.controlsVisible},autoHideStart:d,autoHideEnd:f}}(e,u,h,(e=>{B.currentTimeNew=e}));Nh(d,f,p,h,P,m,N,R);const G=function(e,t,n,o,r){const i=Pi((()=>"progress"===t.gestureType||n.touching));return io(i,(o=>{e.pauseUpdatingCurrentTime=o,n.controlsTouching=o,"progress"===t.gestureType&&o&&(n.controlsVisible=o)})),io([()=>e.currentTime,()=>{Rh.duration}],(()=>{e.progress=e.currentTime/e.duration*100})),io((()=>t.currentTimeNew),(t=>{e.currentTime=t})),i}(u,B,V);return()=>li("uni-video",{ref:r,id:e.id,onClick:U},[li("div",{ref:i,class:"uni-video-container",onTouchstart:F,onTouchend:D,onTouchmove:H,onFullscreenchange:ks(M,["stop"]),onWebkitfullscreenchange:ks((e=>M(e,!0)),["stop"])},[li("video",gi({ref:c,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:u.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onDurationchange:y,onLoadedmetadata:b,onProgress:w,onWaiting:_,onError:x,onPlay:S,onPause:T,onEnded:k,onTimeupdate:e=>{C(e),$(e)},onWebkitbeginfullscreen:()=>I(!0),onX5videoenterfullscreen:()=>I(!0),onWebkitendfullscreen:()=>I(!1),onX5videoexitfullscreen:()=>I(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),uo(li("div",{class:"uni-video-bar uni-video-bar-full",onClick:ks((()=>{}),["stop"])},[li("div",{class:"uni-video-controls"},[uo(li("div",{class:{"uni-video-icon":!0,"uni-video-control-button":!0,"uni-video-control-button-play":!u.playing,"uni-video-control-button-pause":u.playing},onClick:ks(v,["stop"])},null,10,["onClick"]),[[Qi,e.showPlayBtn]]),uo(li("div",{class:"uni-video-current-time"},[Ih(u.currentTime)],512),[[Qi,e.showProgress]]),uo(li("div",{ref:W,class:"uni-video-progress-container",onClick:ks(z,["stop"])},[li("div",{class:{"uni-video-progress":!0,"uni-video-progress-progressing":G.value}},[li("div",{style:{width:u.buffered-u.progress+"%",left:u.progress+"%"},class:"uni-video-progress-buffered"},null,4),li("div",{style:{width:u.progress+"%"},class:"uni-video-progress-played"},null,4),li("div",{ref:q,style:{left:u.progress+"%"},class:{"uni-video-ball":!0,"uni-video-ball-progressing":G.value}},[li("div",{class:"uni-video-inner"},null)],6)],2)],8,["onClick"]),[[Qi,e.showProgress]]),uo(li("div",{class:"uni-video-duration"},[Ih(Number(e.duration)||u.duration)],512),[[Qi,e.showProgress]])]),uo(li("div",{class:{"uni-video-icon":!0,"uni-video-danmu-button":!0,"uni-video-danmu-button-active":E.enable},onClick:ks(L,["stop"])},null,10,["onClick"]),[[Qi,e.danmuBtn]]),uo(li("div",{class:{"uni-video-icon":!0,"uni-video-fullscreen":!0,"uni-video-type-fullscreen":A.fullscreen},onClick:ks((()=>j(!A.fullscreen)),["stop"])},null,10,["onClick"]),[[Qi,e.showFullscreenBtn]])],8,["onClick"]),[[Qi,V.controlsShow]]),uo(li("div",{ref:O,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[Qi,u.start&&E.enable]]),V.centerPlayBtnShow&&li("div",{class:"uni-video-cover",onClick:ks((()=>{}),["stop"])},[li("div",{class:"uni-video-cover-play-button uni-video-icon",onClick:ks(d,["stop"])},null,8,["onClick"])],8,["onClick"]),li("div",{class:"uni-video-loading"},["volume"===B.gestureType?li("div",{class:{"uni-video-toast-container":!0,"uni-video-toast-container-thin":B.toastThin},style:{marginTop:"5px"}},[!B.toastThin&&B.volumeNew>0&&"volume"===B.gestureType?li("text",{class:"uni-video-icon uni-video-toast-icon"},[""]):!B.toastThin&&li("text",{class:"uni-video-icon uni-video-toast-icon"},[""]),li("div",{class:"uni-video-toast-draw",style:{width:100*B.volumeNew+"%"}},null)],2):null]),li("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":G.value}},[li("div",{class:"uni-video-toast-title"},[li("span",{class:"uni-video-toast-title-current-time"},[Ih(B.currentTimeNew)])," / ",Number(e.duration)||Ih(u.duration)])],2),li("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id","onClick"])}}),Fh=ue((()=>{pd.forEach((e=>{Dh.prototype[e]=function(t){y(t)&&this._events[e].push(t)}})),hd.forEach((e=>{Dh.prototype[e]=function(t){var n=this._events[e.replace("off","on")],o=n.indexOf(t);o>=0&&n.splice(o,1)}}))}));class Dh{constructor(){this._src="";var e=this._audio=new Audio;this._stoping=!1;["src","autoplay","loop","duration","currentTime","paused","volume"].forEach((t=>{Object.defineProperty(this,t,{set:"src"===t?t=>(e.src=Df(t),this._src=t,t):n=>(e[t]=n,n),get:"src"===t?()=>this._src:()=>e[t]})})),this.startTime=0,Object.defineProperty(this,"obeyMuteSwitch",{set:()=>!1,get:()=>!1}),Object.defineProperty(this,"buffered",{get(){var t=e.buffered;return t.length?t.end(t.length-1):0}}),this._events={},pd.forEach((e=>{this._events[e]=[]})),e.addEventListener("loadedmetadata",(()=>{var t=Number(this.startTime)||0;t>0&&(e.currentTime=t)}));var t=["canplay","pause","seeking","seeked","timeUpdate"];t.concat(["play","ended","error","waiting"]).forEach((n=>{e.addEventListener(n.toLowerCase(),(()=>{if(this._stoping&&t.indexOf(n)>=0)return;const e=`on${n.slice(0,1).toUpperCase()}${n.slice(1)}`;this._events[e].forEach((e=>{e()}))}),!1)})),Fh()}play(){this._stoping=!1,this._audio.play()}pause(){this._audio.pause()}stop(){this._stoping=!0,this._audio.pause(),this._audio.currentTime=0,this._events.onStop.forEach((e=>{e()}))}seek(e){this._stoping=!1,"number"!=typeof(e=Number(e))||isNaN(e)||(this._audio.currentTime=e)}destroy(){this.stop()}}const Hh=zu(0,(()=>new Dh)),Vh=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let Wh;function qh(){if(Wh=Wh||Vh.__DC_STAT_UUID,!Wh){Wh=Date.now()+""+Math.floor(1e7*Math.random());try{Vh.__DC_STAT_UUID=Wh}catch(e){}}return Wh}function zh(){if(!0!==__uniConfig.darkmode)return b(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function Uh(){let e,t="0",n="",o="phone";const r=navigator.language;if(Wf){e="iOS";const o=Hf.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=Hf.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(Vf){e="Android";const o=Hf.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=Hf.match(/\((.+?)\)/),i=r?r[1].split(";"):Hf.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Xf){if(n="iPad",e="iOS",o="pad",t=y(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=Hf.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(qf||zf||Uf){n="PC",e="PC",o="pc",t="0";let r=Hf.match(/\((.+?)\)/)[1];if(qf){switch(e="Windows",qf[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(zf){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Uf){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(Hf)&&(a=t[n],l=Hf.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:Hf,osname:e,osversion:t,theme:zh()}}const Xh=zu(0,(()=>{const e=window.devicePixelRatio,t=Yf(),n=Gf(t),o=Zf(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=Jf(o);let s=window.innerHeight;const a=Jl.top,l={left:Jl.left,right:i-Jl.right,top:Jl.top,bottom:s-Jl.bottom,width:i-Jl.left-Jl.right,height:s-Jl.top-Jl.bottom},{top:c,bottom:u}=tc();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Jl.top,right:Jl.right,bottom:Jl.bottom,left:Jl.left},screenTop:r-s}}));let Yh,Gh=!0;function Zh(){Gh&&(Yh=Uh())}const Jh=zu(0,(()=>{Zh();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:c}=Yh;return d({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:qh(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLocaleLowerCase():void 0,osVersion:c})})),Kh=zu(0,(()=>{Zh();const{theme:e,language:t,browserName:n,browserVersion:o}=Yh;return d({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Ed?Ed():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})})),Qh=zu(0,(()=>{Gh=!0,Zh(),Gh=!1;const e=Xh(),t=Jh(),n=Kh();Gh=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=Yh,l=d(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return k(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)}));const eg=zu(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function tg(e){const t=localStorage&&localStorage.getItem(e);if(!b(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=b(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const ng=zu(0,(e=>{try{return tg(e)}catch(t){return""}})),og=zu(0,(e=>{localStorage&&localStorage.removeItem(e)})),rg=zu(0,(()=>{localStorage&&localStorage.clear()})),ig=Uu("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())})),sg={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function ag({count:e,sourceType:t,type:n,extension:o}){xp();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${sg[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let lg=null;const cg=Uu("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{Sl();const{t:i}=yl();lg&&(document.body.removeChild(lg),lg=null),lg=ag({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(lg),lg.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||rp(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),lg.click(),Sp()||console.warn(i("uni.chooseFile.notUserActivation"))}),0,$d),ug={esc:["Esc","Escape"],enter:["Enter"]},dg=Object.keys(ug);function fg(e,{onEsc:t,onEnter:n}){const o=un(e.visible),{key:r,disable:i}=function(){const e=un(""),t=un(!1),n=n=>{if(t.value)return;const o=dg.find((e=>-1!==ug[e].indexOf(n.key)));o&&(e.value=o),Ln((()=>e.value=""))};return qo((()=>{document.addEventListener("keyup",n)})),Xo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}();return io((()=>e.visible),(e=>o.value=e)),io((()=>o.value),(e=>i.value=!e)),oo((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let pg=null;const hg=Uu("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{Sl();const{t:r}=yl();pg&&(document.body.removeChild(pg),pg=null),pg=ag({sourceType:e,extension:t,type:"video"}),document.body.appendChild(pg),pg.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||rp(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=rp(t);i.onloadedmetadata=function(){ip(e),n(d(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,ip(e),n(r)}),300),i.src=e}else n(r)})),pg.click(),Sp()||console.warn(r("uni.chooseFile.notUserActivation"))}),0,Ld),gg=qu("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(b(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(m){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)h(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const f=new XMLHttpRequest,p=new mg(f);f.open(o,e);for(const v in n)h(n,v)&&f.setRequestHeader(v,n[v]);const g=setTimeout((function(){f.onload=f.onabort=f.onerror=null,p.abort(),c("timeout",{errCode:5})}),a);return f.responseType=i,f.onload=function(){clearTimeout(g);const e=f.status;let t="text"===i?f.responseText:f.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(m){}l({data:t,statusCode:e,header:vg(f.getAllResponseHeaders()),cookies:[]})},f.onabort=function(){clearTimeout(g),c("abort",{errCode:600003})},f.onerror=function(){clearTimeout(g),c(void 0,{errCode:5})},f.withCredentials=s,f.send(u),p}),0,Id);class mg{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function vg(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class yg{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const bg=qu("downloadFile",(({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,s=new XMLHttpRequest,a=new yg(s);return s.open("GET",e,!0),Object.keys(t).forEach((e=>{s.setRequestHeader(e,t[e])})),s.responseType="blob",s.onload=function(){clearTimeout(i);const t=s.status,n=this.response;let r;const a=s.getResponseHeader("content-disposition");if(a){const e=a.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:rp(n)})},s.onabort=function(){clearTimeout(i),r("abort",{errCode:600003})},s.onerror=function(){clearTimeout(i),r("",{errCode:602001})},s.onprogress=function(e){a._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},s.send(),i=setTimeout((function(){s.onprogress=s.onload=s.onabort=s.onerror=null,a.abort(),r("timeout",{errCode:5})}),n),a}),0,jd);class wg{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const _g=qu("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:s={},timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new wg;return g(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(op(e)):np(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(s).forEach((e=>{d.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),a),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,Nd),xg=[],Sg={open:"",close:"",error:"",message:""};class Tg{constructor(e,t,n){let o;this._callbacks={open:[],close:[],error:[],message:[]};try{const n=this._webSocket=new WebSocket(e,t);n.binaryType="arraybuffer";["open","close","error","message"].forEach((e=>{this._callbacks[e]=[],n.addEventListener(e,(t=>{const{data:n,code:o,reason:r}=t,i="message"===e?{data:n}:"close"===e?{code:o,reason:r}:{};if(this._callbacks[e].forEach((t=>{try{t(i)}catch(n){console.error(`thirdScriptError\n${n};at socketTask.on${M(e)} callback function\n`,n)}})),this===xg[0]&&Sg[e]&&Yg.invokeOnCallback(Sg[e],i),"error"===e||"close"===e){const e=xg.indexOf(this);e>=0&&xg.splice(e,1)}}))}));["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach((e=>{Object.defineProperty(this,e,{get:()=>n[e]})}))}catch(r){o=r}n&&n(o,this)}send(e){const t=(e||{}).data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw de(e,{errMsg:"sendSocketMessage:fail SocketTask.readyState is not OPEN",errCode:10002}),new Error("SocketTask.readyState is not OPEN");n.send(t),de(e,"sendSocketMessage:ok")}catch(o){de(e,{errMsg:`sendSocketMessage:fail ${o}`,errCode:602001})}}close(e={}){const t=this._webSocket;try{const n=e.code||1e3,o=e.reason;b(o)?t.close(n,o):t.close(n),de(e,"closeSocket:ok")}catch(n){de(e,`closeSocket:fail ${n}`)}}onOpen(e){this._callbacks.open.push(e)}onMessage(e){this._callbacks.message.push(e)}onError(e){this._callbacks.error.push(e)}onClose(e){this._callbacks.close.push(e)}}const kg=qu("connectSocket",(({url:e,protocols:t},{resolve:n,reject:o})=>new Tg(e,t,((e,t)=>{e?o(e.toString(),{errCode:600009}):(xg.push(t),n())}))),0,Rd),Cg=Uu("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===wc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(kh().$router.go(-e.delta),t()):n("onBackPress")}),0,Vd),Eg=Uu("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(bf.handledBeforeEntryPageRoutes)return af({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);wf.push({args:{type:"navigateTo",url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Fd);const Og={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Zd.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},$g={light:"#fff",dark:"rgba(255,255,255,0.9)"},Lg=e=>$g[e],Pg=To({name:"Toast",props:Og,setup(e){_l(),xl();const{Icon:t}=function(e){const t=un(Lg(zh())),n=({theme:e})=>t.value=Lg(e);oo((()=>{var t;e.visible?(t=n,__uniConfig.darkmode&&Yg.on("onThemeChange",t)):function(e){Yg.off("onThemeChange",e)}(n)}));return{Icon:Pi((()=>{switch(e.icon){case"success":return li(uc(lc,t.value,38),{class:"uni-toast__icon"});case"error":return li(uc(cc,t.value,38),{class:"uni-toast__icon"});case"loading":return li("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=fg(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return li(Fi,{name:"uni-fade"},{default:()=>[uo(li("uni-toast",{"data-duration":r},[o?li("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Kl},null,40,["onTouchmove"]):"",s||t.value?li("div",{class:"uni-toast"},[s?li("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,li("p",{class:"uni-toast__content"},[i])]):li("div",{class:"uni-sample-toast"},[li("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Qi,n.value]])]})}}});let Ag,Mg,Ig="";const jg=We();function Ng(e){Ag?d(Ag,e):(Ag=Xt(d(e,{visible:!1})),Ln((()=>{var e,t,n;jg.run((()=>{io([()=>Ag.visible,()=>Ag.duration],(([e,t])=>{if(e){if(Mg&&clearTimeout(Mg),"onShowLoading"===Ig)return;Mg=setTimeout((()=>{Hg("onHideToast")}),t)}else Mg&&clearTimeout(Mg)}))})),Yg.on("onHidePopup",(()=>Hg("onHidePopup"))),(e=Pg,t=Ag,n=()=>{},t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Os(To({setup:()=>()=>(Zr(),ti(e,t,null,16))}))).mount(function(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}("u-a-t"))}))),setTimeout((()=>{Ag.visible=!0}),10)}const Rg=Uu("showToast",((e,{resolve:t,reject:n})=>{Ng(e),Ig="onShowToast",t()}),0,Jd),Bg={icon:"loading",duration:1e8,image:""},Fg=Uu("showLoading",((e,{resolve:t,reject:n})=>{d(e,Bg),Ng(e),Ig="onShowLoading",t()}),0,Gd),Dg=Uu("hideLoading",((e,{resolve:t,reject:n})=>{Hg("onHideLoading"),t()}));function Hg(e){const{t:t}=yl();if(!Ig)return;let n="";if("onHideToast"===e&&"onShowToast"!==Ig?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Ig&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);Ig="",setTimeout((()=>{Ag.visible=!1}),10)}function Vg(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Yg.emit("onNavigationBarChange",{titleText:t})}oo(t),Mo(t)}const Wg=Uu("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{!function(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:s}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=s;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:a}=n;i.titleText=a}o()}(pc(),"setNavigationBarTitle",e,t,n)})),qg=Uu("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(b(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const s=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),s(t-10)}))};s(t)}(t||e||0,n),o()}),0,Yd),zg=Uu("stopPullDownRefresh",((e,{resolve:t})=>{Yg.invokeViewMethod("stopPullDownRefresh",{},hc()),t()})),Ug=eu({name:"Layout",setup(e,{emit:t}){const n=un(null);nc({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=sl();return{routeKey:Pi((()=>Lf("/"+e.meta.route,gu()))),isTabBar:Pi((()=>e.meta.isTabBar)),routeCache:Af}}(),{layoutState:r,windowState:i}=function(){hu();{const e=Xt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return io((()=>e.marginWidth),(e=>nc({"--window-margin":e+"px"}))),io((()=>e.leftWindowWidth+e.marginWidth),(e=>{nc({"--window-left":e+"px"})})),io((()=>e.rightWindowWidth+e.marginWidth),(e=>{nc({"--window-right":e+"px"})})),{layoutState:e,windowState:Pi((()=>({})))}}}();!function(e,t){const n=hu();function o(){const o=document.body.clientWidth,r=Cf();let i={};if(r.length>0){i=yf(r[r.length-1]).meta}else{const e=Cc(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((h(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,Ln((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,Ln((()=>{const e=t.value;e&&e.removeAttribute("style")})))}io([()=>n.path],o),qo((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=un(!1);return Pi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(!1);return()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return li(rl,null,{default:zn((({Component:o})=>[(Zr(),ti(Po,{matchBy:"key",cache:n},[(Zr(),ti(Kn(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o);return li("uni-app",{ref:n,class:s.value},[e,!1],2)}}});const Xg=d(Pl,{publishHandler(e,t,n){Yg.subscribeHandler(e,t,n)}}),Yg=d(Dc,{publishHandler(e,t,n){Xg.subscribeHandler(e,t,n)}}),Gg=eu({name:"PageBody",setup(e,t){const n=un(null),o=un(null);return io((()=>false.enablePullDownRefresh),(()=>{o.value=null}),{immediate:!0}),()=>li(qr,null,[!1,li("uni-page-wrapper",gi({ref:n},o.value),[li("uni-page-body",null,[er(t.slots,"default")]),null],16)])}}),Zg=eu({name:"Page",setup(e,t){let n=pu(gu());n.navigationBar;const o={};return Vg(n),()=>li("uni-page",{"data-page":n.route,style:o},[Jg(t),null])}});function Jg(e){return Zr(),ti(Gg,{key:0},{default:zn((()=>[er(e.slots,"page")])),_:3})}const Kg={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=td;const Qg=Object.assign({}),em=Object.assign;window.__uniConfig=em({easycom:{autoscan:!0,custom:{"^uv-(.*)":"@climblee/uv-ui/components/uv-$1/uv-$1.vue"}},globalStyle:{backgroundColor:"#F8F8F8",navigationBar:{backgroundColor:"#F8F8F8",titleText:"uni-app",style:"custom",type:"default",titleColor:"#000000"},isNVue:!1},uniIdRouter:{},compilerVersion:"4.66"},{appId:"__UNI__2D0267D",appName:"KF",appVersion:"1.0.0",appVersionCode:"100",async:Kg,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(Qg).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return em(e[n]||(e[n]={}),Qg[t].default),e}),{}),router:{mode:"history",base:"/kefu_m/",assets:"assets",routerBase:"/kefu_m/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const tm={delay:Kg.delay,timeout:Kg.timeout,suspensible:Kg.suspensible};Kg.loading&&(tm.loadingComponent={name:"SystemAsyncLoading",render:()=>li(Zn(Kg.loading))}),Kg.error&&(tm.errorComponent={name:"SystemAsyncError",render:()=>li(Zn(Kg.error))});const nm=()=>o((()=>import("./pages-sessionlist-sessionlist.zoK3A1EM.js")),__vite__mapDeps([0,1,2,3,4,5,6,7])).then((e=>$h(e.default||e))),om=Co(em({loader:nm},tm)),rm=()=>o((()=>import("./pages-index-index.C3w4cmij.js")),__vite__mapDeps([8,9,1,2,10,3,4])).then((e=>$h(e.default||e))),im=Co(em({loader:rm},tm)),sm=()=>o((()=>import("./pages-login-login.B2tPFuBA.js")),__vite__mapDeps([11,1,2,9,10,12])).then((e=>$h(e.default||e))),am=Co(em({loader:sm},tm)),lm=()=>o((()=>import("./pages-chat-chat.BaQirvQR.js")),__vite__mapDeps([13,1,2,5,6,14])).then((e=>$h(e.default||e))),cm=Co(em({loader:lm},tm));function um(e,t){return Zr(),ti(Zg,null,{page:zn((()=>[li(e,em({},t,{ref:"page"}),null,512)])),_:1})}window.__uniRoutes=[{path:"/",alias:"/pages/sessionlist/sessionlist",component:{setup(){const e=kh(),t=e&&e.$route&&e.$route.query||{};return()=>um(om,t)}},loader:nm,meta:{isQuit:!0,isEntry:!0,navigationBar:{titleText:"会话列表",type:"default"},isNVue:!1}},{path:"/pages/index/index",component:{setup(){const e=kh(),t=e&&e.$route&&e.$route.query||{};return()=>um(im,t)}},loader:rm,meta:{navigationBar:{titleText:"uni-app",type:"default"},isNVue:!1}},{path:"/pages/login/login",component:{setup(){const e=kh(),t=e&&e.$route&&e.$route.query||{};return()=>um(am,t)}},loader:sm,meta:{navigationBar:{titleText:"登录",type:"default"},isNVue:!1}},{path:"/pages/chat/chat",component:{setup(){const e=kh(),t=e&&e.$route&&e.$route.query||{};return()=>um(cm,t)}},loader:lm,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const dm={baseURL:"",timeout:6e5};dm.baseURL="https://www.huohanghang.cn";const fm={set(e,t){eg(e,t)},get:e=>ng(e),remove(e){og(e)},clear(){rg()}};const pm=function(e,t,n){Eg({url:e,success:e=>{e.eventChannel.emit(t,n)}})},hm=function(e){uni.$uv.toast(e)},gm=()=>{let e=1;return e=navigator.userAgent.indexOf("Mobile")>-1?6:5,"micromessenger"==navigator.userAgent.toLowerCase().match(/MicroMessenger/i)&&(e=2),e},mm={request:(e,t,n,o=!1)=>new Promise(((r,i)=>{o?(console.error(n,name),_g({url:dm.baseURL+e,filePath:n,name:"file",header:{token:fm.get("token")||""},success:e=>{let t=JSON.parse(e.data);200==t.code||1==t.code||1204==t.code?r(t):hm(t.info)},fail:e=>{console.log(e),hm("上传失败")}})):gg({url:dm.baseURL+e,data:n,method:t,timeout:dm.timeout,header:{"Content-Type":"application/x-www-form-urlencoded",token:fm.get("token")||"",UserAuth:"user"},success:e=>{1==e.data.code||100==e.data.code||2==e.data.code||4==e.data.code?r(e.data):401==e.data.code||-1==e.data.code?(fm.remove("token"),setTimeout((()=>{uni.$uv.route({type:"redirect",url:"/pages/login/login"}),function(e){const t=kf(),n=t[t.length-1].$page.fullPath;fm.set("losePageUrl",n),fm.set("isLoseLogin",!0)}()}),1e3),hm("登录状态失效")):hm(e.data.msg)},fail(e){console.error(e),i(e)}})}))},vm=e=>mm.request("/kefuapi/file/formImage","POST",e,!0),ym=e=>mm.request("/kefuapi/file/formVideo","POST",e,!0),bm=e=>mm.request("/kefuapi/account/login","POST",e),wm=e=>mm.request("/kefuapi/chat/user","GET",e),_m=e=>mm.request("/kefuapi/chat/record","GET",e),xm=e=>mm.request("/kefuapi/chat/kefuInfo","GET",e),Sm=e=>mm.request("/kefuapi/chat/userInfo","GET",e),Tm=e=>mm.request("/kefuapi/account/logout","GET",e),km=e=>mm.request("/kefuapi/chat/reply","GET",e),Cm=e=>mm.request("/kefuapi/chat/order","GET",e);class Em{constructor(e,n){t(this,"events",{connect:null,close:null,message:null,error:null,open:null}),this.connected=!1,this.error=!1,this.url=`${e}${function(e){let t="";if("object"==typeof e){t="?";for(let n in e)t+=`${n}=${e[n]}&`;t=t.slice(0,-1)}return t}(n)}`,this.socketTask={},this.reconnectLock=!0,this.reconnectTimeout=null,this.reconnectNums=0,this.timeout=1e4,this.clientTimeout=null,this.serverTimeout=null}addEvent(e,t){this.events[e]=t}dispatch(e,t){const n=this.events[e];n&&n(t)}connect(){this.dispatch("connect"),this.socketTask=kg({url:this.url,complete:e=>{}}),this.socketTask.onOpen(this.onOpen.bind(this)),this.socketTask.onError(this.onError.bind(this)),this.socketTask.onMessage(this.onMessage.bind(this)),this.socketTask.onClose(this.onClose.bind(this))}close(){this.reconnectLock=!1,clearTimeout(this.clientTimeout),clearTimeout(this.serverTimeout),this.socketTask.close&&this.socketTask.close()}reconnect(){this.reconnectLock&&(this.reconnectNums>=5||(this.reconnectNums++,this.reconnectLock=!1,clearTimeout(this.reconnectTimeout),this.reconnectTimeout=setTimeout((()=>{this.connect(),this.reconnectLock=!0}),4e3)))}start(){clearTimeout(this.clientTimeout),clearTimeout(this.serverTimeout),this.clientTimeout=setTimeout((()=>{this.send({event:"ping"}),this.serverTimeout=setTimeout((()=>{this.socketTask.close()}),this.timeout)}),this.timeout)}reset(){this.reconnectNums=0,this.start()}send(e){if(!this.connected)return;let t=JSON.stringify(e);this.socketTask.send({data:t})}onOpen(){this.connected=!0,this.start(),console.log("连接成功"),this.dispatch("open")}onError(e){this.error=!0,this.connected=!1,this.dispatch("error")}onMessage({data:e}){this.dispatch("message",JSON.parse(e)),this.reset()}onClose(e){this.dispatch("close"),this.connected=!1,this.reconnect()}}const Om={globalData:{config:{},socket:{}},onLaunch:function(){this.chatconfig(),rd("updateSocket"),od("updateSocket",(()=>{this.updateSocket()}))},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")},methods:{updateSocket(){this.init()},async chatconfig(){const{code:e,data:t}=await mm.request("/kefuapi/chat/config","GET",n);var n;1==e&&(this.globalData.config=t,""!=fm.get("token")&&(console.log(1,fm.get("token")),this.updateSocket()))},init(){console.log(1),this.globalData.socket=new Em(this.globalData.config.ws_domain+"/",{token:fm.get("token"),type:"kefu",client:gm(),shop_id:fm.get("serveinfo").shop_id,chat_type:"kefu_chat"}),this.globalData.socket.addEvent("connect",(()=>{})),this.globalData.socket.addEvent("open",(()=>{})),this.globalData.socket.addEvent("message",(e=>{switch(e.event){case"login":this.loginEvent(e.data);break;case"chat":this.chatEvent(e.data);break;case"transfer":this.transferEvent(e.data);break;case"error":this.errorEvent(e.data)}})),this.globalData.socket.addEvent("error",(e=>{console.log("error连接失败")})),this.globalData.socket.connect()},loginEvent(e){console.log(e)},chatEvent(e){console.log(e);const t=fm.get("serveinfo").id,n=e.from_id,o=e.to_id;if(n==t||o==t){console.log(1);const r=n==t?o:n,i=kf();console.log(i[i.length-1]);const s=i[i.length-1].options.userid;console.log(s,r),r==s&&id("addChatRecordData",e),id("updateSessionlist"),id("updateBadge",{from_id:n})}},transferEvent(e){console.log(e)},errorEvent(e){console.log(e)}}};Oh(Om,{init:Ch,setup(e){const t=hu(),n=()=>{var n;n=e,Object.keys(Od).forEach((e=>{Od[e].forEach((t=>{Ho(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return d(sp,{path:e,query:t}),d(ap,sp),d({},sp)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:Ce(t.query)});if(o&&N(o,s),r&&N(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};lf(),i&&N(i,e)}};return Tr(Xa).isReady().then(n),qo((()=>{window.addEventListener("resize",$e(Lh,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Ph),document.addEventListener("visibilitychange",Ah),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Yg.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Zr(),ti(Ug));e.setup=(e,o)=>{const r=t&&t(e,o);return y(r)?n:r},e.render=n}});var $m=Object.prototype.toString;function Lm(e){return"[object Array]"===$m.call(e)}function Pm(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),Lm(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.call(null,e[r],r,e)}function Am(){let e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=Am(e[n],t):e[n]="object"==typeof t?Am({},t):t}for(let n=0,o=arguments.length;n<o;n++)Pm(arguments[n],t);return e}function Mm(e){return void 0===e}function Im(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function jm(e,t,n){if(!t)return e;var o,r;if(n)o=n(t);else if(r=t,"undefined"!=typeof URLSearchParams&&r instanceof URLSearchParams)o=t.toString();else{var i=[];Pm(t,(function(e,t){null!=e&&(Lm(e)?t+="[]":e=[e],Pm(e,(function(e){!function(e){return"[object Date]"===$m.call(e)}(e)?function(e){return null!==e&&"object"==typeof e}(e)&&(e=JSON.stringify(e)):e=e.toISOString(),i.push(Im(t)+"="+Im(e))})))})),o=i.join("&")}if(o){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const Nm=(e,t)=>{let n={};return e.forEach((e=>{Mm(t[e])||(n[e]=t[e])})),n},Rm=e=>(e=>new Promise(((t,n)=>{let o=jm((r=e.baseURL,i=e.url,r&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(r,i):i),e.params,e.paramsSerializer);var r,i;const s={url:o,header:e.header,complete:r=>{e.fullPath=o,r.config=e,r.rawData=r.data;try{let t=!1;const n=typeof e.forcedJSONParsing;"boolean"===n?t=e.forcedJSONParsing:"object"===n&&(t=(e.forcedJSONParsing.include||[]).includes(e.method)),t&&"string"==typeof r.data&&(r.data=JSON.parse(r.data))}catch(i){}!function(e,t,n){const o=n.config.validateStatus,r=n.statusCode;!r||o&&!o(r)?t(n):e(n)}(t,n,r)}};let a;if("UPLOAD"===e.method){delete s.header["content-type"],delete s.header["Content-Type"];let t={filePath:e.filePath,name:e.name};const n=["files","file","timeout","formData"];a=_g({...s,...t,...Nm(n,e)})}else if("DOWNLOAD"===e.method){const t=["timeout"];a=bg({...s,...Nm(t,e)})}else{const t=["data","method","timeout","dataType","responseType","withCredentials"];a=gg({...s,...Nm(t,e)})}e.getTask&&e.getTask(a,e)})))(e);function Bm(){this.handlers=[]}Bm.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Bm.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Bm.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const Fm=(e,t,n)=>{let o={};return e.forEach((e=>{Mm(n[e])?Mm(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},Dm={baseURL:"",header:{},method:"GET",dataType:"json",paramsSerializer:null,responseType:"text",custom:{},timeout:6e4,withCredentials:!1,validateStatus:function(e){return e>=200&&e<300},forcedJSONParsing:!0};var Hm=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(a){t=function(){}}try{n=Set}catch(a){n=function(){}}try{o=Promise}catch(a){o=function(){}}function r(i,a,l,c,u){"object"==typeof a&&(l=a.depth,c=a.prototype,u=a.includeNonEnumerable,a=a.circular);var d=[],f=[],p="undefined"!=typeof Buffer;return void 0===a&&(a=!0),void 0===l&&(l=1/0),function i(l,h){if(null===l)return null;if(0===h)return l;var g,m;if("object"!=typeof l)return l;if(e(l,t))g=new t;else if(e(l,n))g=new n;else if(e(l,o))g=new o((function(e,t){l.then((function(t){e(i(t,h-1))}),(function(e){t(i(e,h-1))}))}));else if(r.__isArray(l))g=[];else if(r.__isRegExp(l))g=new RegExp(l.source,s(l)),l.lastIndex&&(g.lastIndex=l.lastIndex);else if(r.__isDate(l))g=new Date(l.getTime());else{if(p&&Buffer.isBuffer(l))return Buffer.from?g=Buffer.from(l):(g=new Buffer(l.length),l.copy(g)),g;e(l,Error)?g=Object.create(l):void 0===c?(m=Object.getPrototypeOf(l),g=Object.create(m)):(g=Object.create(c),m=c)}if(a){var v=d.indexOf(l);if(-1!=v)return f[v];d.push(l),f.push(g)}for(var y in e(l,t)&&l.forEach((function(e,t){var n=i(t,h-1),o=i(e,h-1);g.set(n,o)})),e(l,n)&&l.forEach((function(e){var t=i(e,h-1);g.add(t)})),l){Object.getOwnPropertyDescriptor(l,y)&&(g[y]=i(l[y],h-1));try{if("undefined"===Object.getOwnPropertyDescriptor(l,y).set)continue;g[y]=i(l[y],h-1)}catch(T){if(T instanceof TypeError)continue;if(T instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(l);for(y=0;y<b.length;y++){var w=b[y];(!(x=Object.getOwnPropertyDescriptor(l,w))||x.enumerable||u)&&(g[w]=i(l[w],h-1),Object.defineProperty(g,w,x))}}if(u){var _=Object.getOwnPropertyNames(l);for(y=0;y<_.length;y++){var x,S=_[y];(x=Object.getOwnPropertyDescriptor(l,S))&&x.enumerable||(g[S]=i(l[S],h-1),Object.defineProperty(g,S,x))}}return g}(i,l)}function i(e){return Object.prototype.toString.call(e)}function s(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=i,r.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},r.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},r.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},r.__getRegExpFlags=s,r}();function Vm(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function Wm(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function qm(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function zm(e){return"[object Object]"===Object.prototype.toString.call(e)}function Um(e){return"function"==typeof e}const Xm=Object.freeze(Object.defineProperty({__proto__:null,amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},array:qm,carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},contains:function(e,t){return e.indexOf(t)>=0},date:function(e){return!!e&&(Vm(e)&&(e=+e),!/Invalid|NaN/.test(new Date(e).toString()))},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},digits:function(e){return/^\d+$/.test(e)},email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},empty:Wm,enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},func:Um,idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},image:function(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)},jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},mobile:function(e){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(e)},number:Vm,object:zm,promise:function(e){return zm(e)&&Um(e.then)&&Um(e.catch)},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}},Symbol.toStringTag,{value:"Module"}));function Ym(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function Gm(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function Zm(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=Gm(e);return t>0?Ym(Number(e)*Math.pow(10,t)):Number(e)}function Jm(e){(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&console.warn(`${e} 超出了精度限制，结果可能不正确`)}function Km(e,t){const[n,o,...r]=e;let i=t(n,o);return r.forEach((e=>{i=t(i,e)})),i}function Qm(...e){if(e.length>2)return Km(e,Qm);const[t,n]=e,o=Zm(t),r=Zm(n),i=Gm(t)+Gm(n),s=o*r;return Jm(s),s/Math.pow(10,i)}function ev(...e){if(e.length>2)return Km(e,ev);const[t,n]=e,o=Zm(t),r=Zm(n);return Jm(o),Jm(r),Qm(o/r,Ym(Math.pow(10,Gm(n)-Gm(t))))}function tv(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function nv(e,t=new WeakMap){if(null===e||"object"!=typeof e)return e;if(t.has(e))return t.get(e);let n;if(e instanceof Date)n=new Date(e.getTime());else if(e instanceof RegExp)n=new RegExp(e);else if(e instanceof Map)n=new Map(Array.from(e,(([e,n])=>[e,nv(n,t)])));else if(e instanceof Set)n=new Set(Array.from(e,(e=>nv(e,t))));else if(Array.isArray(e))n=e.map((e=>nv(e,t)));else if("[object Object]"===Object.prototype.toString.call(e)){n=Object.create(Object.getPrototypeOf(e)),t.set(e,n);for(const[o,r]of Object.entries(e))n[o]=nv(r,t)}else n=Object.assign({},e);return t.set(e,n),n}function ov(e={},t={}){if("object"!=typeof(e=nv(e))||null===e||"object"!=typeof t||null===t)return e;const n=Array.isArray(e)?e.slice():Object.assign({},e);for(const o in t){if(!t.hasOwnProperty(o))continue;const e=t[o],r=n[o];e instanceof Date?n[o]=new Date(e):e instanceof RegExp?n[o]=new RegExp(e):e instanceof Map?n[o]=new Map(e):e instanceof Set?n[o]=new Set(e):n[o]="object"==typeof e&&null!==e?ov(r,e):e}return n}function rv(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(null==e?void 0:e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):"string"==typeof e&&e.includes("-")&&!e.includes("T")?new Date(e.replace(/-/g,"/")):new Date(e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const r in o){const[e]=new RegExp(`${r}+`).exec(t)||[];if(e){const n="y"===r&&2===e.length?2:0;t=t.replace(e,o[r].slice(n))}}return t}function iv(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function sv(e={},t=!0,n="brackets"){const o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const i in e){const t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(`${i}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{r.push(`${i}[]=${e}`)}));break;case"repeat":t.forEach((e=>{r.push(`${i}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(`${i}=${e}`)}else r.push(`${i}=${t}`)}return r.length?o+r.join("&"):""}function av(){var e;const t=kf(),n=null==(e=t[t.length-1])?void 0:e.route;return`/${n||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const lv=Object.freeze(Object.defineProperty({__proto__:null,$parent:tv,addStyle:function(e,t="object"){if(Wm(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=iv(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[iv(o[0])]=iv(o[1])}return n}let n="";for(const o in e){n+=`${o.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[o]};`}return iv(n)},addUnit:function(e="auto",t=((e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})()?(e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})():"px")){return Vm(e=String(e))?`${e}${t}`:e},deepClone:nv,deepMerge:ov,error:function(e){},formValidate:function(e,t){const n=tv.call(e,"uv-form-item"),o=tv.call(e,"uv-form");n&&o&&o.validateField(n.prop,(()=>{}),t)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},getHistoryPage:function(e=0){const t=kf();return t[t.length-1+e]},getProperty:function(e,t){if(e){if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}},getPx:function(e,t=!1){return Vm(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${td(parseInt(e))}px`:Number(td(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},guid:function(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),`u${r.join("")}`):r.join("")},os:function(){return Qh().platform.toLowerCase()},padZero:function(e){return`00${e}`.slice(-2)},page:av,pages:function(){return kf()},priceFormat:function(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const r=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,s=void 0===o?",":o,a=void 0===n?".":n;let l="";l=(i?function(e,t){const n=Math.pow(10,t);let o=ev(Math.round(Math.abs(Qm(e,n))),n);return e<0&&0!==o&&(o=Qm(o,-1)),o}(r,i)+"":`${Math.round(r)}`).split(".");const c=/(-?\d+)(\d{3})/;for(;c.test(l[0]);)l[0]=l[0].replace(c,`$1${s}$2`);return(l[1]||"").length<i&&(l[1]=l[1]||"",l[1]+=new Array(i-l[1].length+1).join("0")),l.join(a)},queryParams:sv,random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},setConfig:function({props:e={},config:t={},color:n={},zIndex:o={}}){const{deepMerge:r}=uni.$uv;uni.$uv.config=r(uni.$uv.config,t),uni.$uv.props=r(uni.$uv.props,e),uni.$uv.color=r(uni.$uv.color,n),uni.$uv.zIndex=r(uni.$uv.zIndex,o)},setProperty:function(e,t,n){if(!e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const r=t[0];e[r]&&"object"==typeof e[r]||(e[r]={}),t.shift(),o(e[r],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const r=t.split(".");o(e,r,n)}else e[t]=n},sleep:function(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))},sys:function(){return Qh()},timeFormat:rv,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:rv(e,t)}return o},toast:function(e,t=2e3){Rg({title:String(e),icon:"none",duration:t})},trim:iv,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n}},Symbol.toStringTag,{value:"Module"}));const cv=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=sv(t,!1),e+`&${n}`):(n=sv(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=ov(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==av())if(t.intercept&&(n.intercept=t.intercept),n.params=t,n=ov(this.config,n),"function"==typeof n.intercept){await new Promise(((e,t)=>{n.intercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i,events:s}=e;"navigateTo"!=e.type&&"to"!=e.type||Eg({url:t,animationType:r,animationDuration:i,events:s}),"redirectTo"!=e.type&&"redirect"!=e.type||of({url:t}),"switchTab"!=e.type&&"tab"!=e.type||tf({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||sf({url:t}),"navigateBack"!=e.type&&"back"!=e.type||Cg({delta:o})}}).route;let uv,dv=null;function fv(e,t=500,n=!1){if(null!==dv&&clearTimeout(dv),n){const n=!dv;dv=setTimeout((()=>{dv=null}),t),n&&"function"==typeof e&&e()}else dv=setTimeout((()=>{"function"==typeof e&&e()}),t)}function pv(e,t=500,n=!0){n?uv||(uv=!0,"function"==typeof e&&e(),setTimeout((()=>{uv=!1}),t)):uv||(uv=!0,setTimeout((()=>{uv=!1,"function"==typeof e&&e()}),t))}const hv={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$uv.getRect=this.$uvGetRect},created(){this.$uv.getRect=this.$uvGetRect},computed:{$uv(){var e,t;return{...lv,test:Xm,route:cv,debounce:fv,throttle:pv,unit:null==(t=null==(e=null==uni?void 0:uni.$uv)?void 0:e.config)?void 0:t.unit}},bem:()=>function(e,t,n){const o=`uv-${e}--`,r={};return t&&t.map((e=>{r[o+this[e]]=!0})),n&&n.map((e=>{this[e]?r[o+e]=this[e]:delete r[o+e]})),Object.keys(r)}},methods:{openPage(e="url"){const t=this[e];t&&uni[this.linkType]({url:t})},$uvGetRect(e,t){return new Promise((n=>{_d().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){id("uvOnReachBottom")},beforeDestroy(){if(this.parent&&qm(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}},unmounted(){if(this.parent&&qm(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},gv={};function mv(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=vv(e,!1),r=o[0],i=o[1],s=o[2],a=vv(t,!1),l=(a[0]-r)/n,c=(a[1]-i)/n,u=(a[2]-s)/n,d=[];for(let f=0;f<n;f++){let o=yv(`rgb(${Math.round(l*f+r)},${Math.round(c*f+i)},${Math.round(u*f+s)})`);0===f&&(o=yv(e)),f===n-1&&(o=yv(t)),d.push(o)}return d}function vv(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function yv(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}let bv="none";bv="vue3",bv="h5";const wv={route:cv,config:{v:"1.1.20",version:"1.1.20",type:["primary","success","info","error","warning"],color:{"uv-primary":"#2979ff","uv-warning":"#ff9900","uv-success":"#19be6b","uv-error":"#fa3534","uv-info":"#909399","uv-main-color":"#303133","uv-content-color":"#606266","uv-tips-color":"#909399","uv-light-color":"#c0c4cc"},unit:"px"},test:Xm,date:rv,...lv,colorGradient:mv,hexToRgb:vv,rgbToHex:yv,colorToRgba:function(e,t){e=yv(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n},http:new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={},console.warn("设置全局参数必须接收一个Object")),this.config=Hm({...Dm,...e}),this.interceptors={request:new Bm,response:new Bm}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:t.baseURL||e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:Am(e.header||{},t.header||{})};if(o={...o,...Fm(["getTask","validateStatus","paramsSerializer","forcedJSONParsing"],e,t)},"DOWNLOAD"===n){const n=["timeout"];o={...o,...Fm(n,e,t)}}else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["files","file","filePath","name","timeout","formData"].forEach((e=>{Mm(t[e])||(o[e]=t[e])})),Mm(o.timeout)&&!Mm(e.timeout)&&(o.timeout=e.timeout);else{const n=["data","timeout","dataType","responseType","withCredentials"];o={...o,...Fm(n,e,t)}}return o})(this.config,e);let t=[Rm,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}get version(){return"3.1.0"}},debounce:fv,throttle:pv,platform:"h5",mixin:hv,mpMixin:gv};uni.$uv=wv;function _v(){return kh()}function xv(){return _v()&&_v().globalData}function Sv(e,t){try{setTimeout((function(){xv()&&(_v().globalData[`zp_handle${e}Callback`]=t)}),1)}catch(n){}}function Tv(e){return xv()?_v().globalData[`zp_handle${e}Callback`]:null}const kv={handleQuery:function(e){return Sv("Query",e),this},_handleQuery:function(e,t,n,o){const r=Tv("Query");return r?r(e,t,n,o):[e,t,n]},handleFetchParams:function(e){return Sv("FetchParams",e),this},_handleFetchParams:function(e,t){const n=Tv("FetchParams");return n?n(e,t||{}):{pageNo:e.pageNo,pageSize:e.pageSize,...t||{}}},handleFetchResult:function(e){return Sv("FetchResult",e),this},_handleFetchResult:function(e,t,n){const o=Tv("FetchResult");return o&&o(e,t,n),!!o},handleLanguage2Local:function(e){return Sv("Language2Local",e),this},_handleLanguage2Local:function(e,t){const n=Tv("Language2Local");return n?n(e,t):t}};kv.handleFetchParams(((e,t)=>({pageNo:e.pageNo,pageSize:e.pageSize,...t}))).handleFetchResult(((e,t)=>{e.then((e=>{t.complete(e.data.list)})).catch((e=>{t.complete(!1)}))})),Os(Om).use(vh).mount("#app");export{rd as $,er as A,uo as B,ye as C,ve as D,Qi as E,qr as F,hm as G,gm as H,bm as I,Np as J,gv as K,hv as L,mv as M,kf as N,Qp as O,pv as P,Pi as Q,hg as R,cg as S,ig as T,Up as U,Cd as V,Zn as W,km as X,Cm as Y,Bh as Z,Zp as _,od as a,Yp as a0,Hh as a1,Yo as a2,Wg as a3,Sm as a4,_m as a5,Fg as a6,ym as a7,Dg as a8,vm as a9,eg as aa,ng as ab,td as ac,hf as ad,Qh as ae,kv as af,gl as ag,Ed as ah,zg as ai,qg as aj,Ci as ak,Ho as al,wi as am,re as an,ie as ao,se as ap,ae as aq,id as b,xm as c,wm as d,Kn as e,ti as f,li as g,ui as h,di as i,ei as j,Qo as k,fm as l,Tm as m,sf as n,Zr as o,gp as p,eh as q,un as r,pm as s,G as t,kh as u,kg as v,zn as w,uu as x,_d as y,ks as z};
