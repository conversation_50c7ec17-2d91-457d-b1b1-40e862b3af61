<?php

namespace app\shopapi\controller;

use think\facade\Db;
use app\common\server\UrlServer;

/**
 * 代理/商家相关接口
 */
class AgentController extends BaseController
{
    /**
     * @notes 获取分配的采购人员列表
     * <AUTHOR>
     * @date 2024/05/22
     */
    public function purchaserList()
    {
        $merchantUserId = $this->user_id;
        $currentYear = date('Y');

        $params = $this->request->get();
        $limit = $params['limit'] ?? 15;
        $page = $params['page'] ?? 1;

        try {
            $query = Db::name('merchant_purchaser_relation')->alias('r')
                ->join('user u', 'r.purchaser_user_id = u.id')
                ->where('r.merchant_user_id', $merchantUserId)
                ->where('r.assign_year', $currentYear);

            $count = $query->count();

            $lists = $query->clone()->page($page, $limit)
                ->field('u.id, u.nickname, u.avatar, u.mobile')
                ->select()->each(function ($item) {
                    $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
                    return $item;
                });

            return $this->success('获取成功', [
                'count' => $count,
                'lists' => $lists,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }
}
