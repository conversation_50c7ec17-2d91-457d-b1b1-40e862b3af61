{"packages": [{"name": "adbario/php-dot-notation", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/eee4fc81296531e6aafba4c2bbccfc5adab1676e", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0|^6.0", "squizlabs/php_codesniffer": "^3.0"}, "time": "2019-01-01T23:59:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "install-path": "../adbario/php-dot-notation"}, {"name": "alibabacloud/alinlp-20200629", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/alinlp-20200629.git", "reference": "352bda8c22f09775f8964e3e8ae27b5a24c0571f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/alinlp-20200629/zipball/352bda8c22f09775f8964e3e8ae27b5a24c0571f", "reference": "352bda8c22f09775f8964e3e8ae27b5a24c0571f", "shasum": ""}, "require": {"alibabacloud/darabonba-openapi": "^0.2.11", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.20", "php": ">5.5"}, "time": "2024-06-14T10:04:57+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Alinlp\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud alinlp (20200629) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/alinlp-20200629/tree/3.1.0"}, "install-path": "../alibabacloud/alinlp-20200629"}, {"name": "alibabacloud/client", "version": "1.5.31", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "19224d92fe27ab8ef501d77d4891e7660bc023c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/19224d92fe27ab8ef501d77d4891e7660bc023c1", "reference": "19224d92fe27ab8ef501d77d4891e7660bc023c1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "clagiordano/weblibs-configmanager": "^1.0", "danielstjules/stringy": "^3.1", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "mtdowling/jmespath.php": "^2.5", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7.27|^6.1", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "time": "2021-05-13T06:26:38+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Client\\": "src"}, "files": ["src/Functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "install-path": "../alibabacloud/client"}, {"name": "alibabacloud/credentials", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/aliyun/credentials-php.git", "reference": "ebcda2e628180b4df235b46a86e1d014c561f5d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/credentials-php/zipball/ebcda2e628180b4df235b46a86e1d014c561f5d9", "reference": "ebcda2e628180b4df235b46a86e1d014c561f5d9", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7|^6.6|^9.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "time": "2024-10-16T13:29:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Credentials\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Credentials for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "credentials", "library", "sdk", "tool"], "support": {"issues": "https://github.com/aliyun/credentials-php/issues", "source": "https://github.com/aliyun/credentials-php"}, "install-path": "../alibabacloud/credentials"}, {"name": "alibabacloud/darabonba-openapi", "version": "0.2.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba-openapi.git", "reference": "0213396384e2c064eefd614f3dd53636a63f987f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba-openapi/zipball/0213396384e2c064eefd614f3dd53636a63f987f", "reference": "0213396384e2c064eefd614f3dd53636a63f987f", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.1", "alibabacloud/gateway-spi": "^1", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.21", "alibabacloud/tea-xml": "^0.2", "php": ">5.5"}, "time": "2024-07-15T13:11:36+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client", "support": {"issues": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/issues", "source": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/tree/0.2.13"}, "install-path": "../alibabacloud/darabonba-openapi"}, {"name": "alibabacloud/endpoint-util", "version": "0.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/endpoint-util.git", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/endpoint-util/zipball/f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "shasum": ""}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "time": "2020-06-04T10:57:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Endpoint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Endpoint Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/endpoint-util/tree/0.1.1"}, "install-path": "../alibabacloud/endpoint-util"}, {"name": "alibabacloud/gateway-spi", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi.git", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/alibabacloud-gateway-spi/zipball/7440f77750c329d8ab252db1d1d967314ccd1fcb", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.1", "php": ">5.5"}, "time": "2022-07-14T05:31:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Darabonba\\GatewaySpi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Gateway SPI Client", "support": {"source": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi/tree/1.0.0"}, "install-path": "../alibabacloud/gateway-spi"}, {"name": "alibabacloud/openapi-util", "version": "0.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-util.git", "reference": "f31f7bcd835e08ca24b6b8ba33637eb4eceb093a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-util/zipball/f31f7bcd835e08ca24b6b8ba33637eb4eceb093a", "reference": "f31f7bcd835e08ca24b6b8ba33637eb4eceb093a", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-utils": "^0.2", "lizhichao/one-sm": "^1.5", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "*"}, "time": "2023-01-10T09:10:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\OpenApiUtil\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi <PERSON>", "support": {"issues": "https://github.com/alibabacloud-sdk-php/openapi-util/issues", "source": "https://github.com/alibabacloud-sdk-php/openapi-util/tree/0.2.1"}, "install-path": "../alibabacloud/openapi-util"}, {"name": "alibabacloud/tea", "version": "3.1.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "f9c9b2c927253a1c23a5381cc655e41311be7f65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/f9c9b2c927253a1c23a5381cc655e41311be7f65", "reference": "f9c9b2c927253a1c23a5381cc655e41311be7f65", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "time": "2021-05-11T06:17:44+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "install-path": "../alibabacloud/tea"}, {"name": "alibabacloud/tea-fileform", "version": "0.3.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-fileform.git", "reference": "4bf0c75a045c8115aa8cb1a394bd08d8bb833181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-fileform/zipball/4bf0c75a045c8115aa8cb1a394bd08d8bb833181", "reference": "4bf0c75a045c8115aa8cb1a394bd08d8bb833181", "shasum": ""}, "require": {"alibabacloud/tea": "^3.0", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "time": "2020-12-01T07:24:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\FileForm\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea File Library for PHP", "support": {"issues": "https://github.com/alibabacloud-sdk-php/tea-fileform/issues", "source": "https://github.com/alibabacloud-sdk-php/tea-fileform/tree/0.3.4"}, "install-path": "../alibabacloud/tea-fileform"}, {"name": "alibabacloud/tea-utils", "version": "0.2.21", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-utils.git", "reference": "5039e45714c6456186d267f5d81a4b260a652495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-utils/zipball/5039e45714c6456186d267f5d81a4b260a652495", "reference": "5039e45714c6456186d267f5d81a4b260a652495", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "php": ">5.5"}, "time": "2024-07-05T06:05:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\Utils\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea Utils for PHP", "support": {"issues": "https://github.com/aliyun/tea-util/issues", "source": "https://github.com/aliyun/tea-util"}, "install-path": "../alibabacloud/tea-utils"}, {"name": "alibabacloud/tea-xml", "version": "0.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-xml.git", "reference": "3e0c000bf536224eebbac913c371bef174c0a16a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-xml/zipball/3e0c000bf536224eebbac913c371bef174c0a16a", "reference": "3e0c000bf536224eebbac913c371bef174c0a16a", "shasum": ""}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/var-dumper": "*"}, "time": "2022-08-02T04:12:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\XML\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea XML Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/tea-xml/tree/0.2.4"}, "install-path": "../alibabacloud/tea-xml"}, {"name": "alipaysdk/easysdk", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alipay/alipay-easysdk.git", "reference": "7a1cfa83c7e140bded957498ea072c77611e6480"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alipay/alipay-easysdk/zipball/7a1cfa83c7e140bded957498ea072c77611e6480", "reference": "7a1cfa83c7e140bded957498ea072c77611e6480", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.1", "alibabacloud/tea-fileform": "^0.3.2", "danielstjules/stringy": "^3.1", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": ">=6.3", "mtdowling/jmespath.php": "^2.4", "php": ">=7.0", "pimple/pimple": "^3.0", "psr/log": "^1.1", "songshenzong/support": "^2.0", "xin/container": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "time": "2021-01-19T07:30:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Alipay\\EasySDK\\": "php/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "junying.wjy", "email": "<EMAIL>"}], "description": "支付宝官方 Alipay Easy SDK", "support": {"issues": "https://github.com/alipay/alipay-easysdk/issues", "source": "https://github.com/alipay/alipay-easysdk/tree/v2.2.0"}, "install-path": "../alipaysdk/easysdk"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "0c9d902c33847c07efc66c4cdf823deaea8fc2b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/0c9d902c33847c07efc66c4cdf823deaea8fc2b6", "reference": "0c9d902c33847c07efc66c4cdf823deaea8fc2b6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*", "satooshi/php-coveralls": "*"}, "time": "2021-06-04T06:55:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.4.2"}, "install-path": "../aliyuncs/oss-sdk-php"}, {"name": "bacon/bacon-qr-code", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "f73543ac4e1def05f1a70bcd1525c8a157a1ad09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/f73543ac4e1def05f1a70bcd1525c8a157a1ad09", "reference": "f73543ac4e1def05f1a70bcd1525c8a157a1ad09", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^1.4", "phpunit/phpunit": "^7 | ^8 | ^9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "time": "2021-06-18T13:26:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/2.0.4"}, "install-path": "../bacon/bacon-qr-code"}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "time": "2023-12-11T17:09:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "install-path": "../carbonphp/carbon-doctrine-types"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "ecf584f5b3a27929175ff0abdba52f0131bef795"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/ecf584f5b3a27929175ff0abdba52f0131bef795", "reference": "ecf584f5b3a27929175ff0abdba52f0131bef795", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4"}, "require-dev": {"clagiordano/phpunit-result-printer": "^1", "phpunit/phpunit": "^4.8"}, "time": "2020-07-20T20:39:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "install-path": "../clagiordano/weblibs-configmanager"}, {"name": "danielst<PERSON>les/stringy", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "time": "2017-06-12T01:10:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "install-path": "../danielstjules/stringy"}, {"name": "dasprid/enum", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "5abf82f213618696dda8e3bf6f64dd042d8542b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/5abf82f213618696dda8e3bf6f64dd042d8542b2", "reference": "5abf82f213618696dda8e3bf6f64dd042d8542b2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"phpunit/phpunit": "^7 | ^8 | ^9", "squizlabs/php_codesniffer": "^3.4"}, "time": "2020-10-02T16:03:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.3"}, "install-path": "../dasprid/enum"}, {"name": "dragonmantank/cron-expression", "version": "v3.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "be85b3f05b46c39bbc0d95f6c071ddff669510fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/be85b3f05b46c39bbc0d95f6c071ddff669510fa", "reference": "be85b3f05b46c39bbc0d95f6c071ddff669510fa", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "time": "2022-01-18T15:43:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.1"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "install-path": "../dragonmantank/cron-expression"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-composer/easywechat-composer", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mingyoung/easywechat-composer.git", "reference": "93cfce1ec842b9a5b1b0791a52afd18b833f114a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mingyoung/easywechat-composer/zipball/93cfce1ec842b9a5b1b0791a52afd18b833f114a", "reference": "93cfce1ec842b9a5b1b0791a52afd18b833f114a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=7.0"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "phpunit/phpunit": "^6.5 || ^7.0"}, "time": "2020-07-23T11:06:47+00:00", "type": "composer-plugin", "extra": {"class": "EasyWeChatComposer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"EasyWeChatComposer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "张铭阳", "email": "mingyoung<PERSON><EMAIL>"}], "description": "The composer plugin for EasyWeChat", "install-path": "../easywechat-composer/easywechat-composer"}, {"name": "endroid/qr-code", "version": "3.9.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "9cdd4f5d609bfc8811ca4a62b4d23eb16976242f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/endroid/qr-code/zipball/9cdd4f5d609bfc8811ca4a62b4d23eb16976242f", "reference": "9cdd4f5d609bfc8811ca4a62b4d23eb16976242f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"bacon/bacon-qr-code": "^2.0", "khanamiryan/qrcode-detector-decoder": "^1.0.2", "myclabs/php-enum": "^1.5", "php": ">=7.2", "symfony/options-resolver": "^3.4||^4.4||^5.0", "symfony/property-access": "^3.4||^4.4||^5.0"}, "require-dev": {"endroid/quality": "^1.3.7", "setasign/fpdf": "^1.8"}, "suggest": {"ext-gd": "Required for generating PNG images", "roave/security-advisories": "Avoids installation of package versions with vulnerabilities", "setasign/fpdf": "Required to use the FPDF writer.", "symfony/security-checker": "Checks your composer.lock for vulnerabilities"}, "time": "2020-11-27T14:30:38+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/qr-code", "keywords": ["bundle", "code", "endroid", "php", "qr", "qrcode"], "support": {"issues": "https://github.com/endroid/qr-code/issues", "source": "https://github.com/endroid/qr-code/tree/3.9.6"}, "funding": [{"url": "https://github.com/endroid", "type": "github"}], "install-path": "../endroid/qr-code"}, {"name": "ezyang/htmlpurifier", "version": "v4.14.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.2"}, "time": "2021-12-25T01:21:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.14.0"}, "install-path": "../ezyang/htmlpurifier"}, {"name": "guzzlehttp/command", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/command.git", "reference": "2aaa2521a8f8269d6f5dfc13fe2af12c76921034"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/command/zipball/2aaa2521a8f8269d6f5dfc13fe2af12c76921034", "reference": "2aaa2521a8f8269d6f5dfc13fe2af12c76921034", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.2", "guzzlehttp/promises": "~1.3", "guzzlehttp/psr7": "~1.0", "php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "time": "2016-11-24T13:34:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "0.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}], "description": "Provides the foundation for building command-based web service clients", "support": {"issues": "https://github.com/guzzle/command/issues", "source": "https://github.com/guzzle/command/tree/1.0.0"}, "install-path": "../guzzlehttp/command"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "time": "2020-06-16T21:01:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/guzzle-services", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle-services.git", "reference": "9e3abf20161cbf662d616cbb995f2811771759f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle-services/zipball/9e3abf20161cbf662d616cbb995f2811771759f7", "reference": "9e3abf20161cbf662d616cbb995f2811771759f7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/command": "~1.0", "guzzlehttp/guzzle": "^6.2", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"gimler/guzzle-description-loader": "^0.0.4"}, "time": "2017-10-06T14:32:02+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Command\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/konafets"}], "description": "Provides an implementation of the Guzzle Command library that uses Guzzle service descriptions to describe web services, serialize requests, and parse responses into easy to use model structures.", "support": {"issues": "https://github.com/guzzle/guzzle-services/issues", "source": "https://github.com/guzzle/guzzle-services/tree/1.1.3"}, "install-path": "../guzzlehttp/guzzle-services"}, {"name": "guzzlehttp/promises", "version": "1.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/8e7d04f1f6450fef59366c399cfad4b9383aa30d", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "time": "2021-03-07T09:25:29+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "dc960a912984efb74d0a90222870c72c87f10c91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/dc960a912984efb74d0a90222870c72c87f10c91", "reference": "dc960a912984efb74d0a90222870c72c87f10c91", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2021-04-26T09:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "install-path": "../guzzlehttp/psr7"}, {"name": "hhxsv5/php-sse", "version": "v2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/hhxsv5/php-sse.git", "reference": "4244e4e8b416103f585cafcf813cff81061c5752"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hhxsv5/php-sse/zipball/4244e4e8b416103f585cafcf813cff81061c5752", "reference": "4244e4e8b416103f585cafcf813cff81061c5752", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "~6.0", "swoole/ide-helper": "@dev"}, "time": "2021-03-04T09:49:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Hhxsv5\\SSE\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A simple and efficient library implemented HTML5's server-sent events by PHP, is used to real-time push events from server to client, and easier than Websocket, instead of AJAX request.", "homepage": "https://github.com/hhxsv5/php-sse", "keywords": ["Server-Sent Events", "event-stream", "events", "eventsource", "sever-events", "sse"], "support": {"issues": "https://github.com/hhxsv5/php-sse/issues", "source": "https://github.com/hhxsv5/php-sse/tree/v2.0.2"}, "install-path": "../hhxsv5/php-sse"}, {"name": "khanamiryan/qrcode-detector-decoder", "version": "*******", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/khanamiryan/php-qrcode-detector-decoder.git", "reference": "b96163d4f074970dfe67d4185e75e1f4541b30ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/khanamiryan/php-qrcode-detector-decoder/zipball/b96163d4f074970dfe67d4185e75e1f4541b30ca", "reference": "b96163d4f074970dfe67d4185e75e1f4541b30ca", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5.7 | ^7.5 | ^8.0 | ^9.0"}, "time": "2021-04-21T08:02:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Zxing\\": "lib/"}, "files": ["lib/Common/customFunctions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT", "Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "a<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "homepage": "https://github.com/khanamiryan", "role": "Developer"}], "description": "QR code decoder / reader", "homepage": "https://github.com/khanamiryan/php-qrcode-detector-decoder/", "keywords": ["barcode", "qr", "zxing"], "support": {"issues": "https://github.com/khanamiryan/php-qrcode-detector-decoder/issues", "source": "https://github.com/khanamiryan/php-qrcode-detector-decoder/tree/*******"}, "install-path": "../khanamiryan/qrcode-detector-decoder"}, {"name": "league/flysystem", "version": "1.0.70", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "585824702f534f8d3cf7fab7225e8466cc4b7493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/585824702f534f8d3cf7fab7225e8466cc4b7493", "reference": "585824702f534f8d3cf7fab7225e8466cc4b7493", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/phpspec": "^3.4 || ^4.0 || ^5.0 || ^6.0", "phpunit/phpunit": "^5.7.26"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "time": "2020-07-26T07:20:36+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "install-path": "../league/flysystem"}, {"name": "league/flysystem-cached-adapter", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-cached-adapter.git", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-cached-adapter/zipball/d1925efb2207ac4be3ad0c40b8277175f99ffaff", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"league/flysystem": "~1.0", "psr/cache": "^1.0.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7", "predis/predis": "~1.0", "tedivm/stash": "~0.12"}, "suggest": {"ext-phpredis": "Pure C implemented extension for PHP"}, "time": "2020-07-25T15:56:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\Cached\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "frank<PERSON><PERSON>e", "email": "<EMAIL>"}], "description": "An adapter decorator to enable meta-data caching.", "install-path": "../league/flysystem-cached-adapter"}, {"name": "lizhichao/one-sm", "version": "1.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/lizhichao/sm.git", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lizhichao/sm/zipball/687a012a44a5bfd4d9143a0234e1060543be455a", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a", "shasum": ""}, "require": {"php": ">=5.6"}, "time": "2021-05-26T06:19:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"OneSm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "国密sm3", "keywords": ["php", "sm3"], "support": {"issues": "https://github.com/lizhichao/sm/issues", "source": "https://github.com/lizhichao/sm/tree/1.10"}, "funding": [{"url": "https://www.vicsdf.com/img/w.jpg", "type": "custom"}, {"url": "https://www.vicsdf.com/img/z.jpg", "type": "custom"}], "install-path": "../lizhichao/one-sm"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "time": "2020-05-30T13:11:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/master"}, "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "install-path": "../maennchen/zipstream-php"}, {"name": "markbaker/complex", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/ab8bc271e404909db09ff2d5ffa1e538085c0f22", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-06-29T15:32:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.1"}, "install-path": "../markbaker/complex"}, {"name": "markbaker/matrix", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/c66aefcafb4f6c269510e9ac46b82619a904c576", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-07-01T19:01:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.0"}, "install-path": "../markbaker/matrix"}, {"name": "meilisearch/meilisearch-php", "version": "v1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/meilisearch/meilisearch-php.git", "reference": "4dd127cb87848f7a7b28e83bb355b4b86d329867"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/meilisearch/meilisearch-php/zipball/4dd127cb87848f7a7b28e83bb355b4b86d329867", "reference": "4dd127cb87848f7a7b28e83bb355b4b86d329867", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4 || ^8.0", "php-http/discovery": "^1.7", "psr/http-client": "^1.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.8.1", "http-interop/http-factory-guzzle": "^1.2.0", "php-cs-fixer/shim": "^3.59.3", "phpstan/extension-installer": "^1.4.1", "phpstan/phpstan": "^1.11.5", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpunit/phpunit": "^9.5 || ^10.5"}, "suggest": {"guzzlehttp/guzzle": "Use Guzzle ^7 as HTTP client", "http-interop/http-factory-guzzle": "Factory for guzzlehttp/guzzle"}, "time": "2024-10-28T14:04:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MeiliSearch\\": "src/", "Meilisearch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP wrapper for the Meilisearch API", "keywords": ["api", "client", "instant", "meilisearch", "php", "search"], "support": {"issues": "https://github.com/meilisearch/meilisearch-php/issues", "source": "https://github.com/meilisearch/meilisearch-php/tree/v1.11.0"}, "install-path": "../meilisearch/meilisearch-php"}, {"name": "monolog/monolog", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <7.0.1", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2020-12-14T13:15:25+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "install-path": "../monolog/monolog"}, {"name": "mtdowling/jmespath.php", "version": "2.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/42dae2cbd13154083ca6d70099692fef8ca84bfb", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "time": "2020-07-31T21:01:56+00:00", "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "install-path": "../mtdowling/jmespath.php"}, {"name": "myclabs/php-enum", "version": "1.7.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "time": "2020-11-14T18:14:52+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.7"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "install-path": "../myclabs/php-enum"}, {"name": "nesbot/carbon", "version": "2.73.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9228ce90e1035ff2f0db84b40ec2e023ed802075", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "time": "2025-01-08T20:10:23+00:00", "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "install-path": "../nesbot/carbon"}, {"name": "nette/php-generator", "version": "v3.5.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "59bb35ed6e8da95854fbf7b7d47dce6156b42915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/59bb35ed6e8da95854fbf7b7d47dce6156b42915", "reference": "59bb35ed6e8da95854fbf7b7d47dce6156b42915", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nette/utils": "^3.1.2", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "nikic/php-parser": "^4.4", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "suggest": {"nikic/php-parser": "to use ClassType::withBodiesFrom() & GlobalFunction::withBodyFrom()"}, "time": "2021-07-05T12:02:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.5-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.0 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v3.5.4"}, "install-path": "../nette/php-generator"}, {"name": "nette/utils", "version": "v3.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "5c36cc1ba9bb6abb8a9e425cf054e0c3fd5b9822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/5c36cc1ba9bb6abb8a9e425cf054e0c3fd5b9822", "reference": "5c36cc1ba9bb6abb8a9e425cf054e0c3fd5b9822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2 <8.1"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "time": "2021-08-16T21:05:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.3"}, "install-path": "../nette/utils"}, {"name": "open-smf/connection-pool", "version": "v1.0.16", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/open-smf/connection-pool.git", "reference": "f70e47dbf56f1869d3207e15825cf38810b865e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/open-smf/connection-pool/zipball/f70e47dbf56f1869d3207e15825cf38810b865e0", "reference": "f70e47dbf56f1869d3207e15825cf38810b865e0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-swoole": ">=4.2.9", "php": ">=7.0.0"}, "require-dev": {"swoole/ide-helper": "@dev"}, "suggest": {"ext-redis": "A PHP extension for Redis."}, "time": "2021-03-01T04:13:24+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Smf\\ConnectionPool\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A common connection pool based on Swoole is usually used as the database connection pool.", "homepage": "https://github.com/open-smf/connection-pool", "keywords": ["connection-pool", "database-connection-pool", "swoole"], "support": {"issues": "https://github.com/open-smf/connection-pool/issues", "source": "https://github.com/open-smf/connection-pool"}, "install-path": "../open-smf/connection-pool"}, {"name": "overtrue/socialite", "version": "2.0.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/overtrue/socialite.git", "reference": "0bc60597b589592243f074a4d9016aabd2e9cfb2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/socialite/zipball/0bc60597b589592243f074a4d9016aabd2e9cfb2", "reference": "0bc60597b589592243f074a4d9016aabd2e9cfb2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^5.0|^6.0|^7.0", "php": ">=5.6", "symfony/http-foundation": "^2.7|^3.0|^4.0|^5.0"}, "require-dev": {"mockery/mockery": "~1.2", "phpunit/phpunit": "~6"}, "time": "2020-12-14T03:30:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Overtrue\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A collection of OAuth 2 packages that extracts from laravel/socialite.", "keywords": ["login", "o<PERSON>h", "qq", "social", "wechat", "weibo"], "install-path": "../overtrue/socialite"}, {"name": "overtrue/wechat", "version": "4.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/w7corp/easywechat.git", "reference": "a31939c7393a192d1095c280ee3be254bb38e279"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/w7corp/easywechat/zipball/a31939c7393a192d1095c280ee3be254bb38e279", "reference": "a31939c7393a192d1095c280ee3be254bb38e279", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"easywechat-composer/easywechat-composer": "^1.1", "ext-fileinfo": "*", "ext-openssl": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.2 || ^7.0", "monolog/monolog": "^1.22 || ^2.0", "overtrue/socialite": "~2.0", "php": ">=7.2", "pimple/pimple": "^3.0", "psr/simple-cache": "^1.0", "symfony/cache": "^3.3 || ^4.3 || ^5.0", "symfony/event-dispatcher": "^4.3 || ^5.0", "symfony/http-foundation": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/psr-http-message-bridge": "^0.3 || ^1.0 || ^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2.3", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^7.5"}, "time": "2021-05-18T11:57:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"EasyWeChat\\": "src/"}, "files": ["src/Kernel/Support/Helpers.php", "src/Kernel/Helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "微信SDK", "keywords": ["easywechat", "sdk", "wechat", "weixin", "weixin-sdk"], "support": {"issues": "https://github.com/w7corp/easywechat/issues", "source": "https://github.com/w7corp/easywechat/tree/4.4.1"}, "funding": [{"url": "https://www.easywechat.com/img/pay/wechat.jpg", "type": "custom"}, {"url": "https://github.com/overtrue", "type": "github"}, {"url": "https://www.patreon.com/overtrue", "type": "patreon"}], "install-path": "../overtrue/wechat"}, {"name": "php-http/discovery", "version": "1.20.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "time": "2024-10-02T11:20:13+00:00", "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "installation-source": "dist", "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "install-path": "../php-http/discovery"}, {"name": "phpoffice/phpspreadsheet", "version": "1.19.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.2 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0", "friendsofphp/php-cs-fixer": "^2.18", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^0.12.82", "phpstan/phpstan-phpunit": "^0.12.18", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "time": "2021-10-31T15:09:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.19.0"}, "install-path": "../phpoffice/phpspreadsheet"}, {"name": "pimple/pimple", "version": "v3.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "9e403941ef9d65d20cba7d54e29fe906db42cf32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/9e403941ef9d65d20cba7d54e29fe906db42cf32", "reference": "9e403941ef9d65d20cba7d54e29fe906db42cf32", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0", "psr/container": "^1.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.2"}, "time": "2018-01-21T07:42:36+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"Pimple": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "http://pimple.sensiolabs.org", "keywords": ["container", "dependency injection"], "install-path": "../pimple/pimple"}, {"name": "psr/cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T20:24:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "install-path": "../psr/cache"}, {"name": "psr/clock", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0"}, "time": "2022-11-25T14:36:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "install-path": "../psr/clock"}, {"name": "psr/container", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2017-02-14T16:28:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "install-path": "../psr/container"}, {"name": "psr/http-client", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "time": "2020-06-29T06:28:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "time": "2019-04-30T12:38:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2020-03-23T09:12:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "install-path": "../psr/simple-cache"}, {"name": "qcloud/cos-sdk-v5", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/tencentyun/cos-php-sdk-v5.git", "reference": "e67ad8143695192ee206bcbcafc78c08da92c621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentyun/cos-php-sdk-v5/zipball/e67ad8143695192ee206bcbcafc78c08da92c621", "reference": "e67ad8143695192ee206bcbcafc78c08da92c621", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "~6.3", "guzzlehttp/guzzle-services": "~1.1", "php": ">=5.3.0"}, "time": "2021-05-18T12:47:31+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Qcloud\\Cos\\": "src/Qcloud/Cos/"}, "files": ["src/Qcloud/Cos/Common.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "yaozongyou", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP SDK for QCloud COS", "keywords": ["cos", "php", "qcloud"], "support": {"issues": "https://github.com/tencentyun/cos-php-sdk-v5/issues", "source": "https://github.com/tencentyun/cos-php-sdk-v5/tree/v2.2.0"}, "install-path": "../qcloud/cos-sdk-v5"}, {"name": "qiniu/php-sdk", "version": "v7.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "0a461e13b09545b23df361843c6a65fdd3a26426"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/0a461e13b09545b23df361843c6a65fdd3a26426", "reference": "0a461e13b09545b23df361843c6a65fdd3a26426", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.3"}, "time": "2020-09-24T07:31:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Qiniu\\": "src/<PERSON>iu"}, "files": ["src/Qiniu/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "support": {"issues": "https://github.com/qiniu/php-sdk/issues", "source": "https://github.com/qiniu/php-sdk/tree/v7.3.0"}, "install-path": "../qiniu/php-sdk"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "install-path": "../ralouphie/getallheaders"}, {"name": "rmccue/requests", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/WordPress/Requests.git", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/Requests/zipball/82e6936366eac3af4d836c18b9d8c31028fe4cd5", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.2"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5", "requests/test-server": "dev-master", "squizlabs/php_codesniffer": "^3.5", "wp-coding-standards/wpcs": "^2.0"}, "time": "2021-06-04T09:56:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Requests": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "http://ryanmccue.info"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "http://github.com/WordPress/Requests", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "support": {"issues": "https://github.com/WordPress/Requests/issues", "source": "https://github.com/WordPress/Requests/tree/v1.8.1"}, "install-path": "../rmccue/requests"}, {"name": "setasign/fpdf", "version": "1.8.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDF.git", "reference": "0838e0ee4925716fcbbc50ad9e1799b5edfae0a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDF/zipball/0838e0ee4925716fcbbc50ad9e1799b5edfae0a0", "reference": "0838e0ee4925716fcbbc50ad9e1799b5edfae0a0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-gd": "*", "ext-zlib": "*"}, "time": "2023-06-26T14:44:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["fpdf.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://fpdf.org/"}], "description": "FPDF is a PHP class which allows to generate PDF files with pure PHP. F from FPDF stands for Free: you may use it for any kind of usage and modify it to suit your needs.", "homepage": "http://www.fpdf.org", "keywords": ["fpdf", "pdf"], "support": {"source": "https://github.com/Setasign/FPDF/tree/1.8.6"}, "install-path": "../setasign/fpdf"}, {"name": "setasign/fpdi", "version": "v2.6.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "9e013b376939c0d4029f54150d2a16f3c67a5797"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/9e013b376939c0d4029f54150d2a16f3c67a5797", "reference": "9e013b376939c0d4029f54150d2a16f3c67a5797", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "time": "2024-12-10T13:12:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "install-path": "../setasign/fpdi"}, {"name": "songshenzong/support", "version": "2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/songshenzong/support.git", "reference": "34973c04ffcf226e503f1c3a69d30ac49f7621f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/songshenzong/support/zipball/34973c04ffcf226e503f1c3a69d30ac49f7621f6", "reference": "34973c04ffcf226e503f1c3a69d30ac49f7621f6", "shasum": ""}, "require": {"danielstjules/stringy": "^3.1", "ext-json": "*", "ext-simplexml": "*", "ext-xml": "*", "php": ">=5.5"}, "require-dev": {"laravel/framework": "^5.8", "phpunit/phpunit": "^4.8.35|^5.4.3"}, "time": "2019-08-29T01:59:12+00:00", "type": "library", "extra": {"laravel": {"providers": ["Songshenzong\\Support\\StringsServiceProvider"], "aliases": {"Strings": "Songshenzong\\Support\\StringsFacade"}}}, "installation-source": "dist", "autoload": {"psr-4": {"Songshenzong\\Support\\": "src/"}, "files": ["src/StringsHelpers.php", "src/BashEchoHelpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Songshenzong Support package.", "homepage": "http://songshenzong.com", "keywords": ["laravel", "support", "tools", "web"], "support": {"issues": "https://github.com/songshenzong/support/issues", "source": "https://github.com/songshenzong/support"}, "install-path": "../songshenzong/support"}, {"name": "stechstudio/backoff", "version": "1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/stechstudio/backoff.git", "reference": "816e46107a6be2e1072ba0ff2cb26034872dfa49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stechstudio/backoff/zipball/816e46107a6be2e1072ba0ff2cb26034872dfa49", "reference": "816e46107a6be2e1072ba0ff2cb26034872dfa49", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"phpunit/phpunit": "5.5.*"}, "time": "2020-12-26T14:57:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"STS\\Backoff\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP library providing retry functionality with multiple backoff strategies and jitter support", "support": {"issues": "https://github.com/stechstudio/backoff/issues", "source": "https://github.com/stechstudio/backoff/tree/1.2"}, "install-path": "../stechstudio/backoff"}, {"name": "swoole/ide-helper", "version": "4.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "918a98b5b264425fdb59461d9bbd7f9b504ead71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/918a98b5b264425fdb59461d9bbd7f9b504ead71", "reference": "918a98b5b264425fdb59461d9bbd7f9b504ead71", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"guzzlehttp/guzzle": "~6.5.0", "laminas/laminas-code": "~3.4.0", "squizlabs/php_codesniffer": "~3.5.0", "symfony/filesystem": "~4.0"}, "time": "2021-08-19T17:25:57+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "support": {"issues": "https://github.com/swoole/ide-helper/issues", "source": "https://github.com/swoole/ide-helper/tree/4.7.1"}, "funding": [{"url": "https://gitee.com/swoole/swoole?donate=true", "type": "custom"}, {"url": "https://github.com/swoole", "type": "github"}], "install-path": "../swoole/ide-helper"}, {"name": "symfony/cache", "version": "v4.4.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "0da1df9b1a31f328f1711b5cd922c38a15c5fc74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/0da1df9b1a31f328f1711b5cd922c38a15c5fc74", "reference": "0da1df9b1a31f328f1711b5cd922c38a15c5fc74", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "psr/cache": "^1.0|^2.0", "psr/log": "~1.0", "symfony/cache-contracts": "^1.1.7|^2", "symfony/service-contracts": "^1.1|^2", "symfony/var-exporter": "^4.2|^5.0"}, "conflict": {"doctrine/dbal": "<2.6", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4|>=5.0", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6", "doctrine/dbal": "^2.6|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.1|^5.0", "symfony/filesystem": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/var-dumper": "^4.4|^5.0"}, "time": "2021-04-23T07:09:57+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "install-path": "../symfony/cache"}, {"name": "symfony/cache-contracts", "version": "v1.1.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "8d5489c10ef90aa7413e4921fc3c0520e24cbed7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/8d5489c10ef90aa7413e4921fc3c0520e24cbed7", "reference": "8d5489c10ef90aa7413e4921fc3c0520e24cbed7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "psr/cache": "^1.0"}, "suggest": {"symfony/cache-implementation": ""}, "time": "2020-09-02T16:08:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "install-path": "../symfony/cache-contracts"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/605389f2a7e5625f273b53960dc46aeaf9c62918", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "time": "2024-09-25T14:11:13+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/event-dispatcher", "version": "v4.4.20", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "c352647244bd376bf7d31efbd5401f13f50dad0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c352647244bd376bf7d31efbd5401f13f50dad0c", "reference": "c352647244bd376bf7d31efbd5401f13f50dad0c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "time": "2021-01-27T09:09:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/84e23fdcd2517bf37aecbd16967e83f0caee25a7", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "time": "2020-07-06T13:19:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/finder", "version": "v4.4.30", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "70362f1e112280d75b30087c7598b837c1b468b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/70362f1e112280d75b30087c7598b837c1b468b6", "reference": "70362f1e112280d75b30087c7598b837c1b468b6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "time": "2021-08-04T20:31:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.30"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/http-foundation", "version": "v4.4.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "1a6f87ef99d05b1bf5c865b4ef7992263e1cb081"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/1a6f87ef99d05b1bf5c865b4ef7992263e1cb081", "reference": "1a6f87ef99d05b1bf5c865b4ef7992263e1cb081", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "time": "2021-04-30T12:05:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "install-path": "../symfony/http-foundation"}, {"name": "symfony/inflector", "version": "v4.4.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "fc695ab721136b27aa84427a0fa80189761266ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/fc695ab721136b27aa84427a0fa80189761266ef", "reference": "fc695ab721136b27aa84427a0fa80189761266ef", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "time": "2021-05-26T17:39:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts words between their singular and plural forms (English only)", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "support": {"source": "https://github.com/symfony/inflector/tree/v4.4.25"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "use `EnglishInflector` from the String component instead", "install-path": "../symfony/inflector"}, {"name": "symfony/mime", "version": "v4.4.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "36f2e59c90762bb09170553130a4dc1934cada58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/36f2e59c90762bb09170553130a4dc1934cada58", "reference": "36f2e59c90762bb09170553130a4dc1934cada58", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "symfony/dependency-injection": "^3.4|^4.1|^5.0"}, "time": "2021-04-27T14:58:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "install-path": "../symfony/mime"}, {"name": "symfony/options-resolver", "version": "v4.4.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "2e607d627c70aa56284a02d34fc60dfe3a9a352e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/2e607d627c70aa56284a02d34fc60dfe3a9a352e", "reference": "2e607d627c70aa56284a02d34fc60dfe3a9a352e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3"}, "time": "2021-05-26T11:20:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v4.4.25"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/options-resolver"}, {"name": "symfony/polyfill-ctype", "version": "v1.23.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/46cd95797e9df938fdd2b03693b5fca5e64b01ce", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2021-02-19T12:13:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/2d63434d922daf7da8dd863e7907e67ee3031483", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2021-01-22T09:19:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/43a0283138253ed1d48d352ab6d0bdb3f809f248", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "time": "2021-01-22T09:19:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.22.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "f377a3dd1fde44d37b9831d68dc8dea3ffd28e13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/f377a3dd1fde44d37b9831d68dc8dea3ffd28e13", "reference": "f377a3dd1fde44d37b9831d68dc8dea3ffd28e13", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2021-01-07T16:49:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php72", "version": "v1.22.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "time": "2021-01-07T16:49:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-php72"}, {"name": "symfony/polyfill-php80", "version": "v1.22.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/dc3063ba22c2a1fd2f45ed856374d79114998f91", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "time": "2021-01-07T16:49:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/process", "version": "v5.4.47", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5d1662fb32ebc94f17ddb8d635454a776066733d", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "time": "2024-11-06T11:36:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/process"}, {"name": "symfony/property-access", "version": "v4.4.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "3af7c21b4128eadbace0800b51054a81bff896c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/3af7c21b4128eadbace0800b51054a81bff896c6", "reference": "3af7c21b4128eadbace0800b51054a81bff896c6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/cache": "^3.4|^4.0|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "time": "2021-05-26T17:39:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v4.4.25"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/property-access"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "81db2d4ae86e9f0049828d9343a72b9523884e5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/81db2d4ae86e9f0049828d9343a72b9523884e5d", "reference": "81db2d4ae86e9f0049828d9343a72b9523884e5d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1", "symfony/browser-kit": "^4.4 || ^5.0", "symfony/config": "^4.4 || ^5.0", "symfony/event-dispatcher": "^4.4 || ^5.0", "symfony/framework-bundle": "^4.4 || ^5.0", "symfony/http-kernel": "^4.4 || ^5.0", "symfony/phpunit-bridge": "^4.4.19 || ^5.2"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "time": "2021-02-17T10:35:25+00:00", "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "install-path": "../symfony/psr-http-message-bridge"}, {"name": "symfony/service-contracts", "version": "v1.1.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "b776d18b303a39f56c63747bcb977ad4b27aca26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/b776d18b303a39f56c63747bcb977ad4b27aca26", "reference": "b776d18b303a39f56c63747bcb977ad4b27aca26", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "time": "2020-07-06T13:19:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "install-path": "../symfony/service-contracts"}, {"name": "symfony/translation", "version": "v5.4.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/98f26acc99341ca4bab345fb14d7b1d7cb825bed", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "time": "2024-09-25T14:11:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v2.5.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "450d4172653f38818657022252f9d81be89ee9a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/450d4172653f38818657022252f9d81be89ee9a8", "reference": "450d4172653f38818657022252f9d81be89ee9a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "time": "2024-09-25T14:11:13+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation-contracts"}, {"name": "symfony/var-dumper", "version": "v4.4.18", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "4f31364bbc8177f2a6dbc125ac3851634ebe2a03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/4f31364bbc8177f2a6dbc125ac3851634ebe2a03", "reference": "4f31364bbc8177f2a6dbc125ac3851634ebe2a03", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "time": "2020-12-08T16:59:59+00:00", "bin": ["Resources/bin/var-dump-server"], "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "install-path": "../symfony/var-dumper"}, {"name": "symfony/var-exporter", "version": "v4.4.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "ef3054c7e878fe0837ef9ac2c5ecfddfd27dd9e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/ef3054c7e878fe0837ef9ac2c5ecfddfd27dd9e9", "reference": "ef3054c7e878fe0837ef9ac2c5ecfddfd27dd9e9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9"}, "time": "2021-04-01T10:24:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "install-path": "../symfony/var-exporter"}, {"name": "tencentcloud/tencentcloud-sdk-php", "version": "3.0.389", "version_normalized": "3.0.389.0", "source": {"type": "git", "url": "https://github.com/TencentCloud/tencentcloud-sdk-php.git", "reference": "c53cc9cd36061c0f90abf89682f209f2248dd8ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TencentCloud/tencentcloud-sdk-php/zipball/c53cc9cd36061c0f90abf89682f209f2248dd8ff", "reference": "c53cc9cd36061c0f90abf89682f209f2248dd8ff", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0.1", "guzzlehttp/psr7": "^1.4", "php": ">=5.6.0"}, "time": "2021-05-18T00:27:34+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/QcloudApi/QcloudApi.php"], "psr-4": {"TencentCloud\\": "./src/TencentCloud"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "coolli", "email": "<EMAIL>", "homepage": "https://cloud.tencent.com/document/sdk/PHP", "role": "Developer"}], "description": "TencentCloudApi php sdk", "homepage": "https://github.com/TencentCloud/tencentcloud-sdk-php", "install-path": "../tencentcloud/tencentcloud-sdk-php"}, {"name": "topthink/framework", "version": "v6.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "db8fe22520a9660dd5e4c87e304034ac49e39270"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/db8fe22520a9660dd5e4c87e304034ac49e39270", "reference": "db8fe22520a9660dd5e4c87e304034ac49e39270", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-mbstring": "*", "league/flysystem": "^1.0", "league/flysystem-cached-adapter": "^1.0", "php": ">=7.1.0", "psr/container": "~1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-helper": "^3.1.1", "topthink/think-orm": "^2.0"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^7.0"}, "time": "2021-01-25T14:48:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "install-path": "../topthink/framework"}, {"name": "topthink/think-captcha", "version": "v3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "1eef3717c1bcf4f5bbe2d1a1c704011d330a8b55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/1eef3717c1bcf4f5bbe2d1a1c704011d330a8b55", "reference": "1eef3717c1bcf4f5bbe2d1a1c704011d330a8b55", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/framework": "^6.0.0"}, "time": "2020-05-19T10:55:45+00:00", "type": "library", "extra": {"think": {"services": ["think\\captcha\\CaptchaService"], "config": {"captcha": "src/config.php"}}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\captcha\\": "src/"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp", "install-path": "../topthink/think-captcha"}, {"name": "topthink/think-helper", "version": "v3.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "c28d37743bda4a0455286ca85b17b5791d626e10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/c28d37743bda4a0455286ca85b17b5791d626e10", "reference": "c28d37743bda4a0455286ca85b17b5791d626e10", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0"}, "time": "2019-11-08T08:01:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "install-path": "../topthink/think-helper"}, {"name": "topthink/think-multi-app", "version": "v1.0.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-multi-app.git", "reference": "ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-multi-app/zipball/ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3", "reference": "ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0"}, "time": "2020-07-12T13:50:37+00:00", "type": "library", "extra": {"think": {"services": ["think\\app\\Service"]}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\app\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp6 multi app support", "install-path": "../topthink/think-multi-app"}, {"name": "topthink/think-orm", "version": "v2.0.36", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-orm.git", "reference": "f48dc09050f25029d41a66bfc9c3c403e4f82024"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-orm/zipball/f48dc09050f25029d41a66bfc9c3c403e4f82024", "reference": "f48dc09050f25029d41a66bfc9c3c403e4f82024", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-helper": "^3.1"}, "time": "2021-01-12T09:08:52+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}, "files": []}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think orm", "keywords": ["database", "orm"], "install-path": "../topthink/think-orm"}, {"name": "topthink/think-queue", "version": "v3.0.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-queue.git", "reference": "48adee0298a363f497b8ba07628d5b63cf020868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-queue/zipball/48adee0298a363f497b8ba07628d5b63cf020868", "reference": "48adee0298a363f497b8ba07628d5b63cf020868", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "nesbot/carbon": ">=2.16", "symfony/process": ">=4.2", "topthink/framework": "^6.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.2", "phpunit/phpunit": "^6.2", "topthink/think-migration": "^3.0"}, "time": "2025-03-15T08:30:16+00:00", "type": "library", "extra": {"think": {"config": {"queue": "src/config/queue.php"}, "services": ["think\\queue\\Service"]}}, "installation-source": "dist", "autoload": {"files": ["src/common.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Queue Package", "support": {"issues": "https://github.com/top-think/think-queue/issues", "source": "https://github.com/top-think/think-queue/tree/v3.0.12"}, "install-path": "../topthink/think-queue"}, {"name": "topthink/think-swoole", "version": "v3.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-swoole.git", "reference": "df78b1f6eb6cd8f45f49ab7b0d4cc65595181504"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-swoole/zipball/df78b1f6eb6cd8f45f49ab7b0d4cc65595181504", "reference": "df78b1f6eb6cd8f45f49ab7b0d4cc65595181504", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-swoole": ">=4.4.8", "nette/php-generator": "^3.2", "open-smf/connection-pool": "~1.0", "php": ">7.1", "stechstudio/backoff": "^1.2", "swoole/ide-helper": "^4.3", "symfony/finder": "^4.3.2|^5.1", "topthink/framework": "^6.0"}, "require-dev": {"symfony/var-dumper": "^4.3|^5.1", "topthink/think-queue": "^3.0", "topthink/think-tracing": "^1.0"}, "time": "2021-04-29T10:48:04+00:00", "type": "library", "extra": {"think": {"services": ["think\\swoole\\Service"], "config": {"swoole": "src/config/swoole.php"}}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\swoole\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "Swoole extend for thinkphp", "support": {"issues": "https://github.com/top-think/think-swoole/issues", "source": "https://github.com/top-think/think-swoole/tree/v3.1.3"}, "install-path": "../topthink/think-swoole"}, {"name": "topthink/think-template", "version": "v2.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-template.git", "reference": "abfc293f74f9ef5127b5c416310a01fe42e59368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-template/zipball/abfc293f74f9ef5127b5c416310a01fe42e59368", "reference": "abfc293f74f9ef5127b5c416310a01fe42e59368", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "psr/simple-cache": "^1.0"}, "time": "2020-12-10T07:52:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the php template engine", "install-path": "../topthink/think-template"}, {"name": "topthink/think-trace", "version": "v1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-trace.git", "reference": "9a9fa8f767b6c66c5a133ad21ca1bc96ad329444"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-trace/zipball/9a9fa8f767b6c66c5a133ad21ca1bc96ad329444", "reference": "9a9fa8f767b6c66c5a133ad21ca1bc96ad329444", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0"}, "time": "2020-06-29T05:27:28+00:00", "type": "library", "extra": {"think": {"services": ["think\\trace\\Service"], "config": {"trace": "src/config.php"}}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\trace\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp debug trace", "install-path": "../topthink/think-trace"}, {"name": "topthink/think-view", "version": "v1.0.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-view.git", "reference": "edce0ae2c9551ab65f9e94a222604b0dead3576d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-view/zipball/edce0ae2c9551ab65f9e94a222604b0dead3576d", "reference": "edce0ae2c9551ab65f9e94a222604b0dead3576d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "topthink/think-template": "^2.0"}, "time": "2019-11-06T11:40:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\view\\driver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp template driver", "install-path": "../topthink/think-view"}, {"name": "webmozart/assert", "version": "1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "time": "2022-06-03T18:03:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "install-path": "../webmozart/assert"}, {"name": "workerman/gateway-worker", "version": "v3.1.18", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/walkor/GatewayWorker.git", "reference": "8d371770cb0dbd8166b94d6049a6a497c13476df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/GatewayWorker/zipball/8d371770cb0dbd8166b94d6049a6a497c13476df", "reference": "8d371770cb0dbd8166b94d6049a6a497c13476df", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0", "workerman/workerman": "^4.0.30"}, "time": "2024-12-01T09:51:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GatewayWorker\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "keywords": ["communication", "distributed"], "support": {"issues": "https://github.com/walkor/GatewayWorker/issues", "source": "https://github.com/walkor/GatewayWorker/tree/v3.1.18"}, "funding": [{"url": "https://opencollective.com/walkor", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "install-path": "../workerman/gateway-worker"}, {"name": "workerman/workerman", "version": "v4.1.17", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/walkor/workerman.git", "reference": "bb9e4b0c3fc3931a2ae38a1fb7a3ac09246efae9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/workerman/zipball/bb9e4b0c3fc3931a2ae38a1fb7a3ac09246efae9", "reference": "bb9e4b0c3fc3931a2ae38a1fb7a3ac09246efae9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0"}, "suggest": {"ext-event": "For better performance. "}, "time": "2024-11-07T07:48:34+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "http://doc.workerman.net/"}, "funding": [{"url": "https://opencollective.com/workerman", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "install-path": "../workerman/workerman"}, {"name": "xin/container", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://gitee.com/liuxiaojinla/php-container", "reference": "97bb67f87dd851545938a1f2fe0ffbd379e3ff81"}, "require": {"ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "psr/container": "^1.0", "xin/helper": "^1.0"}, "time": "2019-10-21T03:51:25+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"xin\\container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "晋", "email": "<EMAIL>"}], "description": "严格基于PSR11规范实现基础的容器和依赖注入", "install-path": "../xin/container"}, {"name": "xin/helper", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://gitee.com/liuxiaojinla/php-helper", "reference": "02a58132dae2aea2d1c0b8e66f55125969224747"}, "require": {"ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*"}, "time": "2019-06-22T08:28:23+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"xin\\helper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "晋", "email": "<EMAIL>"}], "description": "PHP项目日常开发必备基础库，数组工具类、字符串工具类、数字工具类、函数工具类、服务器工具类、加密工具类", "install-path": "../xin/helper"}, {"name": "yansongda/pay", "version": "v2.10.5", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/yansongda/pay.git", "reference": "f7d93ed784de4ca09d3386d28139c724ddd526fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/pay/zipball/f7d93ed784de4ca09d3386d28139c724ddd526fc", "reference": "f7d93ed784de4ca09d3386d28139c724ddd526fc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-bcmath": "*", "ext-json": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-simplexml": "*", "php": ">=7.1.3", "symfony/event-dispatcher": "^4.0 || ^5.0", "symfony/http-foundation": "^4.0 || ^5.0.7", "yansongda/supports": "^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "mockery/mockery": "^1.2", "phpunit/phpunit": "^7.5"}, "time": "2022-12-03T13:44:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Yansongda\\Pay\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "专注 Alipay 和 WeChat 的支付扩展包", "keywords": ["alipay", "pay", "wechat"], "support": {"issues": "https://github.com/yansongda/pay/issues", "source": "https://github.com/yansongda/pay"}, "install-path": "../yansongda/pay"}, {"name": "yansongda/supports", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/yansongda/supports.git", "reference": "de9a8d38b0461ddf9c12f27390dad9a40c9b4e3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/supports/zipball/de9a8d38b0461ddf9c12f27390dad9a40c9b4e3b", "reference": "de9a8d38b0461ddf9c12f27390dad9a40c9b4e3b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.2 || ^7.0", "monolog/monolog": "^1.23 || ^2.0", "php": ">=7.1.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "phpunit/phpunit": "^7.5", "predis/predis": "^1.1"}, "suggest": {"predis/predis": "Allows to use throttle feature"}, "time": "2020-10-14T08:17:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Yansongda\\Supports\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "common components", "keywords": ["Guzzle", "array", "collection", "config", "http", "support", "throttle"], "support": {"issues": "https://github.com/yansongda/supports/issues", "source": "https://github.com/yansongda/supports"}, "install-path": "../yansongda/supports"}, {"name": "yly-openapi/yly-openapi-sdk", "version": "v1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Qzm6826/yilianyun-php-sdk.git", "reference": "f4ca7a2296fcb7001eb154dcba1cc389837c9a27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Qzm6826/yilianyun-php-sdk/zipball/f4ca7a2296fcb7001eb154dcba1cc389837c9a27", "reference": "f4ca7a2296fcb7001eb154dcba1cc389837c9a27", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "time": "2021-05-12T03:13:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"App\\": "Lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "曲子陌", "email": "<EMAIL>"}], "description": "yly-openapi-php-sdk", "keywords": ["yly-openapi-php-sdk"], "support": {"issues": "https://github.com/Qzm6826/yilianyun-php-sdk/issues", "source": "https://github.com/Qzm6826/yilianyun-php-sdk/tree/v1.0.3"}, "install-path": "../yly-openapi/yly-openapi-sdk"}], "dev": true, "dev-package-names": ["symfony/var-dumper", "topthink/think-trace"]}