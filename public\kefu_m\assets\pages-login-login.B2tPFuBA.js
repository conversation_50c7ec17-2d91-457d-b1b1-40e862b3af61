import{r as e,e as a,f as l,w as t,q as s,o,g as n,h as u,G as i,H as c,I as d,l as f,b as r,J as p}from"./index-KqVIYTFB.js";import{_ as m,o as v,a as g,b as y,c as _,r as k,d as h}from"./uni-app.es.elp5fm4t.js";import{_ as x}from"./uv-button.CCU3gl96.js";const b=m({__name:"login",setup(m){const b=e(!1),z=e(""),V=e("");async function w(){if(!V.value)return i("请输入登录账号！");if(!z.value)return i("请输入登录密码！");let e={account:V.value,client:c(),password:z.value,type:"1"};console.log(e);const{code:a,data:l}=await d(e);1==a&&(f.set("token",l.token),f.set("serveinfo",l),r("updateSocket"),uni.$uv.route({url:"/pages/sessionlist/sessionlist"}))}return v((function(e){})),g((function(){})),y((function(){})),_((function(){})),(e,i)=>{const c=s,d=k(a("uv-icon"),h),f=p,r=k(a("uv-button"),x);return o(),l(c,{class:"content height-100vh",style:{background:"#FFFFFF"}},{default:t((()=>[n(c,{class:"font-weight-bold text-center font-size-46"},{default:t((()=>[u(" 客服系统 ")])),_:1}),n(c,{class:"font-weight-bold text-center font-size-32"},{default:t((()=>[u(" 客服端登录 ")])),_:1}),n(c,{class:"padding-about-20 margin-top-120"},{default:t((()=>[n(c,{class:"input display-flex align-items"},{default:t((()=>[n(d,{name:"account",color:"#666666",size:"28"}),n(f,{type:"number",value:"",modelValue:V.value,"onUpdate:modelValue":i[0]||(i[0]=e=>V.value=e),maxlength:"11",class:"file-1 margin-left-20 font-size-26",placeholder:"请输入账号"},null,8,["modelValue"])])),_:1}),n(c,{class:"input display-flex align-items margin-top-10"},{default:t((()=>[n(d,{name:"lock-open",color:"#666666",size:"28"}),n(c,{class:"file-1 display-flex align-items"},{default:t((()=>[b.value?(o(),l(f,{key:0,type:"text",modelValue:z.value,"onUpdate:modelValue":i[1]||(i[1]=e=>z.value=e),value:"",class:"file-1 margin-left-20 font-size-26",placeholder:"请输入密码"},null,8,["modelValue"])):(o(),l(f,{key:1,type:"password",value:"",modelValue:z.value,"onUpdate:modelValue":i[2]||(i[2]=e=>z.value=e),class:"file-1 margin-left-20 font-size-26",placeholder:"请输入密码"},null,8,["modelValue"])),n(c,{onClick:i[3]||(i[3]=e=>b.value=!b.value),class:""},{default:t((()=>[b.value?(o(),l(d,{key:0,name:"eye",color:"#666666",size:"22"})):(o(),l(d,{key:1,name:"eye-off-outline",color:"#666666",size:"24"}))])),_:1})])),_:1})])),_:1}),n(c,{class:"margin-top-120"},{default:t((()=>[n(r,{type:"primary",text:"登录",onClick:w})])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-343379f9"]]);export{b as default};
