import{e as t,f as e,w as a,q as o,o as n,g as s,h as i,v as r,x as p}from"./index-KqVIYTFB.js";import{_ as l}from"./uv-button.CCU3gl96.js";import{o as c,a as m,b as u,c as y,r as d}from"./uni-app.es.elp5fm4t.js";import{_ as f}from"./zb-popover.DS7TbkBw.js";const x={__name:"index",setup(x){c((function(t){})),m((function(){}));const b=[{text:"选项一"},{text:"选项二"},{text:"选项三"}];async function g(t){console.log(t)}return u((function(){})),y((function(){})),(c,m)=>{const u=d(t("uv-button"),l),y=p,x=d(t("zb-popover"),f),h=o;return n(),e(h,{class:"content"},{default:a((()=>[s(u,{type:"primary",text:"确定"}),s(u,{type:"primary",plain:!0,text:"镂空"}),s(u,{type:"primary",plain:!0,hairline:!0,text:"细边"}),s(u,{type:"primary",disabled:c.disabled,text:"禁用"},null,8,["disabled"]),s(u,{type:"primary",loading:"",loadingText:"加载中"}),s(u,{type:"primary",icon:"map",text:"图标按钮"}),s(u,{type:"primary",shape:"circle",text:"按钮形状"}),s(u,{onClick:m[0]||(m[0]=t=>async function(){console.log(1),r({url:"wss://kefu.huohanghang.cn/?token=866eedcb201943d2bfc5c7ad8a7fa685&type=kefu&client=2&shop_id=49",complete:t=>{console.log(t)}})}()),text:"渐变色按钮",color:"linear-gradient(to right, rgb(66, 83, 216), rgb(213, 51, 186))"}),s(u,{type:"primary",size:"small",text:"大小尺寸"}),s(x,{placement:"bottom-start",options:b,ref:"Popover1",onHandleClick:g,class:"item-popover"},{default:a((()=>[s(y,{class:"mini-btn",type:"primary",size:"mini"},{default:a((()=>[i("浅色风格")])),_:1})])),_:1},512)])),_:1})}}};export{x as default};
