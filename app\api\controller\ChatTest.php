<?php

namespace app\api\controller;

use app\common\basics\ApiBase;
use app\api\logic\UserLogic;
use app\common\server\JsonServer;

/**
 * 聊天功能测试控制器
 * Class ChatTest
 * @package app\api\controller
 */
class ChatTest extends ApiBase
{
    /**
     * 测试获取聊天记录接口
     * @return \think\response\Json
     */
    public function testChatRecord()
    {
        try {
            $shop_id = $this->request->get('shop_id/d', 0);
            $page = $this->request->get('page/d', 1);
            $size = $this->request->get('size/d', 20);

            // 测试用户ID，实际使用时应该从登录用户获取
            $user_id = $this->user_id;

            $result = UserLogic::getChatRecord($user_id, $shop_id, $page, $size);

            return JsonServer::success('获取成功', [
                'shop_id' => $shop_id,
                'chat_type' => $shop_id == 0 ? 'user_chat' : 'kefu_chat',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试用户对用户聊天记录
     * @return \think\response\Json
     */
    public function testUserChatRecord()
    {
        try {
            $page = $this->request->get('page/d', 1);
            $size = $this->request->get('size/d', 20);
            $user_id = $this->user_id;

            $result = UserLogic::getUserChatRecord($user_id, $page, $size);

            return JsonServer::success('获取用户聊天记录成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试客服聊天记录
     * @return \think\response\Json
     */
    public function testKefuChatRecord()
    {
        try {
            $shop_id = $this->request->get('shop_id/d', 1);
            $page = $this->request->get('page/d', 1);
            $size = $this->request->get('size/d', 20);
            $user_id = $this->user_id;

            $result = UserLogic::getChatRecord($user_id, $shop_id, $page, $size);

            return JsonServer::success('获取客服聊天记录成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }
}
