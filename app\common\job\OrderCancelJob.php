<?php

namespace app\common\job;

use think\queue\Job;
use app\api\logic\OrderLogic;
use app\common\server\JsonServer;
use think\facade\Log;
use app\common\model\order\Order;
use app\common\enum\OrderEnum;
use app\common\enum\OrderLogEnum;
use app\common\enum\PayEnum;

/**
 * 订单取消队列任务
 * Class OrderCancelJob
 * @package app\common\job
 */
class OrderCancelJob
{
    /**
     * 执行订单取消任务
     * @param Job $job 队列任务对象
     * @param array $data 任务数据 ['order_id' => 订单ID, 'user_id' => 用户ID]
     */
    public function fire(Job $job, $data)
    {
        // 记录任务开始执行
        Log::info('OrderCancelJob 任务开始执行', [
            'job_id' => $job->getJobId(),
            'attempts' => $job->attempts(),
            'data' => $data
        ]);

        try {
            // 验证数据
            if (empty($data['order_id']) || empty($data['user_id'])) {
                Log::error('OrderCancelJob 参数错误: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
                $job->delete(); // 参数错误直接删除任务
                return;
            }

            $order_id = $data['order_id'];
            $user_id = $data['user_id'];

            Log::info("OrderCancelJob 开始处理订单取消: order_id={$order_id}, user_id={$user_id}, attempts={$job->attempts()}");

            // 调用修改后的订单取消逻辑（不触发事件）
            $result = self::cancelOrderWithoutEvent($order_id, $user_id);

            // 检查结果
            if ($result && isset($result['code']) && $result['code'] == 1) {
                // 取消成功，推送后续处理任务到队列
                Log::info("OrderCancelJob 订单取消成功: order_id={$order_id}");

                // 推送 AfterCancelOrder 任务到队列
                \think\facade\Queue::push('app\common\job\AfterCancelOrderJob', [
                    'type' => OrderLogEnum::TYPE_USER,
                    'channel' => OrderLogEnum::USER_CANCEL_ORDER,
                    'order_id' => $order_id,
                    'handle_id' => $user_id,
                ], 'orderCancel');

                $job->delete(); // 删除任务
            } else {
                // 取消失败
                $error_msg = isset($result['msg']) ? $result['msg'] : '订单取消失败';
                Log::error("OrderCancelJob 订单取消失败: order_id={$order_id}, error={$error_msg}");

                // 检查重试次数
                if ($job->attempts() >= 3) {
                    Log::error("OrderCancelJob 达到最大重试次数，任务失败: order_id={$order_id}");
                    $job->delete(); // 达到最大重试次数，删除任务
                } else {
                    $job->release(30); // 延迟30秒后重试
                }
            }

        } catch (\Exception $e) {
            Log::error('OrderCancelJob 执行异常: ' . $e->getMessage() . ', 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            // 检查重试次数
            if ($job->attempts() >= 3) {
                Log::error("OrderCancelJob 异常达到最大重试次数: " . $e->getMessage());
                $job->delete(); // 达到最大重试次数，删除任务
            } else {
                $job->release(30); // 延迟30秒后重试
            }
        }
    }

    /**
     * 任务失败处理
     * @param array $data 任务数据
     */
    public function failed($data)
    {
        Log::error('OrderCancelJob 最终执行失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        // 可以在这里添加失败后的处理逻辑，比如：
        // 1. 发送通知给管理员
        // 2. 记录到失败队列表
        // 3. 发送邮件或短信通知
    }

    /**
     * 不触发事件的订单取消逻辑
     * @param int $order_id
     * @param int $user_id
     * @return array
     */
    private static function cancelOrderWithoutEvent($order_id, $user_id)
    {
        $time = time();
        $order = Order::with(['orderGoods'])->where(['del' => 0, 'user_id' => $user_id, 'id' => $order_id])->find();

        if (!$order || (int)$order['order_status'] > OrderEnum::ORDER_STATUS_DELIVERY) {
            return JsonServer::error('很抱歉!订单无法取消');
        }

        if ($order['order_status'] == OrderEnum::ORDER_STATUS_NO_PAID) {
           \think\facade\Db::table('order')->where(['id' => $order_id])->update(['order_status' => OrderEnum::ORDER_STATUS_DOWN, 'cancel_time' => $time]);
           return JsonServer::success('取消成功');
        }

        \think\facade\Db::startTrans();
        try {
            // 取消订单（不触发事件）
            self::cancelOrderStatusOnly($order_id, OrderLogEnum::TYPE_USER, $user_id);

            // 已支付的订单,取消,退款
            if ($order['pay_status'] == PayEnum::ISPAID) {
                // 更新订单状态
                \app\common\logic\OrderRefundLogic::cancelOrderRefundUpdate($order);
                // 订单退款
                \app\common\logic\OrderRefundLogic::refund($order, $order['order_amount'], $order['order_amount']);
            }

            \think\facade\Db::commit();
            return JsonServer::success('取消成功');
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            \app\api\logic\OrderLogic::addErrorRefund($order, $e->getMessage());
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 只更新订单状态，不触发事件
     * @param int $order_id
     * @param int $handle_type
     * @param int $handle_id
     */
    private static function cancelOrderStatusOnly($order_id, $handle_type, $handle_id)
    {
        // 更新订单状态
        $order = Order::where('id', $order_id)->find();
        $order->order_status = OrderEnum::ORDER_STATUS_DOWN;
        $order->update_time = time();
        $order->cancel_time = time();
        $order->save();

        // 记录订单日志（不触发事件）
        switch ($handle_type) {
            case OrderLogEnum::TYPE_USER:
                $channel = OrderLogEnum::USER_CANCEL_ORDER;
                break;
            case OrderLogEnum::TYPE_SHOP:
                $channel = OrderLogEnum::SHOP_CANCEL_ORDER;
                break;
            case OrderLogEnum::TYPE_SYSTEM:
                $channel = OrderLogEnum::SYSTEM_CANCEL_ORDER;
                break;
        }

        // 记录订单日志
        \app\common\logic\OrderLogLogic::record(
            $handle_type,
            $channel,
            $order_id,
            $handle_id,
            $channel
        );
    }
}
