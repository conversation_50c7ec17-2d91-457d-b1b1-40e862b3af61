<?php

namespace app\common\job;

use think\queue\Job;
use app\api\logic\OrderLogic;
use app\common\server\JsonServer;
use think\facade\Log;

/**
 * 订单取消队列任务
 * Class OrderCancelJob
 * @package app\common\job
 */
class OrderCancelJob
{
    /**
     * 执行订单取消任务
     * @param Job $job 队列任务对象
     * @param array $data 任务数据 ['order_id' => 订单ID, 'user_id' => 用户ID]
     */
    public function fire(Job $job, $data)
    {
        // 记录任务开始执行
        Log::info('OrderCancelJob 任务开始执行', [
            'job_id' => $job->getJobId(),
            'attempts' => $job->attempts(),
            'data' => $data
        ]);

        try {
            // 验证数据
            if (empty($data['order_id']) || empty($data['user_id'])) {
                Log::error('OrderCancelJob 参数错误: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
                $job->delete(); // 参数错误直接删除任务
                return;
            }

            $order_id = $data['order_id'];
            $user_id = $data['user_id'];

            Log::info("OrderCancelJob 开始处理订单取消: order_id={$order_id}, user_id={$user_id}, attempts={$job->attempts()}");

            // 调用原有的订单取消逻辑
            $result = OrderLogic::cancel($order_id, $user_id);

            // 检查结果
            if ($result && isset($result['code']) && $result['code'] == 1) {
                // 取消成功
                Log::info("OrderCancelJob 订单取消成功: order_id={$order_id}");
                $job->delete(); // 删除任务
            } else {
                // 取消失败
                $error_msg = isset($result['msg']) ? $result['msg'] : '订单取消失败';
                Log::error("OrderCancelJob 订单取消失败: order_id={$order_id}, error={$error_msg}");
                
                // 检查重试次数
                if ($job->attempts() >= 3) {
                    Log::error("OrderCancelJob 达到最大重试次数，任务失败: order_id={$order_id}");
                    $job->delete(); // 达到最大重试次数，删除任务
                } else {
                    $job->release(30); // 延迟30秒后重试
                }
            }

        } catch (\Exception $e) {
            Log::error('OrderCancelJob 执行异常: ' . $e->getMessage() . ', 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            // 检查重试次数
            if ($job->attempts() >= 3) {
                Log::error("OrderCancelJob 异常达到最大重试次数: " . $e->getMessage());
                $job->delete(); // 达到最大重试次数，删除任务
            } else {
                $job->release(30); // 延迟30秒后重试
            }
        }
    }

    /**
     * 任务失败处理
     * @param array $data 任务数据
     */
    public function failed($data)
    {
        Log::error('OrderCancelJob 最终执行失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        
        // 可以在这里添加失败后的处理逻辑，比如：
        // 1. 发送通知给管理员
        // 2. 记录到失败队列表
        // 3. 发送邮件或短信通知
    }
}
