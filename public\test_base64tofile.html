<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Base64ToFile功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin-top: 10px;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        img {
            max-width: 100%;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>测试Base64ToFile功能</h1>
    
    <div class="container">
        <h2>1. 生成二维码并转换为文件</h2>
        <div>
            <label for="content">二维码内容：</label>
            <input type="text" id="content" value="https://www.example.com" style="width: 300px;">
        </div>
        <button id="generateQR">生成二维码并保存为文件</button>
        <div id="qrResult"></div>
    </div>
    
    <div class="container">
        <h2>2. 上传图片并转换为Base64</h2>
        <input type="file" id="imageFile" accept="image/*">
        <button id="convertToBase64">转换为Base64</button>
        <div>
            <textarea id="base64Output" readonly></textarea>
        </div>
    </div>
    
    <div class="container">
        <h2>3. 将Base64转换为文件</h2>
        <button id="convertToFile">将Base64转换为文件</button>
        <div id="fileResult"></div>
    </div>
    
    <script>
        document.getElementById('generateQR').addEventListener('click', function() {
            const content = document.getElementById('content').value;
            if (!content) {
                alert('请输入二维码内容');
                return;
            }
            
            fetch('/api/qrcode/base64tofile?content=' + encodeURIComponent(content) + '&return_url=1')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        const resultDiv = document.getElementById('qrResult');
                        resultDiv.innerHTML = `
                            <p>二维码已生成并保存为文件</p>
                            <p>文件路径: ${data.data.qr_code}</p>
                            <img src="${data.data.qr_code}" alt="生成的二维码">
                        `;
                    } else {
                        alert('生成二维码失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('请求失败');
                });
        });
        
        document.getElementById('convertToBase64').addEventListener('click', function() {
            const fileInput = document.getElementById('imageFile');
            if (!fileInput.files || !fileInput.files[0]) {
                alert('请先选择一个图片文件');
                return;
            }
            
            const file = fileInput.files[0];
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const base64 = e.target.result;
                document.getElementById('base64Output').value = base64;
            };
            
            reader.readAsDataURL(file);
        });
        
        document.getElementById('convertToFile').addEventListener('click', function() {
            const base64 = document.getElementById('base64Output').value;
            if (!base64) {
                alert('请先转换图片为Base64');
                return;
            }
            
            fetch('/api/file/base64tofile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    base64: base64,
                    return_url: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    const resultDiv = document.getElementById('fileResult');
                    resultDiv.innerHTML = `
                        <p>Base64已转换为文件</p>
                        <p>文件路径: ${data.data.file_path}</p>
                        <img src="${data.data.file_path}" alt="转换后的图片">
                    `;
                } else {
                    alert('转换失败: ' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('请求失败');
            });
        });
    </script>
</body>
</html>
