<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知测试工具</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .header {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 20px;
        }
        .tabs {
            display: flex;
            background-color: #f1f1f1;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 2px solid #4CAF50;
        }
        .iframe-container {
            flex: 1;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>通知测试工具</h1>
            <div>
                <button id="refresh-all">刷新所有</button>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-iframe="test_websocket.html">原始测试页面</div>
            <div class="tab" data-iframe="test_websocket_compatible.html">兼容测试页面</div>
            <div class="tab" data-iframe="admin_notification_test.html">高级测试页面</div>
        </div>
        
        <div class="iframe-container">
            <iframe id="test-iframe" src="test_websocket.html"></iframe>
        </div>
    </div>
    
    <script>
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签页的active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                
                // 添加当前标签页的active类
                this.classList.add('active');
                
                // 更新iframe的src
                const iframeSrc = this.getAttribute('data-iframe');
                document.getElementById('test-iframe').src = iframeSrc;
            });
        });
        
        // 刷新所有
        document.getElementById('refresh-all').addEventListener('click', function() {
            document.getElementById('test-iframe').src = document.getElementById('test-iframe').src;
        });
    </script>
</body>
</html>
