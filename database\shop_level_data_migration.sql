-- 商家等级体系数据迁移脚本
-- 将现有商家数据迁移到新的等级体系

-- 1. 备份原始数据（建议在执行前手动备份）
-- CREATE TABLE ls_shop_backup AS SELECT * FROM ls_shop;
-- CREATE TABLE ls_shop_apply_backup AS SELECT * FROM ls_shop_apply;
-- CREATE TABLE ls_shop_merchantfees_backup AS SELECT * FROM ls_shop_merchantfees;

-- 2. 迁移ls_shop表数据
-- 2.1 将有验厂服务的商家设为实力厂商（等级2）
UPDATE `ls_shop` 
SET 
    `level` = 2,
    `level_upgrade_time` = `create_time`,
    `level_expire_time` = CASE 
        WHEN `expire_time` > 0 THEN `expire_time`
        ELSE UNIX_TIMESTAMP() + 31536000  -- 默认1年后到期
    END
WHERE `yan_fee` = 1 AND `del` = 0;

-- 2.2 将其他已审核通过的商家设为商家会员（等级1）
UPDATE `ls_shop` 
SET 
    `level` = 1,
    `level_upgrade_time` = `create_time`,
    `level_expire_time` = CASE 
        WHEN `expire_time` > 0 THEN `expire_time`
        ELSE UNIX_TIMESTAMP() + 31536000  -- 默认1年后到期
    END
WHERE `level` = 0 AND `del` = 0 AND `id` IN (
    SELECT DISTINCT s.id FROM (
        SELECT id FROM ls_shop WHERE id IN (
            SELECT DISTINCT shop_id FROM ls_shop_merchantfees WHERE status = 1 AND shop_id > 0
        )
    ) s
);

-- 2.3 将未支付费用但已创建的商家设为0元入驻（等级0）
UPDATE `ls_shop` 
SET 
    `level` = 0,
    `level_upgrade_time` = `create_time`,
    `level_expire_time` = 0  -- 0元入驻不设到期时间
WHERE `level` = 0 AND `del` = 0;

-- 3. 迁移ls_shop_apply表数据
-- 3.1 根据支付记录设置目标等级和预付费状态
UPDATE `ls_shop_apply` sa
INNER JOIN `ls_shop_merchantfees` sm ON sa.user_id = sm.user_id
SET 
    sa.target_level = CASE 
        WHEN sm.feetype = 1 THEN 2  -- 超级商家套餐 -> 实力厂商
        WHEN sm.feetype = 0 THEN 1  -- 入驻费 -> 商家会员
        ELSE 1
    END,
    sa.pre_paid = CASE WHEN sm.status = 1 THEN 1 ELSE 0 END,
    sa.payment_order_sn = sm.order_sn,
    sa.payment_time = CASE WHEN sm.status = 1 THEN sm.created_at ELSE NULL END
WHERE sa.del = 0 AND sm.id = (
    SELECT id FROM (
        SELECT id FROM ls_shop_merchantfees 
        WHERE user_id = sa.user_id 
        ORDER BY id DESC LIMIT 1
    ) tmp
);

-- 3.2 未支付的申请设为0元入驻
UPDATE `ls_shop_apply` 
SET `target_level` = 0, `pre_paid` = 0
WHERE `target_level` = 0 AND `del` = 0;

-- 4. 迁移ls_shop_merchantfees表数据
UPDATE `ls_shop_merchantfees` 
SET 
    `target_level` = CASE 
        WHEN `feetype` = 1 THEN 2  -- 超级商家套餐 -> 实力厂商
        WHEN `feetype` = 0 THEN 1  -- 入驻费 -> 商家会员
        WHEN `feetype` = 2 THEN 2  -- 检验费 -> 实力厂商
        ELSE 1
    END,
    `upgrade_type` = 0  -- 标记为新入驻
WHERE `target_level` = 0;

-- 5. 创建升级记录（基于已支付的费用记录）
INSERT INTO `ls_shop_level_upgrade` (
    `shop_id`, `from_level`, `to_level`, `order_sn`, `amount`, 
    `status`, `pay_time`, `effect_time`, `expire_time`, 
    `remark`, `create_time`, `update_time`
)
SELECT 
    COALESCE(sm.shop_id, s.id) as shop_id,
    0 as from_level,
    sm.target_level as to_level,
    sm.order_sn,
    sm.amount,
    CASE WHEN sm.status = 1 THEN 2 ELSE 0 END as status,  -- 2=已生效
    CASE WHEN sm.status = 1 THEN UNIX_TIMESTAMP(sm.created_at) ELSE NULL END as pay_time,
    CASE WHEN sm.status = 1 THEN UNIX_TIMESTAMP(sm.created_at) ELSE NULL END as effect_time,
    CASE WHEN sm.status = 1 THEN s.level_expire_time ELSE NULL END as expire_time,
    '数据迁移生成' as remark,
    UNIX_TIMESTAMP() as create_time,
    UNIX_TIMESTAMP() as update_time
FROM `ls_shop_merchantfees` sm
LEFT JOIN `ls_shop` s ON sm.shop_id = s.id OR (sm.shop_id = 0 AND s.id = (
    SELECT shop_id FROM ls_user WHERE id = sm.user_id LIMIT 1
))
WHERE sm.target_level > 0;

-- 6. 数据验证查询（迁移后执行以验证数据正确性）
-- SELECT 
--     level,
--     COUNT(*) as count,
--     COUNT(CASE WHEN level_expire_time > UNIX_TIMESTAMP() THEN 1 END) as active_count
-- FROM ls_shop 
-- WHERE del = 0 
-- GROUP BY level;

-- SELECT 
--     target_level,
--     pre_paid,
--     COUNT(*) as count
-- FROM ls_shop_apply 
-- WHERE del = 0 
-- GROUP BY target_level, pre_paid;
