<?php
/**
 * 队列监控脚本
 * 用于监控订单取消队列的状态
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 订单取消队列监控 ===\n";
echo "监控时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 获取队列专用的Redis连接
    $queueConfig = config('queue.connections.redis');
    $redis = new \Redis();
    $redis->connect($queueConfig['host'], $queueConfig['port']);
    if (!empty($queueConfig['password'])) {
        $redis->auth($queueConfig['password']);
    }
    $redis->select($queueConfig['select']);

    // 检查队列长度 - 使用think-queue的队列名称格式
    $queueName = 'queues:orderCancel';
    $queueLength = $redis->llen($queueName);

    echo "Redis连接信息:\n";
    echo "  主机: {$queueConfig['host']}:{$queueConfig['port']}\n";
    echo "  数据库: {$queueConfig['select']}\n\n";
    
    echo "队列状态:\n";
    echo "  队列名称: orderCancel\n";
    echo "  待处理任务数: {$queueLength}\n";
    
    if ($queueLength > 0) {
        echo "  ⚠️  有 {$queueLength} 个任务等待处理\n";
    } else {
        echo "  ✓ 队列为空，无待处理任务\n";
    }
    
    echo "\n";
    
    // 检查延迟队列
    $delayedQueueName = 'queues:orderCancel:delayed';
    $delayedLength = $redis->zcard($delayedQueueName);
    
    echo "延迟队列:\n";
    echo "  延迟任务数: {$delayedLength}\n";
    
    if ($delayedLength > 0) {
        echo "  ⚠️  有 {$delayedLength} 个延迟任务\n";
    } else {
        echo "  ✓ 无延迟任务\n";
    }
    
    echo "\n";
    
    // 检查保留队列（正在处理的任务）
    $reservedQueueName = 'queues:orderCancel:reserved';
    $reservedLength = $redis->zcard($reservedQueueName);
    
    echo "保留队列:\n";
    echo "  正在处理任务数: {$reservedLength}\n";
    
    if ($reservedLength > 0) {
        echo "  ℹ️  有 {$reservedLength} 个任务正在处理\n";
    } else {
        echo "  ✓ 无正在处理的任务\n";
    }
    
    echo "\n";
    
    // 显示建议
    if ($queueLength > 10) {
        echo "建议:\n";
        echo "  ⚠️  队列积压较多，建议增加队列进程数量\n";
        echo "  可以启动多个队列监听进程来加快处理速度\n";
    } elseif ($queueLength == 0 && $delayedLength == 0 && $reservedLength == 0) {
        echo "状态: ✓ 队列运行正常，无积压任务\n";
    }
    
    echo "\n=== 监控完成 ===\n";
    
} catch (\Exception $e) {
    echo "❌ 监控失败: " . $e->getMessage() . "\n";
    echo "请检查Redis连接配置\n";
}
