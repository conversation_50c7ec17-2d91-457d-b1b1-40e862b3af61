<?php
/**
 * Redis队列调试脚本
 * 用于检查Redis中的实际队列数据
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== Redis队列调试 ===\n";
echo "调试时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 获取队列配置
    $queueConfig = config('queue.connections.redis');
    echo "队列配置:\n";
    echo "  主机: {$queueConfig['host']}\n";
    echo "  端口: {$queueConfig['port']}\n";
    echo "  数据库: {$queueConfig['select']}\n";
    echo "  队列名: {$queueConfig['queue']}\n\n";
    
    // 连接Redis
    $redis = new \Redis();
    $redis->connect($queueConfig['host'], $queueConfig['port']);
    if (!empty($queueConfig['password'])) {
        $redis->auth($queueConfig['password']);
    }
    $redis->select($queueConfig['select']);
    
    echo "Redis连接成功\n\n";
    
    // 检查所有可能的队列键名
    $possibleKeys = [
        '{queues:orderCancel}',
        'queues:default',
        'orderCancel',
        'default',
        'queue:orderCancel',
        'queue:default'
    ];
    
    echo "检查所有可能的队列键:\n";
    foreach ($possibleKeys as $key) {
        $length = $redis->llen($key);
        echo "  {$key}: {$length} 个任务\n";
        
        if ($length > 0) {
            echo "    前3个任务内容:\n";
            $tasks = $redis->lrange($key, 0, 2);
            foreach ($tasks as $i => $task) {
                echo "    [" . ($i + 1) . "] " . substr($task, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\n";
    
    // 检查所有以queue开头的键
    echo "所有以'queue'开头的键:\n";
    $keys = $redis->keys('queue*');
    foreach ($keys as $key) {
        $type = $redis->type($key);
        $typeNames = [
            \Redis::REDIS_STRING => 'string',
            \Redis::REDIS_LIST => 'list',
            \Redis::REDIS_SET => 'set',
            \Redis::REDIS_ZSET => 'zset',
            \Redis::REDIS_HASH => 'hash'
        ];
        $typeName = $typeNames[$type] ?? 'unknown';
        
        if ($type == \Redis::REDIS_LIST) {
            $length = $redis->llen($key);
            echo "  {$key} (list): {$length} 个元素\n";
        } elseif ($type == \Redis::REDIS_ZSET) {
            $length = $redis->zcard($key);
            echo "  {$key} (zset): {$length} 个元素\n";
        } else {
            echo "  {$key} ({$typeName})\n";
        }
    }
    
    echo "\n";
    
    // 检查所有键
    echo "所有键 (前20个):\n";
    $allKeys = $redis->keys('*');
    $displayKeys = array_slice($allKeys, 0, 20);
    foreach ($displayKeys as $key) {
        echo "  {$key}\n";
    }
    
    if (count($allKeys) > 20) {
        echo "  ... 还有 " . (count($allKeys) - 20) . " 个键\n";
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (\Exception $e) {
    echo "❌ 调试失败: " . $e->getMessage() . "\n";
    echo "请检查Redis连接配置\n";
}
