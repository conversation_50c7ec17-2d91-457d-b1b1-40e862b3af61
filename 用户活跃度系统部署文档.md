# 用户活跃度系统部署文档

## 📋 系统概述

用户活跃度系统是一个完整的用户行为激励机制，通过积分和等级体系鼓励用户参与平台活动。

### 主要功能
- **积分获取**: 用户通过登录、发布采购信息、聊天、购买商品等行为获得积分
- **等级体系**: 基于积分自动计算用户活跃度等级（0-5级）
- **采购商标识**: 发布采购信息后自动标记为采购商
- **后台管理**: 完整的配置管理和统计功能
- **防刷机制**: 防止恶意刷分的安全措施

## 🗄️ 数据库变更

### 1. 用户表字段扩展
```sql
-- 在ls_user表中添加活跃度相关字段
ALTER TABLE `ls_user` 
ADD COLUMN `activity_score` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '活跃度积分' AFTER `user_growth`,
ADD COLUMN `is_purchaser` tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '是否为采购商：0-否；1-是' AFTER `activity_score`,
ADD COLUMN `last_activity_login` int(10) unsigned DEFAULT NULL COMMENT '最后活跃登录时间' AFTER `is_purchaser`;
```

### 2. 活跃度日志表
```sql
-- 创建用户活跃度日志表
CREATE TABLE `ls_user_activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `activity_type` varchar(50) NOT NULL COMMENT '活动类型：purchaser_login,publish_demand,chat,purchase',
  `score_change` int(10) NOT NULL DEFAULT 0 COMMENT '积分变化（可为负数）',
  `before_score` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '变化前积分',
  `after_score` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '变化后积分',
  `before_level` tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '变化前等级',
  `after_level` tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '变化后等级',
  `remark` varchar(255) DEFAULT '' COMMENT '备注说明',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_activity_type` (`activity_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活跃度日志表';
```

### 3. 后台菜单权限
```sql
-- 添加用户活跃度设置菜单
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`) 
VALUES (1, 0, 18, '用户活跃度设置', 'layui-icon-user', 'setting.UserActivity/index', 50, 0, 0);

-- 添加权限项
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`) 
VALUES (2, 0, 491, '查看配置', '', 'setting.UserActivity/index', 50, 0, 0),
       (2, 0, 491, '保存配置', '', 'setting.UserActivity/save', 51, 0, 0),
       (2, 0, 491, '重新计算等级', '', 'setting.UserActivity/recalculate', 52, 0, 0);
```

## ⚙️ 配置项说明

### 系统设置
- `is_enabled`: 是否启用活跃度系统 (默认: 1)
- `login_check_days`: 采购商登录积分检查天数 (默认: 7)
- `chat_daily_limit`: 每日聊天积分获取上限次数 (默认: 10)

### 积分规则
- `purchaser_login_score`: 采购商登录获得积分 (默认: 10)
- `publish_demand_score`: 发布采购信息获得积分 (默认: 20)
- `chat_score`: 用户聊天获得积分 (默认: 1)
- `purchase_score`: 购买商品获得积分 (默认: 20)

### 等级标准
- 1级（活跃新手）: 100分
- 2级（活跃用户）: 300分
- 3级（活跃达人）: 500分
- 4级（活跃专家）: 1000分
- 5级（活跃大师）: 2000分

## 🔧 部署步骤

### 1. 执行数据库脚本
```bash
# 连接数据库执行以上SQL脚本
mysql -h 114.55.251.16 -u kshop -p kshop < database/user_activity_system.sql
```

### 2. 初始化配置
```bash
# 执行配置初始化脚本
mysql -h 114.55.251.16 -u kshop -p kshop < database/user_activity_init_config.sql
```

### 3. 文件部署
确保以下文件已正确部署：
- `app/common/enum/UserActivityEnum.php`
- `app/common/logic/UserActivityLogic.php`
- `app/common/model/user/UserActivityLog.php`
- `app/admin/controller/setting/UserActivity.php`
- `app/admin/logic/setting/UserActivityLogic.php`
- `app/admin/validate/setting/UserActivityValidate.php`
- `app/admin/view/setting/user_activity/index.html`

### 4. 业务集成验证
系统已自动集成到以下业务流程：
- ✅ 用户登录 (`app/api/logic/LoginLogic.php`)
- ✅ 发布采购信息 (`app/api/logic/CommunityLogic.php`)
- ✅ 用户聊天 (`app/common/listener/websocket/Chat.php`)
- ✅ 订单支付 (`app/common/logic/PayNotifyLogic.php`)

### 5. 测试验证
```bash
# 运行测试脚本验证系统功能
php test_user_activity.php
```

## 🎯 使用说明

### 后台管理
1. 登录后台管理系统
2. 进入 "基础设置" -> "用户活跃度设置"
3. 配置积分规则和等级标准
4. 查看统计数据和活跃度日志

### 积分获取规则
1. **采购商登录**: 采购商7天内首次登录获得积分
2. **发布采购信息**: 每次发布获得积分，同时标记为采购商
3. **用户聊天**: 每次聊天获得积分（有每日上限）
4. **购买商品**: 每次购买获得积分

### 等级计算
- 系统根据用户当前积分自动计算等级
- 等级变化时会记录到活跃度日志
- 支持手动重新计算所有用户等级

## 🛡️ 安全特性

### 防刷机制
- 采购商登录积分：7天内重复登录不加分
- 聊天积分：每日获取次数限制
- 异常处理：活跃度系统异常不影响主业务流程

### 数据完整性
- 所有积分变化都有详细日志记录
- 支持积分变化前后的状态对比
- 事务保证数据一致性

## 📊 监控和维护

### 统计功能
- 用户等级分布统计
- 采购商数量统计
- 活动类型统计
- 每日活跃度记录统计

### 日志管理
- 完整的积分变化历史
- 支持按用户、活动类型、时间范围筛选
- 详细的操作记录和备注

## 🔄 扩展性

### 新增活动类型
1. 在 `UserActivityEnum` 中添加新的活动类型常量
2. 在配置中添加对应的积分规则
3. 在 `UserActivityLogic::calculateScoreChange` 中添加计算逻辑
4. 在相应业务流程中调用积分添加方法

### 自定义等级
- 支持动态配置等级数量和积分要求
- 等级名称可在枚举中自定义
- 支持复杂的等级计算规则

## ✅ 部署检查清单

- [ ] 数据库表结构已创建
- [ ] 配置项已初始化
- [ ] 后台菜单权限已添加
- [ ] 核心文件已部署
- [ ] 业务集成已完成
- [ ] 测试脚本运行成功
- [ ] 后台管理页面可正常访问
- [ ] 积分获取功能正常
- [ ] 等级计算功能正常
- [ ] 日志记录功能正常

## 📞 技术支持

如有问题，请检查：
1. 数据库连接是否正常
2. 配置项是否正确设置
3. 文件权限是否正确
4. 错误日志中的详细信息

系统设计遵循高可用、高性能、易扩展的原则，确保在高并发环境下稳定运行。
