<?php
/**
 * 简单队列测试脚本
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 简单队列测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 1. 推送测试任务
    echo "1. 推送测试任务...\n";
    $testData = [
        'message' => 'Hello Queue!',
        'timestamp' => time()
    ];
    
    \think\facade\Queue::push('app\common\job\TestJob', $testData, 'orderCancel');
    echo "   ✓ 测试任务推送成功\n";
    echo "   任务数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 2. 推送订单取消任务
    echo "2. 推送订单取消任务...\n";
    $orderData = [
        'order_id' => 999999,
        'user_id' => 1
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJob', $orderData, 'orderCancel');
    echo "   ✓ 订单取消任务推送成功\n";
    echo "   任务数据: " . json_encode($orderData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "3. 检查日志文件...\n";
    $logPath = app()->getRuntimePath() . 'log';
    echo "   日志目录: {$logPath}\n";
    echo "   请查看日志文件中的任务执行记录\n\n";
    
    echo "4. 启动队列监听...\n";
    echo "   运行命令: php think queue:listen --queue=orderCancel\n";
    echo "   或者: php start_queue.php\n\n";
    
    echo "=== 测试完成 ===\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
