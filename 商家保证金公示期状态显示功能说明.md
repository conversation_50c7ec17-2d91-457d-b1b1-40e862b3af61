# 商家保证金公示期状态显示功能说明

## 📋 功能概述

根据商家保证金退款申请的公示期状态，动态显示不同的退款状态和操作按钮，提升管理员的操作体验和业务流程的清晰度。

## 🔧 实现的功能

### 1. **数据库字段扩展**
为 `ls_shop_deposit` 表添加了公示期相关字段：
- `refund_publicity_start_time` - 退款公示期开始时间
- `refund_publicity_end_time` - 退款公示期结束时间

### 2. **状态显示逻辑**
根据公示期时间动态显示退款状态：

| 条件 | 显示状态 | 颜色 | 说明 |
|------|----------|------|------|
| 公示期内 | 已申请(公示期) | 橙色 (#FFB800) | 商家已申请退款，正在公示期内 |
| 公示期结束 | 待退款(公示期结束) | 蓝色 (#1E9FFF) | 公示期已结束，可以处理退款 |
| 未申请 | 未申请 | 灰色 (#999) | 尚未申请退款 |
| 已退款 | 已退款 | 绿色 (#5FB878) | 退款已完成 |
| 拒绝退款 | 拒绝退款 | 红色 (#FF5722) | 退款申请被拒绝 |

### 3. **操作按钮逻辑**
根据状态显示不同的操作按钮：

- **公示期内**：只显示"明细"和"退款详情"按钮
- **公示期结束**：显示"处理退款"按钮，允许管理员处理退款
- **未申请状态**：显示"扣减"和"退费"按钮
- **已退款状态**：显示"退款详情"按钮

## 🎯 核心代码实现

### 表格列配置
```javascript
like.tableLists("#like-table-lists", "{:url()}", [
    {field:"id", width:60, title:"ID"}
    ,{field:"name", width:180, title:"商家名称"}
    ,{field:"deposit_amount", width:100, align:"center", title:"缴纳金额"}
    ,{field:"current_balance", width:120, align:"center", title:"当前剩余保证金"}
    ,{field:"payment_method", width:100, align:"center", title:"缴纳方式"}
    ,{field:"status", width:100, align:"center", title:"审核状态", templet:"#table-status"}
    ,{field:"pay_status", width:100, align:"center", title:"支付状态", templet:"#table-pay-status"}
    ,{field:"refund_status", width:150, align:"center", title:"退款状态", templet:"#table-refund-status"}
    ,{field:"refund_publicity_end_time", width:170, align:"center", title:"公示期结束时间", templet:"#table-publicity-time"}
    ,{field:"remark", width:150, title:"审核理由"}
    ,{field:"created_at", width:170, title:"缴纳时间"}
    ,{field:"updated_at", width:170, align:"center", title:"变动时间"}
    ,{title:"操作", width:280, align:"center", fixed:"right", toolbar:"#table-operation"}
]);
```

### 后端逻辑 (ApplyLogic.php)
```php
// 处理退款状态显示逻辑
if ($item['refund_status'] == 1) {
    // 申请中状态，需要判断公示期
    if (!empty($item['refund_publicity_start_time']) && !empty($item['refund_publicity_end_time'])) {
        $current_time = time();
        $publicity_end_time = strtotime($item['refund_publicity_end_time']);
        
        if ($current_time < $publicity_end_time) {
            // 公示期内
            $item['refund_status_display'] = '已申请(公示期)';
            $item['refund_status_color'] = '#FFB800';
            $item['can_refund'] = false;
        } else {
            // 公示期结束
            $item['refund_status_display'] = '待退款(公示期结束)';
            $item['refund_status_color'] = '#1E9FFF';
            $item['can_refund'] = true;
        }
    }
}
```

### 前端模板 (deposit.html)
```html
<!-- 状态显示 -->
<script type="text/html" id="table-refund-status">
    <span style="color: {{d.refund_status_color ? d.refund_status_color : '#999'}};">
        {{d.refund_status_display ? d.refund_status_display : '未知状态'}}
    </span>
</script>

<!-- 公示期结束时间显示 -->
<script type="text/html" id="table-publicity-time">
    {{#  if(d.refund_status == 1 && d.refund_publicity_end_time){ }}
        {{#  var current_time = new Date().getTime(); }}
        {{#  var end_time = new Date(d.refund_publicity_end_time).getTime(); }}
        {{#  if(current_time < end_time){ }}
            <span style="color: #FFB800;">{{d.refund_publicity_end_time}}</span>
            <br><small style="color: #999;">公示期进行中</small>
        {{#  } else { }}
            <span style="color: #1E9FFF;">{{d.refund_publicity_end_time}}</span>
            <br><small style="color: #1E9FFF;">公示期已结束</small>
        {{#  } }}
    {{#  } else { }}
        <span style="color: #999;">-</span>
    {{#  } }}
</script>

<!-- 操作按钮 -->
<script type="text/html" id="table-operation">
    {{#  if(d.can_refund && d.refund_status == 1){ }}
    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="process_refund">处理退款</a>
    {{#  } }}
</script>
```

## 🔄 业务流程

### 申请退款流程
1. **商家申请退款** → 设置 `refund_status = 1`
2. **设置公示期** → 记录 `refund_publicity_start_time` 和 `refund_publicity_end_time`
3. **公示期内** → 显示"已申请(公示期)"，不可操作
4. **公示期结束** → 显示"待退款(公示期结束)"，可以处理退款

### 处理退款流程
1. **管理员点击"处理退款"** → 打开处理页面
2. **选择处理结果** → 同意退款 或 拒绝退款
3. **同意退款** → 调用通用退款逻辑，实际退款
4. **拒绝退款** → 更新状态为拒绝，记录拒绝原因

## 📊 状态流转图

```
商家申请退款
     ↓
设置公示期 (refund_status = 1)
     ↓
公示期内 → 显示"已申请(公示期)"
     ↓
公示期结束 → 显示"待退款(公示期结束)"
     ↓
管理员处理
     ↓
同意退款 → 实际退款 → 显示"已退款"
     ↓
拒绝退款 → 显示"拒绝退款"
```

## 🎨 界面效果

### 列表页面
- **退款状态列** 根据公示期动态显示状态和颜色
- **公示期结束时间列** 显示具体的公示期结束时间，并标注当前状态
- **操作列** 根据状态显示相应的操作按钮

#### 公示期结束时间列显示逻辑
| 状态 | 显示内容 | 颜色 |
|------|----------|------|
| 公示期进行中 | 2024-12-18 15:30<br><small>公示期进行中</small> | 橙色 |
| 公示期已结束 | 2024-12-18 15:30<br><small>公示期已结束</small> | 蓝色 |
| 非退款申请状态 | - | 灰色 |

### 处理退款页面
- 显示商家信息、保证金信息、申请信息
- 显示公示期开始和结束时间
- 提供同意/拒绝退款选项
- 拒绝时必须填写备注说明

## 🔧 技术特点

### 1. **动态状态计算**
- 实时计算公示期是否结束
- 根据时间动态显示状态

### 2. **灵活的操作控制**
- 根据业务状态控制按钮显示
- 防止误操作和非法操作

### 3. **完整的业务流程**
- 从申请到处理的完整流程
- 支持同意和拒绝两种处理结果

### 4. **用户体验优化**
- 清晰的状态显示
- 直观的操作按钮
- 友好的提示信息

## 📝 配置说明

### 公示期天数配置
在后台配置中设置：
- 配置项：`shop_entry.deposit_publicity_days`
- 默认值：7天
- 说明：商家申请退款后的公示期天数

### 相关接口
- `GET /admin/shop.Apply/deposit` - 保证金列表
- `POST /admin/shop.Apply/processRefund` - 处理退款申请
- `GET /admin/shop.Apply/refundDetail` - 退款详情

## 🎉 优势总结

1. **业务流程清晰** - 明确的公示期概念和状态流转
2. **操作体验优化** - 根据状态动态显示操作选项
3. **数据完整性** - 完整记录公示期时间和处理过程
4. **扩展性良好** - 易于扩展其他退款相关功能
5. **安全性保障** - 严格的状态检查和权限控制

通过这个功能，管理员可以清楚地了解每个退款申请的当前状态，并在合适的时机进行相应的操作，大大提升了保证金管理的效率和准确性。
