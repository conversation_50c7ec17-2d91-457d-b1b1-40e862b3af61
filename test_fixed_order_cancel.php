<?php
/**
 * 测试修复后的订单取消功能
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 测试修复后的订单取消功能 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 1. 推送订单取消任务
    echo "1. 推送订单取消任务...\n";
    $orderData = [
        'order_id' => 335,  // 使用真实的订单ID
        'user_id' => 1168   // 使用真实的用户ID
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJob', $orderData, 'orderCancel');
    echo "   ✓ 订单取消任务推送成功\n";
    echo "   任务数据: " . json_encode($orderData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "2. 修复内容:\n";
    echo "   ✓ 修复了模型类路径错误\n";
    echo "   ✓ 添加了缺失的use语句\n";
    echo "   ✓ 修复了枚举类引用\n";
    echo "   ✓ 分离了订单状态更新和后续处理\n\n";
    
    echo "3. 启动队列监听:\n";
    echo "   php start_queue.php\n\n";
    
    echo "4. 监控队列状态:\n";
    echo "   php monitor_queue.php\n\n";
    
    echo "=== 测试完成 ===\n";
    echo "现在队列任务应该能正常执行了！\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
