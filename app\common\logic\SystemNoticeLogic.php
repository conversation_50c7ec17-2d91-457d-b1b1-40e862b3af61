<?php
namespace app\common\logic;

use think\facade\Cache;
use think\facade\Log;
use app\common\basics\Logic;
use app\common\enum\NoticeEnum;
use app\common\model\Notice;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;

/**
 * 系统通知逻辑
 * Class SystemNoticeLogic
 * @package app\common\logic
 */
class SystemNoticeLogic extends Logic
{
    /**
     * 发送系统通知
     *
     * @param string $title 标题
     * @param string $content 内容
     * @param string $type 类型 (system, error, warning, info)
     * @param string $url 跳转链接
     * @return bool
     */
    public static function send(string $title, string $content, string $type = 'system', string $url = ''): bool
    {
        try {
            $redis = Cache::store('swoole_notice')->handler();
            $channel = 'notification'; // 与swoole.php中配置的订阅频道一致

            $notificationData = [
                'event' => 'notification',
                'data' => [
                    'title'   => $title,
                    'content' => $content,
                    'type'    => 'notification',
                    'url'     => $url,
                    'client'  => 5,
                    'icon'    => self::getIconByType($type),
                ],
            ];

            $message = json_encode($notificationData);
            $redis->publish($channel, $message);
            Log::info("发布系统通知到Redis:" . $message);
           
            return true;
        } catch (\Exception $e) {
            Log::error("发布系统通知到Redis失败:" . $e->getMessage());
           
            return false;
        }
    }

    /**
     * 根据类型获取图标
     * @param string $type
     * @return int
     */
    private static function getIconByType(string $type): int
    {
        $iconMap = [
            'system'  => 1,
            'error'   => 2,
            'warning' => 3,
            'info'    => 4,
            'default' => 0,
        ];
        return $iconMap[$type] ?? 0;
    }


      public static function index($user_id)
    {
        //最新系统消息
        $server = Notice::where([
            ['user_id', '=', $user_id],
            ['send_type', '=', NoticeEnum::SYSTEM_NOTICE],
            ['scene', 'not in', [NoticeEnum::GET_EARNINGS_NOTICE, NoticeEnum::GET_FUTURE_EARNINGS_NOTICE]],
        ])->order('id desc')->find();

        //最新收益通知
        $earning = Notice::where([
            ['user_id', '=', $user_id],
            ['send_type', '=', NoticeEnum::SYSTEM_NOTICE],
            ['scene', 'in', [NoticeEnum::GET_EARNINGS_NOTICE, NoticeEnum::GET_FUTURE_EARNINGS_NOTICE]],
        ])->order('id desc')->find();

        $data['system'] = [
            'title' => '系统通知',
            'content' => $server['content'] ?? '暂无系统消息',
            'img' => UrlServer::getFileUrl(ConfigServer::get('website', 'system_notice')),
            'type' => 'system',
        ];
        // $data['earning'] = [
        //     'title' => '收益通知',
        //     'content' => $earning['content'] ?? '暂无收益消息',
        //     'img' => UrlServer::getFileUrl(ConfigServer::get('website', 'earning_notice')),
        //     'type' => 'earning',
        // ];
        $res = array_values($data);
        return $res;
    }



    /**
     * Notes: 消息列表
     * @param $user_id
     * @param $type
     * @param $page
     * @param $size
     * <AUTHOR> 1:18)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function lists($user_id, $type, $page, $size)
    {
        $where = [];
        $where[] = ['user_id', '=', $user_id];
        $where[] = ['send_type', '=', NoticeEnum::SYSTEM_NOTICE];

        if ($type == 'earning') {
            $where[] = ['scene', 'in', [NoticeEnum::GET_EARNINGS_NOTICE, NoticeEnum::GET_FUTURE_EARNINGS_NOTICE]];
        } else {
            $where[] = ['scene', 'not in', [NoticeEnum::GET_EARNINGS_NOTICE, NoticeEnum::GET_FUTURE_EARNINGS_NOTICE]];
        }

        $count = Notice::where($where)->count();
        $lists = Notice::where($where)
            ->order('id desc')
            ->page($page, $size)
            ->select();

        //更新为已读
        Notice::where($where)
            ->where('read', '<>', 1)
            ->update(['read' => 1]);

        return [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }


    /**
     * Notes: 是否有未读的消息
     * @param $user_id
     * <AUTHOR> 1:17)
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function unRead($user_id)
    {
        $un_read = Notice::where([
            'user_id' => $user_id,
            'read' => 0,
            'send_type' => NoticeEnum::SYSTEM_NOTICE
        ])->find();
        if ($un_read) {
            return true;
        }
        return false;
    }
}