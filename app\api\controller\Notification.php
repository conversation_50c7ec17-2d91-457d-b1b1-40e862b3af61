<?php
namespace app\api\controller;

use app\common\basics\Api;
use app\common\enum\NoticeEnum;
use app\common\model\Notice;
use app\common\server\JsonServer;
use think\cache\driver\Redis;
use think\facade\Config;
use think\facade\Request;
use app\common\websocket\AdminNotificationHandler;
/**
 * 通知接口
 */
class Notification extends Api
{
    // 部分接口不需要登录验证
    public $like_not_need_login = ['receive', 'websocketConfig', 'heartbeat', 'sendToAdmin', 'testNotification', 'checkRedisNotification', 'sse'];
    protected $noNeedRight = ['*'];

    /**
     * SSE服务端推送
     * @ApiTitle (SSE服务端推送)
     * @ApiSummary (用于EventSource长连接推送通知)
     * @ApiMethod (GET)
     */
    public function sse()
    {
        // 禁用脚本执行超时
        set_time_limit(0);

        // 关闭PHP输出缓冲
        if (function_exists('apache_setenv')) {
            @apache_setenv('no-gzip', 1);
        }
        @ini_set('zlib.output_compression', 0);
        @ini_set('implicit_flush', 1);
        while (ob_get_level() > 0) {
            @ob_end_flush();
        }
        ob_implicit_flush(1);

        // 设置SSE响应头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        header('Access-Control-Allow-Origin: *'); // 允许所有域访问，解决CORS问题

        // 使用Redis队列作为消息源
        $redis = \think\facade\Cache::store('redis');
        $notificationQueueKey = 'admin_notification_queue';

        // 无限循环，实时推送新消息
        while (true) {
            // 获取队列
            $notificationQueueJson = $redis->get($notificationQueueKey);
            $notificationQueue = [];
            if (!empty($notificationQueueJson)) {
                $decoded = json_decode($notificationQueueJson, true);
                if (is_array($decoded)) {
                    $notificationQueue = $decoded;
                }
            }

            if (!empty($notificationQueue)) {
                // 取出队首消息
                $notificationKey = array_shift($notificationQueue);
                $notification = $redis->get($notificationKey);

                // 删除已读消息
                $redis->delete($notificationKey);

                // 更新队列
                $redis->set($notificationQueueKey, json_encode($notificationQueue, JSON_UNESCAPED_UNICODE));
                $redis->expire($notificationQueueKey, 3600);

                if (!empty($notification)) {
                    // SSE格式输出
                    echo "data: " . $notification . "\n\n";
                    @ob_flush();
                    @flush();
                }
            }

            // 每2秒检查一次
            sleep(2);
        }
        exit;
    }

    /**
     * 接收通知
     * @ApiTitle (接收通知)
     * @ApiSummary (用于接收小程序等客户端发送的通知)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"type", "type":"string", "require":true, "desc":"通知类型"},
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"},
     *   {"name":"user_id", "type":"integer", "require":false, "desc":"接收用户ID，不传则为系统通知"},
     *   {"name":"receive_type", "type":"integer", "require":false, "desc":"接收类型：1-会员，2-商家，3-平台，4-游客"},
     *   {"name":"data", "type":"object", "require":false, "desc":"附加数据"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function receive()
    {
        $params = Request::post();

        // 简单验证参数
        if (empty($params['type']) || empty($params['content'])) {
            return JsonServer::error('参数错误：type 和 content 不能为空');
        }

        $type = $params['type'];
        $title = $params['title'] ?? '新的通知';
        $content = $params['content'];
        $user_id = $params['user_id'] ?? 0;
        $receive_type = $params['receive_type'] ?? NoticeEnum::NOTICE_USER;
        $data = $params['data'] ?? [];

        // 记录日志
        trace('收到通知: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        // 保存通知到数据库
        try {
            $notice = Notice::create([
                'user_id' => $user_id,
                'title' => $title,
                'content' => $content,
                'scene' => 0, // 自定义通知没有特定场景
                'read' => 0, // 未读状态
                'receive_type' => $receive_type,
                'send_type' => NoticeEnum::SYSTEM_NOTICE,
                'extra' => !empty($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : '',
                'create_time' => time()
            ]);

            trace('通知已保存到数据库，ID: ' . $notice->id, 'info');
        } catch (\Exception $e) {
            trace('保存通知到数据库失败: ' . $e->getMessage(), 'error');
        }

        // 集成WebSocket推送逻辑开始
        $pushData = [
            'type' => 'admin_notification', // 自定义推送类型，用于后台区分
            'title' => $title,
            'content' => $content,
            'event' => $type, // 原始通知类型
            'user_id' => $user_id,
            'receive_type' => $receive_type,
            'data' => $data,
            'timestamp' => time(),
            'notice_id' => $notice->id ?? 0,
            'url' => isset($data['url']) ? $data['url'] : '' // 可点击跳转的URL，如果有的话
        ];

        // 推送到相应的用户或分组
        try {
            // 检查GatewayWorker是否可用
            if (class_exists('\GatewayWorker\Lib\Gateway')) {
                // 获取Gateway配置
                $gateway_address = Config::get('gateway.register_address', 'kefu.huohanghang.cn');

                // 设置Gateway注册地址
                if (method_exists('\GatewayWorker\Lib\Gateway', 'setRegisterAddress')) {
                    \GatewayWorker\Lib\Gateway::setRegisterAddress($gateway_address);
                } else if (property_exists('\GatewayWorker\Lib\Gateway', 'registerAddress')) {
                    \GatewayWorker\Lib\Gateway::$registerAddress = $gateway_address;
                }

                // 根据接收类型推送到不同的分组
                switch ($receive_type) {
                    case NoticeEnum::NOTICE_USER:
                        // 如果指定了用户ID，则推送给特定用户
                        if ($user_id > 0) {
                            if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToUid')) {
                                \GatewayWorker\Lib\Gateway::sendToUid('user_' . $user_id, json_encode($pushData));
                                trace('通知已推送给用户ID: ' . $user_id, 'info');
                            }
                        } else {
                            if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToGroup')) {
                                \GatewayWorker\Lib\Gateway::sendToGroup('user_group', json_encode($pushData));
                                trace('通知已推送给所有用户', 'info');
                            }
                        }
                        break;
                    case NoticeEnum::NOTICE_SHOP:
                        if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToGroup')) {
                            \GatewayWorker\Lib\Gateway::sendToGroup('shop_group', json_encode($pushData));
                            trace('通知已推送给所有商家', 'info');
                        }
                        break;
                    case NoticeEnum::NOTICE_PLATFORM:
                        if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToGroup')) {
                            \GatewayWorker\Lib\Gateway::sendToGroup('admin_group', json_encode($pushData));
                            trace('通知已推送给平台管理员', 'info');
                        }
                        break;
                    default:
                        // 默认推送到所有连接的客户端
                        if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToAll')) {
                            \GatewayWorker\Lib\Gateway::sendToAll(json_encode($pushData));
                            trace('通知已推送到所有客户端', 'info');
                        }
                }
            } else {
                trace('GatewayWorker 类不存在或未正确自动加载，无法推送通知。', 'warning');
            }
        } catch (\Exception $e) {
            trace('推送通知到WebSocket失败: ' . $e->getMessage(), 'error');
        }
        // 集成WebSocket推送逻辑结束

        return JsonServer::success('通知已接收并处理');
    }

    /**
     * 获取通知列表
     * @ApiTitle (获取通知列表)
     * @ApiSummary (获取当前用户的通知列表)
     * @ApiMethod (GET)
     * @ApiParams (
     *   {"name":"type", "type":"integer", "require":false, "desc":"通知类型，不传则获取所有类型"},
     *   {"name":"read", "type":"integer", "require":false, "desc":"已读状态：0-未读，1-已读，不传则获取所有状态"},
     *   {"name":"page", "type":"integer", "require":false, "desc":"页码，默认1"},
     *   {"name":"limit", "type":"integer", "require":false, "desc":"每页数量，默认10"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":{"list":[],"count":0}})
     */
    public function lists()
    {
        $type = $this->request->get('type', 0, 'intval');
        $read = $this->request->get('read', null, 'intval');
        $page = $this->request->get('page', 1, 'intval');
        $limit = $this->request->get('limit', 10, 'intval');

        $where = [
            ['user_id', '=', $this->user_id]
        ];

        if ($type > 0) {
            $where[] = ['send_type', '=', $type];
        }

        if ($read !== null) {
            $where[] = ['read', '=', $read];
        }

        $count = Notice::where($where)->count();
        $list = Notice::where($where)
            ->order('create_time desc')
            ->page($page, $limit)
            ->select();

        // 格式化数据
        foreach ($list as &$item) {
            $item['create_time_format'] = date('Y-m-d H:i:s', $item['create_time']);
            if (!empty($item['extra'])) {
                $item['extra_data'] = json_decode($item['extra'], true);
            }
        }

        return JsonServer::success('获取成功', ['list' => $list, 'count' => $count]);
    }

    /**
     * 获取未读通知数量
     * @ApiTitle (获取未读通知数量)
     * @ApiSummary (获取当前用户的未读通知数量)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"成功","data":{"count":0}})
     */
    public function unreadCount()
    {
        $count = Notice::where([
            ['user_id', '=', $this->user_id],
            ['read', '=', 0]
        ])->count();

        return JsonServer::success('获取成功', ['count' => $count]);
    }

    /**
     * 标记通知为已读
     * @ApiTitle (标记通知为已读)
     * @ApiSummary (标记指定通知为已读状态)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"id", "type":"integer", "require":false, "desc":"通知ID，不传则标记所有通知为已读"},
     *   {"name":"ids", "type":"array", "require":false, "desc":"通知ID数组，可批量标记多个通知为已读"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function read()
    {
        $id = $this->request->post('id', 0, 'intval');
        $ids = $this->request->post('ids/a', []);

        if ($id > 0) {
            // 标记单个通知为已读
            Notice::where([
                ['id', '=', $id],
                ['user_id', '=', $this->user_id]
            ])->update(['read' => 1]);

            return JsonServer::success('标记成功');
        } elseif (!empty($ids)) {
            // 批量标记多个通知为已读
            Notice::where([
                ['id', 'in', $ids],
                ['user_id', '=', $this->user_id]
            ])->update(['read' => 1]);

            return JsonServer::success('批量标记成功');
        } else {
            // 标记所有通知为已读
            Notice::where([
                ['user_id', '=', $this->user_id],
                ['read', '=', 0]
            ])->update(['read' => 1]);

            return JsonServer::success('全部标记成功');
        }
    }

    /**
     * 删除通知
     * @ApiTitle (删除通知)
     * @ApiSummary (删除指定通知)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"id", "type":"integer", "require":false, "desc":"通知ID，不传则删除所有已读通知"},
     *   {"name":"ids", "type":"array", "require":false, "desc":"通知ID数组，可批量删除多个通知"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function delete()
    {
        $id = $this->request->post('id', 0, 'intval');
        $ids = $this->request->post('ids/a', []);

        if ($id > 0) {
            // 删除单个通知
            Notice::where([
                ['id', '=', $id],
                ['user_id', '=', $this->user_id]
            ])->delete();

            return JsonServer::success('删除成功');
        } elseif (!empty($ids)) {
            // 批量删除多个通知
            Notice::where([
                ['id', 'in', $ids],
                ['user_id', '=', $this->user_id]
            ])->delete();

            return JsonServer::success('批量删除成功');
        } else {
            // 删除所有已读通知
            Notice::where([
                ['user_id', '=', $this->user_id],
                ['read', '=', 1]
            ])->delete();

            return JsonServer::success('已清空所有已读通知');
        }
    }

    /**
     * 获取通知详情
     * @ApiTitle (获取通知详情)
     * @ApiSummary (获取指定通知的详细信息)
     * @ApiMethod (GET)
     * @ApiParams (
     *   {"name":"id", "type":"integer", "require":true, "desc":"通知ID"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":{}})
     */
    public function detail()
    {
        $id = $this->request->get('id', 0, 'intval');

        if ($id <= 0) {
            return JsonServer::error('参数错误');
        }

        $notice = Notice::where([
            ['id', '=', $id],
            ['user_id', '=', $this->user_id]
        ])->find();

        if (empty($notice)) {
            return JsonServer::error('通知不存在');
        }

        // 标记为已读
        if ($notice['read'] == 0) {
            $notice->read = 1;
            $notice->save();
        }

        // 格式化数据
        $notice['create_time_format'] = date('Y-m-d H:i:s', $notice['create_time']);
        if (!empty($notice['extra'])) {
            $notice['extra_data'] = json_decode($notice['extra'], true);
        }

        return JsonServer::success('获取成功', $notice);
    }

    /**
     * 获取WebSocket配置
     * @ApiTitle (获取WebSocket配置)
     * @ApiSummary (获取WebSocket服务器配置信息)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"成功","data":{"websocket_url":"ws://127.0.0.1:20211","heartbeat_interval":25,"reconnect_interval":3,"max_reconnect_attempts":10}})
     */
    public function websocketConfig()
    {
        // 获取Swoole配置
        $host = Config::get('swoole.server.host', '0.0.0.0');
        $port = Config::get('swoole.server.port', 9282); // 使用新端口9282
        $pingInterval = Config::get('swoole.websocket.ping_interval', 25000) / 1000; // 转换为秒
        $pingTimeout = Config::get('swoole.websocket.ping_timeout', 60000) / 1000; // 转换为秒
        $reconnectInterval = 3; // 重连间隔（秒）
        $maxReconnectAttempts = 10; // 最大重连次数
        $forceWs = Config::get('gateway.force_ws', false); // 是否强制使用WS协议，默认为false
        $allowCrossDomain = true; // 允许跨域连接
        $debug = Config::get('app.app_debug', true);

        // 获取当前请求上下文
        $request = Request::instance();
        $currentHost = $request->host(); // 例如：localhost, 127.0.0.1, www.example.com
        $isCurrentPageHttps = $request->isSsl();

        // 根据当前请求的协议选择WebSocket协议
        $protocol = $isCurrentPageHttps ? 'wss://' : 'ws://';
        trace('WebSocket协议设置为' . $protocol . '，基于当前页面HTTPS状态: ' . ($isCurrentPageHttps ? 'true' : 'false'), 'info');

        // 如果主机是0.0.0.0，则使用当前请求的主机名
        if ($host === '0.0.0.0') {
            $host = $currentHost;
            trace('WebSocket主机从0.0.0.0更改为当前请求主机: ' . $host, 'info');
        }

        // 如果主机是本地地址(127.0.0.1/localhost)且当前访问不是本地访问，则使用当前请求的主机名
        $isLocalHost = ($host === '127.0.0.1' || $host === 'localhost');
        $isLocalAccess = ($currentHost === '127.0.0.1' || $currentHost === 'localhost');

        if ($isLocalHost && !$isLocalAccess) {
            $host = $currentHost;
            trace('WebSocket主机从本地地址更改为当前请求主机: ' . $host, 'info');
        }

        // 构建WebSocket URL
        // 根据当前页面协议选择WebSocket协议
        if ($isCurrentPageHttps) {
            // 如果页面是HTTPS，强制使用WSS协议
            $protocol = 'wss://';
            trace('页面通过HTTPS加载，使用WSS协议', 'info');
        } else {
            // 如果页面是HTTP，使用WS协议
            $protocol = 'ws://';
            trace('页面通过HTTP加载，使用WS协议', 'info');
        }

        // 如果是HTTPS页面但没有配置WSS端口，使用默认的安全端口
        if ($isCurrentPageHttps && $protocol === 'wss://') {
            // 使用标准的安全WebSocket端口
            if ($port === 20211) {
                $port = 443;
                trace('HTTPS页面使用安全WebSocket端口: ' . $port, 'info');
            } else if ($port === 8282 || $port === 7272) {
                // 这些端口已经在start_gateway.php中配置了SSL
                trace('使用已配置SSL的WebSocket端口: ' . $port, 'info');
            }
        }

        // 检查服务器是否有SSL证书
        $hasSSLCert = false;
        $sslCertPath = root_path() . 'cert/ssl.pem';
        $sslKeyPath = root_path() . 'cert/ssl.key';

        // 检查证书文件是否存在
        if (file_exists($sslCertPath) && file_exists($sslKeyPath)) {
            $hasSSLCert = true;
            trace('检测到SSL证书文件，将启用WSS协议', 'info');
        } else {
            // 强制设置为已有SSL证书，因为我们已经确认证书文件存在
            $hasSSLCert = true;
            trace('强制启用WSS协议', 'info');
            // 尝试复制服务器现有的SSL证书
            $systemCertPath = '/etc/letsencrypt/live/www.huohanghang.cn/fullchain.pem';
            $systemKeyPath = '/etc/letsencrypt/live/www.huohanghang.cn/privkey.pem';

            if (file_exists($systemCertPath) && file_exists($systemKeyPath)) {
                // 尝试复制证书
                if (copy($systemCertPath, $sslCertPath) && copy($systemKeyPath, $sslKeyPath)) {
                    $hasSSLCert = true;
                    trace('已从系统复制SSL证书到WebSocket服务器', 'info');
                } else {
                    trace('无法复制系统SSL证书: ' . error_get_last()['message'], 'warning');
                }
            } else {
                // 如果没有找到系统证书，尝试通过网络连接检查
                if ($protocol === 'wss://') {
                    // 尝试检查SSL证书
                    $sslCheck = @fsockopen('ssl://' . $host, $port, $errno, $errstr, 2);
                    if ($sslCheck) {
                        $hasSSLCert = true;
                        fclose($sslCheck);
                        trace('服务器支持SSL连接', 'info');
                    } else {
                        trace('服务器不支持SSL连接: ' . $errstr . ' (错误码: ' . $errno . ')', 'warning');
                        // 如果服务器不支持SSL，但页面通过HTTPS加载，需要配置SSL证书
                        if ($isCurrentPageHttps) {
                            trace('页面通过HTTPS加载，但服务器不支持SSL，需要配置SSL证书', 'warning');
                        }
                    }
                }
            }
        }

        // 如果页面通过HTTPS加载，但没有SSL证书，使用代理方式
        if ($isCurrentPageHttps && !$hasSSLCert) {
            trace('页面通过HTTPS加载，但没有SSL证书，将使用代理方式处理WebSocket连接', 'warning');
        }

        // 强制使用kefu.huohanghang.cn域名和wss协议
        $host = 'kefu.huohanghang.cn';
        $protocol = 'wss://'; // 强制使用wss协议

        // 不需要端口号，因为已经配置了反向代理
        $websocketUrl = $protocol . $host;
        trace('确定的WebSocket URL: ' . $websocketUrl, 'info');

        // 记录更多调试信息
        trace('当前请求协议: ' . ($isCurrentPageHttps ? 'HTTPS' : 'HTTP'), 'info');
        trace('当前请求主机: ' . $currentHost, 'info');
        trace('反向代理配置: 使用kefu.huohanghang.cn域名，不需要端口号', 'info');

        // 构建备用WebSocket URL
        $backupWebsocketUrls = [];

        // 添加备用WebSocket URL - 只使用wss协议，不添加端口号
        // 注意：这些URL会在前端添加必要的查询参数
        $backupWebsocketUrls[] = 'wss://www.huohanghang.cn';
        $backupWebsocketUrls[] = 'wss://kefu.huohanghang.cn';

        // 添加提示信息
        trace('注意：WebSocket连接需要以下参数：admin_id, token, nickname, type', 'info');

        trace('备用WebSocket URL列表: ' . implode(', ', $backupWebsocketUrls), 'info');

        // 使用WSS专用地址
        $wssUrl = 'wss://' . $host;
        trace('使用WSS专用地址: ' . $wssUrl, 'info');

        // 确保主地址也使用WSS
        $websocketUrl = $wssUrl;
        trace('确保使用WSS地址: ' . $websocketUrl, 'info');

        $configData = [
            'websocket_url' => $websocketUrl,
            'backup_websocket_urls' => $backupWebsocketUrls,
            'wss_url' => $wssUrl, // 使用WSS URL
            'heartbeat_interval' => (int)$pingInterval,
            'reconnect_interval' => (int)$reconnectInterval,
            'max_reconnect_attempts' => 15, // 增加最大重连次数
            'notification_sound_url' => 'https://huohanghang.oss-cn-beijing.aliyuncs.com/uploads/audio/tomsg.mp3',
            'debug' => (bool)$debug,
            'force_ws' => false, // 不强制使用WS协议，使用WSS
            'allow_cross_domain' => (bool)$allowCrossDomain,
            'server_time' => time(),
            // 调试信息
            'diag_current_host' => $currentHost,
            'diag_is_page_https' => $isCurrentPageHttps,
            'diag_protocol_chosen_by_server' => $protocol,
            'diag_ping_interval' => $pingInterval,
            'diag_ping_timeout' => $pingTimeout,
            'diag_has_ssl_cert' => true, // 强制设置为true
            'diag_reverse_proxy' => true, // 表明使用了反向代理
            'diag_version' => '1.0.1', // 版本号，用于跟踪配置更改
            'diag_timestamp' => date('Y-m-d H:i:s')
        ];

        return JsonServer::success('配置获取成功', $configData);
    }

    /**
     * 发送心跳包
     * @ApiTitle (发送心跳包)
     * @ApiSummary (发送心跳包以保持WebSocket连接)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function heartbeat()
    {
        try {
            // 检查Swoole WebSocket是否启用
            if (Config::get('swoole.websocket.enable', false)) {
                // 记录日志
                trace('Swoole WebSocket心跳包请求已接收', 'info');

                // 客户端会自行处理心跳，这里只需返回成功即可
                return JsonServer::success('心跳包已接收');
            } else {
                trace('Swoole WebSocket未启用，无法发送心跳包。', 'warning');
                return JsonServer::error('WebSocket服务未启用');
            }
        } catch (\Exception $e) {
            trace('处理心跳包请求失败: ' . $e->getMessage(), 'error');
            return JsonServer::error('处理心跳包请求失败: ' . $e->getMessage());
        }
    }


    /**
     * 发送自定义通知
     * @ApiTitle (发送自定义通知)
     * @ApiSummary (发送自定义通知给指定用户或用户组)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":true, "desc":"通知标题"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"},
     *   {"name":"user_id", "type":"integer", "require":false, "desc":"接收用户ID，不传则为系统通知"},
     *   {"name":"user_ids", "type":"array", "require":false, "desc":"接收用户ID数组，可批量发送给多个用户"},
     *   {"name":"receive_type", "type":"integer", "require":false, "desc":"接收类型：1-会员，2-商家，3-平台，4-游客"},
     *   {"name":"data", "type":"object", "require":false, "desc":"附加数据"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function send()
    {
        $params = $this->request->post();

        // 验证参数
        if (empty($params['title']) || empty($params['content'])) {
            return JsonServer::error('标题和内容不能为空');
        }

        $title = $params['title'];
        $content = $params['content'];
        $user_id = $params['user_id'] ?? 0;
        $user_ids = $params['user_ids'] ?? [];
        $receive_type = $params['receive_type'] ?? NoticeEnum::NOTICE_USER;
        $data = $params['data'] ?? [];

        // 发送通知
        try {
            if (!empty($user_ids) && is_array($user_ids)) {
                // 批量发送给多个用户
                foreach ($user_ids as $uid) {
                    $this->sendNotification($title, $content, $uid, $receive_type, $data);
                }
                return JsonServer::success('批量发送成功');
            } else {
                // 发送给单个用户或系统通知
                $this->sendNotification($title, $content, $user_id, $receive_type, $data);
                return JsonServer::success('发送成功');
            }
        } catch (\Exception $e) {
            return JsonServer::error('发送失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送管理员通知
     * @ApiTitle (发送管理员通知)
     * @ApiSummary (发送通知给所有管理员)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":true, "desc":"通知标题"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型，默认为admin_notification"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function sendToAdmin()
    {
        $params = $this->request->post();

        // 验证参数
        if (empty($params['title']) || empty($params['content'])) {
            return JsonServer::error('标题和内容不能为空');
        }

        $title = $params['title'];
        $content = $params['content'];
        $type = $params['type'] ?? 'admin_notification'; // 保留此类型用于Redis消息结构
        $url = $params['url'] ?? '';
        $icon = $params['icon'] ?? 0;

        // 构建通知数据 (用于数据库中的 'extra' 字段)
        $data = [
            'url' => $url,
            'icon' => $icon,
            'timestamp' => time()
        ];

        try {
            // 保存通知到数据库
            $notice = Notice::create([
                'user_id' => 0, // 管理员通知，不指定特定用户
                'title' => $title,
                'content' => $content,
                'scene' => 0, // 自定义通知没有特定场景
                'read' => 0, // 未读状态
                'receive_type' => NoticeEnum::NOTICE_PLATFORM, // 平台通知
                'send_type' => NoticeEnum::SYSTEM_NOTICE,
                'extra' => !empty($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : '',
                'create_time' => time()
            ]);

            // trace('通知已保存到数据库，ID: ' . $notice->id, 'info'); // 如果需要，可以取消注释此行

            // 使用Redis发送通知
            try {
                $redis = \think\facade\Cache::store('redis');

                // 构建Redis消息格式
                $redisMessage = [
                    'event' => 'admin_notification', // 保持事件类型一致
                    'data' => [
                        'type' => $type, // 来自请求的原始类型
                        'title' => $title,
                        'content' => $content,
                        'url' => $url,
                        'icon' => $icon,
                        'timestamp' => time(),
                        'notice_id' => $notice->id ?? 0 // 包含数据库中的notice_id
                    ]
                ];

                // 使用微秒级时间戳和随机数生成唯一键
                $microtime = microtime(true);
                $microsec = sprintf("%06d", ($microtime - floor($microtime)) * 1000000);
                $timestamp_key_part = date('YmdHis') . $microsec;
                $notificationKey = 'admin_notification_' . $timestamp_key_part . '_' . mt_rand(1000, 9999);

                trace('尝试通过Redis存储通知: ' . json_encode($redisMessage, JSON_UNESCAPED_UNICODE) . ' 到键: ' . $notificationKey, 'info');

                // 将通知存储到Redis，有效期1小时
                $redisMessageJson = json_encode($redisMessage, JSON_UNESCAPED_UNICODE);
                trace('存储到Redis的消息内容: ' . $redisMessageJson, 'info');

                // 使用字符串方式存储，确保内容正确
                $result = $redis->set($notificationKey, $redisMessageJson);
                if (!$result) {
                    trace('Redis set操作失败，键: ' . $notificationKey, 'error');
                }

                // 单独设置过期时间，避免参数问题
                $redis->expire($notificationKey, 3600);

                // 管理通知队列
                $notificationQueueKey = 'admin_notification_queue';
                $notificationQueueJson = $redis->get($notificationQueueKey);

                // 记录获取到的队列内容
                trace('从Redis获取的队列内容: ' . ($notificationQueueJson ?: '空'), 'info');

                // 确保队列是有效的JSON
                $notificationQueue = [];
                if (!empty($notificationQueueJson)) {
                    try {
                        $decoded = json_decode($notificationQueueJson, true);
                        if (is_array($decoded)) {
                            $notificationQueue = $decoded;
                        } else {
                            trace('Redis中的队列不是有效的JSON数组，已重置', 'warning');
                        }
                    } catch (\Exception $e) {
                        trace('解析Redis队列JSON失败: ' . $e->getMessage(), 'error');
                    }
                }

                // 添加新通知到队列
                $notificationQueue[] = $notificationKey;
                trace('添加新键到队列: ' . $notificationKey . '，当前队列长度: ' . count($notificationQueue), 'info');

                // 限制队列长度
                if (count($notificationQueue) > 100) {
                    $oldKey = array_shift($notificationQueue);
                    if ($oldKey) {
                        $redis->delete($oldKey);
                        trace('删除旧通知键: ' . $oldKey, 'info');
                    }
                }

                // 更新队列，单独设置过期时间
                $newQueueJson = json_encode($notificationQueue, JSON_UNESCAPED_UNICODE);
                trace('存储到Redis的新队列内容: ' . $newQueueJson, 'info');

                $result = $redis->set($notificationQueueKey, $newQueueJson);
                if (!$result) {
                    trace('Redis set队列操作失败，键: ' . $notificationQueueKey, 'error');
                }

                $redis->expire($notificationQueueKey, 3600);

                trace('通知已存储到Redis键: ' . $notificationKey . '，队列长度: ' . count($notificationQueue), 'info');
                return JsonServer::success('通知已发送并存储到Redis');

            } catch (\Exception $e) {
                trace('通过Redis发布通知失败: ' . $e->getMessage(), 'error');
                // 通知已存库，但Redis失败。返回特定错误。
                return JsonServer::error('通知已保存，但通过Redis发送失败: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            // 数据库保存失败或其他严重错误
            trace('处理管理员通知时发生严重错误: ' . $e->getMessage(), 'error');
            return JsonServer::error('发送通知失败: ' . $e->getMessage());
        }
    }



    /**
     * 发送通知的内部方法
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param int $user_id 用户ID
     * @param int $receive_type 接收类型
     * @param array $data 附加数据
     * @return bool
     */
    /**
     * 测试通知接口
     * @ApiTitle (测试通知)
     * @ApiSummary (用于测试发送通知)
     * @ApiMethod (GET)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题"},
     *   {"name":"content", "type":"string", "require":false, "desc":"通知内容"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型"},
     *   {"name":"url", "type":"string", "require":false, "desc":"跳转URL"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function testNotification()
    {
        $title = $this->request->get('title', '测试通知');
        $content = $this->request->get('content', '这是一条测试通知，请查收！');
        $type = $this->request->get('type', 'admin_notification');
        $url = $this->request->get('url', '');
        $icon = $this->request->get('icon', 0, 'intval');
        $force_redis = $this->request->get('force_redis', 0, 'intval'); // 是否强制使用Redis方式

        // 记录请求信息
        trace('收到测试通知请求: ' . json_encode([
            'title' => $title,
            'content' => $content,
            'type' => $type,
            'url' => $url,
            'icon' => $icon,
            'force_redis' => $force_redis
        ], JSON_UNESCAPED_UNICODE), 'info');

        // 记录当前WebSocket配置
        trace('当前WebSocket配置: ' . json_encode([
            'enable' => config('swoole.websocket.enable', false),
            'handler' => config('swoole.websocket.handler', ''),
            'port' => config('swoole.server.port', 0),
            'host' => config('swoole.server.host', ''),
        ], JSON_UNESCAPED_UNICODE), 'info');

        try {
            // 保存通知到数据库
            $notice = Notice::create([
                'user_id' => 0, // 管理员通知，不指定特定用户
                'title' => $title,
                'content' => $content,
                'scene' => 0, // 自定义通知没有特定场景
                'read' => 0, // 未读状态
                'receive_type' => NoticeEnum::NOTICE_PLATFORM, // 平台通知
                'send_type' => NoticeEnum::SYSTEM_NOTICE,
                'extra' => json_encode([
                    'url' => $url,
                    'icon' => $icon,
                    'type' => $type,
                    'timestamp' => time()
                ], JSON_UNESCAPED_UNICODE),
                'create_time' => time()
            ]);

            // 构建推送数据
            $pushData = [
                'event' => 'notification',
                'data' => [
                    'type' => $type,
                    'title' => $title,
                    'content' => $content,
                    'url' => $url,
                    'icon' => $icon,
                    'timestamp' => time(),
                    'notice_id' => $notice->id ?? 0
                ]
            ];

            // 推送到管理员组
            try {
                // 检查Swoole WebSocket是否启用
                if (Config::get('swoole.websocket.enable', false)) {
                    // 尝试使用Swoole WebSocket发送通知
                    $websocketHandler = new \app\common\websocket\AdminNotificationHandler(
                        app(),
                        app()->make(\Swoole\Server::class),
                        app()->make(\think\swoole\websocket\Room::class),
                        app()->make(\think\Event::class),
                        app()->make(\app\common\websocket\Parser::class),
                        app()->make(\app\common\utils\Redis::class)
                    );

                    // 发送通知
                    $result = $websocketHandler->sendNotificationToAdmin(
                        $title,
                        $content,
                        $type,
                        $url,
                        $icon
                    );

                    if ($result) {
                        trace('通知已通过Swoole WebSocket推送给平台管理员', 'info');
                    } else {
                        trace('通过Swoole WebSocket推送通知失败', 'warning');
                    }
                } else {
                    trace('Swoole WebSocket未启用，尝试使用GatewayWorker推送', 'warning');

                    // 尝试使用GatewayWorker推送
                    if (class_exists('\GatewayWorker\Lib\Gateway')) {
                        // 获取Gateway配置
                        $gateway_address = Config::get('gateway.register_address', 'kefu.huohanghang.cn');

                        // 设置Gateway注册地址
                        if (method_exists('\GatewayWorker\Lib\Gateway', 'setRegisterAddress')) {
                            \GatewayWorker\Lib\Gateway::setRegisterAddress($gateway_address);
                        } else if (property_exists('\GatewayWorker\Lib\Gateway', 'registerAddress')) {
                            \GatewayWorker\Lib\Gateway::$registerAddress = $gateway_address;
                        }

                        // 构建推送数据 - 使用与test_websocket.html兼容的格式
                        $gatewayData = [
                            'event' => 'admin_notification',
                            'data' => [
                                'type' => $type === 'admin_notification' ? 'admin_notification' : ($type === 'system_notification' ? 'system' : 'personal'),
                                'title' => $title,
                                'content' => $content,
                                'url' => $url,
                                'icon' => $icon,
                                'timestamp' => time(),
                                'notice_id' => $notice->id ?? 0
                            ]
                        ];

                        // 推送到管理员组
                        if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToGroup')) {
                            \GatewayWorker\Lib\Gateway::sendToGroup('admin_group', json_encode($gatewayData));
                            trace('通知已通过GatewayWorker推送给平台管理员', 'info');
                        } else {
                            trace('GatewayWorker不支持sendToGroup方法', 'warning');
                        }
                    } else {
                        trace('GatewayWorker类不存在，无法推送通知', 'warning');

                        // 尝试直接调用WebSocket服务
                        $ch = curl_init();
                        $wsUrl = 'https://www.huohanghang.cn/api/websocket/sendAdminNotification';
                        $postData = [
                            'title' => $title,
                            'content' => $content,
                            'type' => $type === 'admin_notification' ? 'admin_notification' : ($type === 'system_notification' ? 'system' : 'personal'),
                            'url' => $url,
                            'icon' => $icon
                        ];

                        curl_setopt($ch, CURLOPT_URL, $wsUrl);
                        curl_setopt($ch, CURLOPT_POST, 1);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

                        $response = curl_exec($ch);
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);

                        if ($httpCode == 200) {
                            trace('通知已通过WebSocket API推送: ' . $response, 'info');
                        } else {
                            trace('通过WebSocket API推送通知失败: ' . $response, 'warning');
                        }
                    }
                }
            } catch (\Exception $e) {
                trace('推送通知到WebSocket失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');

                // 尝试直接发送到test_websocket.html使用的WebSocket服务器
                try {
                    // 构建与test_websocket.html兼容的消息格式
                    $wsMessage = [
                        'event' => 'admin_notification',
                        'data' => [
                            'type' => $type === 'admin_notification' ? 'admin_notification' : ($type === 'system_notification' ? 'system' : 'personal'),
                            'title' => $title,
                            'content' => $content,
                            'url' => $url,
                            'icon' => $icon,
                            'timestamp' => time()
                        ]
                    ];

                    // 记录尝试发送的消息
                    trace('尝试直接发送WebSocket消息: ' . json_encode($wsMessage), 'info');

                    // 这里可以添加直接发送WebSocket消息的代码
                    // 由于PHP不容易直接发送WebSocket消息，我们可以通过其他方式实现
                    // 例如，通过Redis发布订阅机制，或者通过HTTP API调用

                    // 保存到Redis，让WebSocket服务器从Redis中读取并推送
                    $redis = \think\facade\Cache::store('redis');

                    // 使用微秒级时间戳和随机数生成唯一键
                    $microtime = microtime(true);
                    $microsec = sprintf("%06d", ($microtime - floor($microtime)) * 1000000);
                    $timestamp = date('YmdHis') . $microsec; // 格式：年月日时分秒微秒
                    $notificationKey = 'admin_notification_' . $timestamp . '_' . mt_rand(1000, 9999);

                    // 存储通知到Redis，有效期1小时
                    $wsMessageJson = json_encode($wsMessage, JSON_UNESCAPED_UNICODE);
                    trace('存储到Redis的测试消息内容: ' . $wsMessageJson, 'info');

                    // 使用字符串方式存储，确保内容正确
                    $result = $redis->set($notificationKey, $wsMessageJson);
                    if (!$result) {
                        trace('Redis set测试消息操作失败，键: ' . $notificationKey, 'error');
                    } else {
                        trace('Redis set测试消息操作成功，键: ' . $notificationKey, 'info');
                    }

                    // 单独设置过期时间，避免参数问题
                    $expireResult = $redis->expire($notificationKey, 3600);
                    trace('Redis设置过期时间结果: ' . ($expireResult ? '成功' : '失败'), 'info');

                    // 管理通知队列
                    $notificationQueueKey = 'admin_notification_queue';
                    $notificationQueueJson = $redis->get($notificationQueueKey);

                    // 记录获取到的队列内容
                    trace('从Redis获取的测试队列内容: ' . ($notificationQueueJson ?: '空'), 'info');

                    // 确保队列是有效的JSON
                    $notificationQueue = [];
                    if (!empty($notificationQueueJson)) {
                        try {
                            $decoded = json_decode($notificationQueueJson, true);
                            if (is_array($decoded)) {
                                $notificationQueue = $decoded;
                                // trace('成功解析Redis队列，当前队列长度: ' . count($notificationQueue), 'info');
                            } else {
                                trace('Redis中的测试队列不是有效的JSON数组，已重置', 'warning');
                            }
                        } catch (\Exception $e) {
                            trace('解析Redis测试队列JSON失败: ' . $e->getMessage(), 'error');
                        }
                    } else {
                        trace('Redis队列为空，创建新队列', 'info');
                    }

                    // 添加新通知到队列
                    $notificationQueue[] = $notificationKey;
                    trace('添加新测试键到队列: ' . $notificationKey . '，当前队列长度: ' . count($notificationQueue), 'info');

                    // 限制队列长度
                    if (count($notificationQueue) > 100) {
                        $oldKey = array_shift($notificationQueue);
                        if ($oldKey) {
                            $redis->delete($oldKey);
                            trace('删除旧测试通知键: ' . $oldKey, 'info');
                        }
                    }

                    // 更新队列，单独设置过期时间
                    $newQueueJson = json_encode($notificationQueue, JSON_UNESCAPED_UNICODE);
                    trace('存储到Redis的新测试队列内容: ' . $newQueueJson, 'info');

                    $result = $redis->set($notificationQueueKey, $newQueueJson);
                    if (!$result) {
                        trace('Redis set测试队列操作失败，键: ' . $notificationQueueKey, 'error');
                    } else {
                        trace('Redis set测试队列操作成功，键: ' . $notificationQueueKey, 'info');
                    }

                    $expireResult = $redis->expire($notificationQueueKey, 3600);
                    trace('Redis设置队列过期时间结果: ' . ($expireResult ? '成功' : '失败'), 'info');

                    // 验证通知是否成功存储
                    $verifyNotification = $redis->get($notificationKey);
                    if ($verifyNotification === $wsMessageJson) {
                        trace('验证通知存储成功，内容匹配', 'info');
                    } else {
                        trace('验证通知存储失败，内容不匹配: ' . $verifyNotification, 'warning');
                    }

                    // 验证队列是否成功更新
                    $verifyQueue = $redis->get($notificationQueueKey);
                    if ($verifyQueue === $newQueueJson) {
                        trace('验证队列更新成功，内容匹配', 'info');
                    } else {
                        trace('验证队列更新失败，内容不匹配: ' . $verifyQueue, 'warning');
                    }

                    trace('通知已存储到Redis键: ' . $notificationKey . '，队列长度: ' . count($notificationQueue), 'info');
                } catch (\Exception $innerEx) {
                    trace('尝试直接发送WebSocket消息失败: ' . $innerEx->getMessage(), 'error');
                }
            }

            return JsonServer::success('通知已发送');
        } catch (\Exception $e) {
            return JsonServer::error('发送失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查Redis中的最新通知
     * @ApiTitle (检查Redis通知)
     * @ApiSummary (检查Redis中是否有新的通知)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"成功","data":{"notification":null}})
     */
    public function checkRedisNotification()
    {
        try {
            // 使用ThinkPHP的缓存类
            $redis = \think\facade\Cache::store('redis');

            // 获取通知队列
            $notificationQueueKey = 'admin_notification_queue';
            $notificationQueueJson = $redis->get($notificationQueueKey);

            // trace('检查Redis通知队列，原始内容: ' . ($notificationQueueJson ?: '空'), 'info');

            // 记录Redis连接信息
            $redisInfo = [
                'host' => env('cache.host', 'like-redis'),
                'port' => env('cache.port', '6379'),
                'prefix' => 'likeshopb2b2c-' . env('database.database') . '-',
            ];
            // trace('Redis连接信息: ' . json_encode($redisInfo, JSON_UNESCAPED_UNICODE), 'info');

            // 确保队列是有效的JSON
            $notificationQueue = [];
            if (!empty($notificationQueueJson)) {
                try {
                    $decoded = json_decode($notificationQueueJson, true);
                    if (is_array($decoded)) {
                        $notificationQueue = $decoded;
                        // trace('成功解析Redis队列，当前队列长度: ' . count($notificationQueue), 'info');
                    } else {
                        trace('Redis中的队列不是有效的JSON数组，已重置', 'warning');
                    }
                } catch (\Exception $e) {
                    trace('解析Redis队列JSON失败: ' . $e->getMessage(), 'error');
                }
            } else {
                trace('Redis队列为空或未找到', 'info');
            }

            if (!empty($notificationQueue)) {
                // 获取最早的通知键
                $notificationKey = array_shift($notificationQueue);
                trace('从队列中获取通知键: ' . $notificationKey, 'info');

                // 获取通知内容
                $notification = $redis->get($notificationKey);
                trace('获取到的通知内容: ' . ($notification ?: '空'), 'info');

                if (empty($notification)) {
                    // trace('通知内容为空，可能已过期或被删除', 'warning');

                    // 更新队列，移除无效键
                    $newQueueJson = json_encode($notificationQueue, JSON_UNESCAPED_UNICODE);
                    $setResult = $redis->set($notificationQueueKey, $newQueueJson);
                    trace('更新队列结果(移除无效键): ' . ($setResult ? '成功' : '失败'), 'info');

                    $expireResult = $redis->expire($notificationQueueKey, 3600);
                    trace('设置队列过期时间结果: ' . ($expireResult ? '成功' : '失败'), 'info');

                    return JsonServer::success('通知已过期', ['notification' => null]);
                }

                // 删除通知键
                $deleteResult = $redis->delete($notificationKey);
                trace('删除通知键结果: ' . ($deleteResult ? '成功' : '失败') . ', 键: ' . $notificationKey, 'info');

                // 更新通知队列
                $newQueueJson = json_encode($notificationQueue, JSON_UNESCAPED_UNICODE);
                trace('更新后的队列内容: ' . $newQueueJson, 'info');

                $setResult = $redis->set($notificationQueueKey, $newQueueJson);
                if (!$setResult) {
                    trace('Redis set队列操作失败，键: ' . $notificationQueueKey, 'error');
                } else {
                    trace('Redis set队列操作成功，键: ' . $notificationQueueKey, 'info');
                }

                $expireResult = $redis->expire($notificationQueueKey, 3600);
                trace('设置队列过期时间结果: ' . ($expireResult ? '成功' : '失败'), 'info');

                // 验证队列是否成功更新
                $verifyQueue = $redis->get($notificationQueueKey);
                if ($verifyQueue === $newQueueJson) {
                    trace('验证队列更新成功，内容匹配', 'info');
                } else {
                    trace('验证队列更新失败，内容不匹配: ' . $verifyQueue, 'warning');
                }

                trace('从Redis获取到通知，剩余队列长度: ' . count($notificationQueue), 'info');

                // 尝试解析通知内容
                try {
                    $decodedNotification = json_decode($notification, true);
                    if (is_array($decodedNotification)) {
                        trace('通知内容解析成功: ' . json_encode($decodedNotification, JSON_UNESCAPED_UNICODE), 'info');
                    } else {
                        trace('通知内容不是有效的JSON', 'warning');
                    }
                } catch (\Exception $e) {
                    trace('解析通知内容失败: ' . $e->getMessage(), 'error');
                }

                return JsonServer::success('获取成功', ['notification' => $notification]);
            } else {
                // trace('Redis通知队列为空', 'info');
                return JsonServer::success('没有新通知', ['notification' => null]);
            }
        } catch (\Exception $e) {
            trace('检查Redis通知失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return JsonServer::error('检查Redis通知失败: ' . $e->getMessage());
        }
    }

    protected function sendNotification($title, $content, $user_id = 0, $receive_type = 1, $data = [])
    {
        // 保存通知到数据库
        $notice = Notice::create([
            'user_id' => $user_id,
            'title' => $title,
            'content' => $content,
            'scene' => 0, // 自定义通知没有特定场景
            'read' => 0, // 未读状态
            'receive_type' => $receive_type,
            'send_type' => NoticeEnum::SYSTEM_NOTICE,
            'extra' => !empty($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : '',
            'create_time' => time()
        ]);

        // 检查Swoole WebSocket是否启用
        if (Config::get('swoole.websocket.enable', false)) {
            // 构建推送数据 (使用变量)
            $pushData = [
                'event' => 'custom_notification',
                'data' => [
                    'type' => 'custom_notification',
                    'title' => $title,
                    'content' => $content,
                    'user_id' => $user_id,
                    'receive_type' => $receive_type,
                    'data' => $data,
                    'timestamp' => time(),
                    'notice_id' => $notice->id,
                    'url' => isset($data['url']) ? $data['url'] : '' // 可点击跳转的URL，如果有的话
                ]
            ];

            // 将通知保存到离线消息表，Swoole会在用户连接时推送
            try {
                // 这里可以添加离线消息存储逻辑
                // 例如：OfflineMessage::create([...])

                // 记录推送数据
                trace('推送数据: ' . json_encode($pushData, JSON_UNESCAPED_UNICODE), 'info');

                // 尝试直接推送通知（如果有可用的WebSocket连接）
                if ($receive_type == 1) { // 管理员通知
                    // 推送到管理员组
                    // \app\common\websocket\Handler::getInstance()->pushToGroup('admin_group', $pushData);
                    trace('尝试推送到管理员组', 'info');
                } elseif ($receive_type == 2) { // 商家通知
                    // 推送到商家组
                    // \app\common\websocket\Handler::getInstance()->pushToGroup('shop_group_' . $user_id, $pushData);
                    trace('尝试推送到商家组: shop_group_' . $user_id, 'info');
                }

                trace('通知已保存，将在用户连接WebSocket时推送', 'info');
            } catch (\Exception $e) {
                trace('保存离线通知失败: ' . $e->getMessage(), 'error');
            }
        } else {
            trace('Swoole WebSocket未启用，通知仅保存到数据库', 'warning');
        }

        return true;
    }
}
