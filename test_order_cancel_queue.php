<?php
/**
 * 订单取消队列功能测试脚本
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 订单取消队列功能测试 ===\n\n";

try {
    // 1. 测试队列连接
    echo "1. 测试队列连接...\n";
    $queue = \think\facade\Queue::connection('redis');
    echo "   ✓ 队列连接成功\n\n";
    
    // 2. 测试推送任务到队列
    echo "2. 测试推送订单取消任务...\n";
    $testData = [
        'order_id' => 999999, // 测试订单ID
        'user_id' => 1        // 测试用户ID
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJob', $testData, 'orderCancel');
    echo "   ✓ 任务推送成功\n";
    echo "   任务数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 3. 检查队列配置
    echo "3. 检查队列配置...\n";
    $config = config('queue');
    echo "   默认驱动: " . $config['default'] . "\n";
    echo "   Redis主机: " . $config['connections']['redis']['host'] . "\n";
    echo "   Redis端口: " . $config['connections']['redis']['port'] . "\n";
    echo "   队列名称: " . $config['connections']['redis']['queue'] . "\n\n";
    
    // 4. 显示启动命令
    echo "4. 启动队列监听命令:\n";
    echo "   php think queue:listen --queue=orderCancel\n";
    echo "   或者运行: php start_queue.php\n\n";
    
    echo "=== 测试完成 ===\n";
    echo "✓ 队列配置正常\n";
    echo "✓ 任务推送成功\n";
    echo "请启动队列监听进程来处理任务\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "请检查以下配置:\n";
    echo "1. Redis服务是否正常运行\n";
    echo "2. Redis连接配置是否正确\n";
    echo "3. think-queue扩展是否正确安装\n";
}
