<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接WebSocket测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            margin-left: 10px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #4CAF50;
            color: white;
        }
        .status.disconnected {
            background-color: #f44336;
            color: white;
        }
        .status.connecting {
            background-color: #FFC107;
            color: black;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.disconnect {
            background-color: #f44336;
        }
        button.disconnect:hover {
            background-color: #d32f2f;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #0066cc;
        }
        .log-entry.success {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .log-entry.sent {
            color: #2196F3;
        }
        .log-entry.received {
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>直接WebSocket测试 <span id="status" class="status disconnected">未连接</span></h1>
        
        <div class="form-group">
            <label for="url">WebSocket URL</label>
            <input type="text" id="url" value="wss://kefu.huohanghang.cn" placeholder="wss://example.com">
        </div>
        
        <div class="form-group">
            <label for="admin_id">管理员ID</label>
            <input type="text" id="admin_id" value="1" placeholder="管理员ID">
        </div>
        
        <div class="form-group">
            <label for="nickname">昵称</label>
            <input type="text" id="nickname" value="管理员" placeholder="昵称">
        </div>
        
        <div class="form-group">
            <label for="token">Token</label>
            <input type="text" id="token" value="admin_token" placeholder="Token">
        </div>
        
        <div class="form-group">
            <button id="connect">连接</button>
            <button id="disconnect" class="disconnect" disabled>断开连接</button>
            <button id="ping" disabled>发送Ping</button>
            <button id="clear-log">清空日志</button>
        </div>
        
        <div class="form-group">
            <label for="message">通知内容</label>
            <textarea id="message" placeholder="通知内容">这是一条测试通知，请查收！</textarea>
        </div>
        
        <div class="form-group">
            <button id="send-admin" disabled>发送管理员通知</button>
            <button id="send-system" disabled>发送系统通知</button>
            <button id="send-personal" disabled>发送个人通知</button>
        </div>
        
        <div class="log-container">
            <h3>操作日志</h3>
            <div id="log"></div>
        </div>
    </div>
    
    <script>
        let socket = null;
        
        // DOM元素
        const connectBtn = document.getElementById('connect');
        const disconnectBtn = document.getElementById('disconnect');
        const pingBtn = document.getElementById('ping');
        const sendAdminBtn = document.getElementById('send-admin');
        const sendSystemBtn = document.getElementById('send-system');
        const sendPersonalBtn = document.getElementById('send-personal');
        const clearLogBtn = document.getElementById('clear-log');
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        
        // 添加日志函数
        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logEl.insertBefore(logEntry, logEl.firstChild);
        }
        
        // 连接WebSocket
        connectBtn.addEventListener('click', () => {
            if (socket) {
                addLog('已有连接，请先断开', 'error');
                return;
            }
            
            const url = document.getElementById('url').value;
            const adminId = document.getElementById('admin_id').value;
            const nickname = document.getElementById('nickname').value;
            const token = document.getElementById('token').value;
            
            if (!url) {
                addLog('请输入WebSocket URL', 'error');
                return;
            }
            
            if (!adminId) {
                addLog('请输入管理员ID', 'error');
                return;
            }
            
            // 构建URL
            let baseUrl = url;
            if (baseUrl.endsWith('/')) {
                baseUrl = baseUrl.slice(0, -1);
            }
            
            const fullUrl = `${baseUrl}?admin_id=${adminId}&nickname=${encodeURIComponent(nickname)}&token=${encodeURIComponent(token)}&type=admin&t=${Date.now()}`;
            
            addLog(`完整连接URL: ${fullUrl}`, 'info');
            statusEl.textContent = '连接中...';
            statusEl.className = 'status connecting';
            
            try {
                socket = new WebSocket(fullUrl);
                
                socket.onopen = (event) => {
                    addLog('连接已建立', 'success');
                    statusEl.textContent = '已连接';
                    statusEl.className = 'status connected';
                    
                    // 启用按钮
                    disconnectBtn.disabled = false;
                    pingBtn.disabled = false;
                    sendAdminBtn.disabled = false;
                    sendSystemBtn.disabled = false;
                    sendPersonalBtn.disabled = false;
                    connectBtn.disabled = true;
                };
                
                socket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`收到消息: ${JSON.stringify(data, null, 2)}`, 'received');
                    } catch (e) {
                        addLog(`收到消息(非JSON): ${event.data}`, 'received');
                    }
                };
                
                socket.onclose = (event) => {
                    addLog(`连接已关闭: 代码=${event.code}, 原因=${event.reason || 'N/A'}`, 'info');
                    statusEl.textContent = '未连接';
                    statusEl.className = 'status disconnected';
                    
                    // 禁用按钮
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    sendAdminBtn.disabled = true;
                    sendSystemBtn.disabled = true;
                    sendPersonalBtn.disabled = true;
                    connectBtn.disabled = false;
                    
                    socket = null;
                };
                
                socket.onerror = (error) => {
                    addLog(`连接错误: ${error.message || 'Unknown error'}`, 'error');
                };
            } catch (e) {
                addLog(`创建WebSocket对象失败: ${e.message}`, 'error');
                statusEl.textContent = '未连接';
                statusEl.className = 'status disconnected';
            }
        });
        
        // 断开连接
        disconnectBtn.addEventListener('click', () => {
            if (!socket) {
                addLog('没有活动的连接', 'error');
                return;
            }
            
            socket.close();
            addLog('正在关闭连接...', 'info');
        });
        
        // 发送Ping
        pingBtn.addEventListener('click', () => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接', 'error');
                return;
            }
            
            const pingData = {
                event: 'ping',
                data: {
                    timestamp: Date.now()
                }
            };
            
            socket.send(JSON.stringify(pingData));
            addLog(`发送Ping: ${JSON.stringify(pingData)}`, 'sent');
        });
        
        // 发送管理员通知
        sendAdminBtn.addEventListener('click', () => {
            sendNotification('admin_notification');
        });
        
        // 发送系统通知
        sendSystemBtn.addEventListener('click', () => {
            sendNotification('system');
        });
        
        // 发送个人通知
        sendPersonalBtn.addEventListener('click', () => {
            sendNotification('personal');
        });
        
        // 发送通知函数
        function sendNotification(type) {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接', 'error');
                return;
            }
            
            const message = document.getElementById('message').value;
            const adminId = document.getElementById('admin_id').value;
            
            if (!message) {
                addLog('请输入通知内容', 'error');
                return;
            }
            
            const data = {
                event: 'admin_notification',
                data: {
                    type: type,
                    title: '测试通知',
                    content: message,
                    admin_id: adminId,
                    timestamp: Date.now()
                }
            };
            
            socket.send(JSON.stringify(data));
            addLog(`发送${type}通知: ${JSON.stringify(data, null, 2)}`, 'sent');
        }
        
        // 清空日志
        clearLogBtn.addEventListener('click', () => {
            logEl.innerHTML = '';
        });
        
        // 页面加载完成
        window.addEventListener('load', () => {
            addLog('页面加载完成，可以开始测试WebSocket通知功能', 'info');
        });
    </script>
</body>
</html>
