# 商家等级体系API接口文档

## 1. 获取等级配置列表

**接口地址：** `GET /api/shop_level/levelConfigs`

**请求参数：** 无

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "id": 1,
            "level": 0,
            "name": "0元入驻",
            "price": 0.00,
            "price_text": "免费",
            "duration": 31536000,
            "features": {
                "goods_limit": 50,
                "basic_decoration": true,
                "basic_service": true
            },
            "limits": {
                "goods_count": 50,
                "decoration_templates": ["basic"],
                "marketing_tools": []
            },
            "description": "免费注册，享受基础功能",
            "sort": 1
        },
        {
            "id": 2,
            "level": 1,
            "name": "商家会员",
            "price": 1000.00,
            "price_text": "¥1000",
            "duration": 31536000,
            "features": {
                "goods_limit": 500,
                "advanced_decoration": true,
                "marketing_tools": true,
                "data_analysis": true
            },
            "limits": {
                "goods_count": 500,
                "decoration_templates": ["basic", "advanced"],
                "marketing_tools": ["coupon", "discount"]
            },
            "description": "付费会员，享受更多营销工具和数据分析",
            "sort": 2
        }
    ]
}
```

## 2. 选择等级并创建支付订单

**接口地址：** `POST /api/shop_level/selectLevel`

**请求参数：**
```json
{
    "target_level": 1  // 目标等级：0=0元入驻，1=商家会员，2=实力厂商
}
```

**返回示例：**
```json
{
    "code": 1,
    "msg": "订单创建成功",
    "data": {
        "target_level": 1,
        "need_payment": true,
        "order_sn": "SM202412011200001",
        "amount": 1000.00,
        "level_name": "商家会员"
    }
}
```

## 3. 创建支付订单

**接口地址：** `POST /api/shop_level/createPayment`

**请求参数：**
```json
{
    "order_sn": "SM202412011200001",
    "pay_way": 1,  // 支付方式：1=微信支付，2=支付宝
    "from": 2      // 客户端类型：1=小程序，2=H5，3=iOS，4=Android
}
```

**返回示例：**
```json
{
    "code": 1,
    "msg": "支付创建成功",
    "data": {
        "pay_way": 1,
        "config": {
            "appId": "wx1234567890",
            "timeStamp": "1638360000",
            "nonceStr": "abc123",
            "package": "prepay_id=wx123456789",
            "signType": "RSA",
            "paySign": "signature"
        }
    }
}
```

## 4. 获取支付状态

**接口地址：** `GET /api/shop_level/getPaymentStatus`

**请求参数：**
- `order_sn`: 订单号

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "order_sn": "SM202412011200001",
        "amount": 1000.00,
        "target_level": 1,
        "level_name": "商家会员",
        "status": 1,
        "status_text": "已支付",
        "created_at": "2024-12-01 12:00:00"
    }
}
```

## 5. 商家申请入驻（修改版）

**接口地址：** `POST /api/shop/apply`

**请求参数：**
```json
{
    "target_level": 1,           // 目标等级
    "cid": 1,                   // 主营类目ID
    "name": "测试商家",          // 商家名称
    "nickname": "张三",          // 联系人
    "mobile": "***********",     // 手机号
    "account": "testshop",       // 商家账号
    "password": "123456",        // 密码
    "license": ["file1.jpg", "file2.jpg"]  // 资质文件
}
```

**返回示例：**
```json
{
    "code": 1,
    "msg": "申请提交成功",
    "data": {
        "apply_id": 123,
        "audit_status": 1,
        "audit_status_text": "待审核"
    }
}
```

## 6. 商家等级升级

**接口地址：** `POST /api/shop_level/upgrade`

**请求参数：**
```json
{
    "target_level": 2  // 目标等级
}
```

**返回示例：**
```json
{
    "code": 1,
    "msg": "升级订单创建成功",
    "data": {
        "upgrade_id": 456,
        "order_sn": "*****************",
        "amount": 2000.00,
        "target_level": 2,
        "target_level_name": "实力厂商"
    }
}
```

## 7. 获取可升级等级列表

**接口地址：** `GET /api/shop_level/getUpgradableLevels`

**请求参数：** 无（从session获取商家信息）

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "id": 3,
            "level": 2,
            "name": "实力厂商",
            "price": 3000.00,
            "upgrade_price": 2000.00,
            "description": "最高等级，包含验厂认证和VIP服务",
            "features": {
                "goods_unlimited": true,
                "factory_certification": true,
                "vip_service": true
            }
        }
    ]
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 状态码说明

### 申请状态（audit_status）
- 1: 待审核
- 2: 审核通过
- 3: 审核拒绝

### 支付状态（status）
- 0: 待支付
- 1: 已支付

### 商家等级（level）
- 0: 0元入驻
- 1: 商家会员
- 2: 实力厂商

### 升级状态（upgrade status）
- 0: 待支付
- 1: 已支付
- 2: 已生效

## 权限说明

### 0元入驻权限
- 商品发布：50个
- 店铺装修：基础模板
- 营销工具：无
- 数据分析：基础统计

### 商家会员权限
- 商品发布：500个
- 店铺装修：基础+高级模板
- 营销工具：优惠券、满减
- 数据分析：详细报表

### 实力厂商权限
- 商品发布：无限制
- 店铺装修：全部模板
- 营销工具：全部工具
- 数据分析：高级分析
- 特殊标识：验厂认证
- 客服支持：VIP通道
