{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "商家保证金退款接口文档", "description": "基于 app/shopapi/controller/Index.php 自动生成，适用于Apifox导入。", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "商家保证金管理", "id": 55359880, "auth": {}, "securityScheme": {}, "parentId": 46973025, "serverId": "", "description": "商家保证金退款相关接口", "items": [{"name": "获取商家保证金退款警告信息", "api": {"id": "288483560", "method": "get", "path": "shopapi/index/getShopDepositRefundNotice", "description": "获取商家保证金退款的警告信息，包括警告图片和公示期说明", "tags": ["商家保证金", "退款管理"], "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "获取成功"}, "data": {"type": "object", "properties": {"notice": {"type": "string", "description": "退款警告信息HTML内容，包含警告图片", "example": "<div><img src=\"/uploads/shop_deposit_refund_warning.png\" /></div>"}, "publicity_period_days": {"type": "integer", "description": "退款公示期天数", "example": 7}}, "required": ["notice", "publicity_period_days"]}}, "required": ["code", "msg", "data"]}}, {"code": 500, "name": "服务器错误", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 0}, "msg": {"type": "string", "description": "错误信息", "example": "获取失败：系统错误"}, "data": {"type": "object", "example": {}}}}}]}}, {"name": "检查商家保证金退款资格", "api": {"id": "288483561", "method": "get", "path": "shopapi/index/checkShopDepositRefundCondition", "description": "检查商家是否满足保证金退款条件，包括订单状态、商品状态、维权期等多项检查", "tags": ["商家保证金", "退款管理", "资格检查"], "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "show": {"type": "integer", "description": "是否显示弹窗：0不显示，1显示", "example": 0}, "msg": {"type": "string", "description": "提示信息", "example": "商家保证金退款条件检查结果"}, "data": {"type": "object", "properties": {"can_deactivate": {"type": "boolean", "description": "是否可以申请退款", "example": false}, "has_deposit": {"type": "object", "properties": {"status": {"type": "boolean", "description": "保证金状态检查结果", "example": true}, "message": {"type": "string", "description": "检查结果说明", "example": "保证金状态有效，当前余额：5000.00元"}}}, "ongoing_orders": {"type": "object", "properties": {"status": {"type": "boolean", "description": "进行中订单检查结果", "example": false}, "message": {"type": "string", "description": "检查结果说明", "example": "您有 3 个订单未完成，请先处理完成"}}}, "active_goods": {"type": "object", "properties": {"status": {"type": "boolean", "description": "上架商品检查结果", "example": false}, "message": {"type": "string", "description": "检查结果说明", "example": "您有 15 个商品仍在上架状态，请先下架所有商品"}}}, "rights_protection": {"type": "object", "properties": {"status": {"type": "boolean", "description": "维权期检查结果", "example": false}, "message": {"type": "string", "description": "检查结果说明", "example": "您有 2 个订单仍在维权期内（确认收货后15天），请等待维权期结束"}}}, "unshipped_orders": {"type": "object", "properties": {"status": {"type": "boolean", "description": "未发货订单检查结果", "example": true}, "message": {"type": "string", "description": "检查结果说明", "example": "所有订单已发货并收货"}}}, "pending_aftersales": {"type": "object", "properties": {"status": {"type": "boolean", "description": "待处理售后检查结果", "example": true}, "message": {"type": "string", "description": "检查结果说明", "example": "所有售后已处理完成"}}}}}}}}]}}, {"name": "确认商家保证金退款申请", "api": {"id": "288483562", "method": "post", "path": "shopapi/index/confirmShopDepositRefund", "description": "在通过所有检查后，提交商家保证金退款申请", "tags": ["商家保证金", "退款管理", "申请提交"], "parameters": {"body": {"type": "json", "jsonSchema": {"type": "object", "properties": {"reason": {"type": "string", "description": "退款原因（可选）", "example": "商家主动申请退款"}}}}}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "show": {"type": "integer", "description": "是否显示弹窗：0不显示，1显示", "example": 0}, "msg": {"type": "string", "description": "提示信息", "example": "退款申请提交成功，请等待平台审核"}, "data": {"type": "object", "properties": {"can_deactivate": {"type": "boolean", "description": "是否可以申请退款", "example": true}, "refund_amount": {"type": "string", "description": "退款金额", "example": "5000.00"}}}}}}, {"code": 400, "name": "失败", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 0}, "show": {"type": "integer", "description": "是否显示弹窗：0不显示，1显示", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "申请失败：您有 3 个订单未完成，请先处理完成"}, "data": {"type": "object", "properties": {"can_deactivate": {"type": "boolean", "description": "是否可以申请退款", "example": false}}}}}}]}}]}], "commonParameters": {"parameters": {"header": [{"name": "token", "defaultEnable": true, "type": "string", "id": "ShopTokenHeader", "description": "商家登录令牌", "required": true}, {"name": "Content-Type", "defaultEnable": true, "type": "string", "id": "ContentTypeHeader", "description": "内容类型", "required": false, "example": "application/json"}]}}, "projectSetting": {"id": "5618395", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务", "url": "https://www.huohanghang.cn"}]}}