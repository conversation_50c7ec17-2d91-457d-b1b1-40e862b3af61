<?php

namespace app\admin\controller\shop;

use app\common\basics\AdminBase;
use app\common\model\shop\ShopAuth;
use app\common\server\JsonServer;
use think\facade\Db;

/**
 * 商家权限商家类型管理
 */
class AuthMerchantType extends AdminBase
{
    /**
     * 权限商家类型设置页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $page = $get['page'] ?? 1;
            $limit = $get['limit'] ?? 15;
            
            // 查询权限列表
            $count = ShopAuth::where('del', 0)->where('type', 1)->count();
            $lists = ShopAuth::where('del', 0)
                ->where('type', 1)
                ->order('sort DESC, id ASC')
                ->page($page, $limit)
                ->select()
                ->toArray();
            
            // 处理数据
            $tierNames = [
                '0' => '0元入驻',
                '1' => '商家会员', 
                '2' => '实力厂商'
            ];
            
            foreach ($lists as &$item) {
                // 解析允许的商家类型
                $allowed_types = explode(',', $item['merchant_types'] ?? '0,1,2');
                $type_names = [];
                foreach ($allowed_types as $type) {
                    if (isset($tierNames[$type])) {
                        $type_names[] = $tierNames[$type];
                    }
                }
                $item['merchant_type_names'] = implode(', ', $type_names);
                $item['merchant_types_array'] = $allowed_types;
                
                // 设置状态样式
                if (count($allowed_types) == 3) {
                    $item['type_class'] = 'layui-bg-blue';
                    $item['type_text'] = '全部商家';
                } elseif (in_array('2', $allowed_types)) {
                    $item['type_class'] = 'layui-bg-orange';
                    $item['type_text'] = '付费商家';
                } elseif (in_array('1', $allowed_types)) {
                    $item['type_class'] = 'layui-bg-green';
                    $item['type_text'] = '会员以上';
                } else {
                    $item['type_class'] = 'layui-bg-gray';
                    $item['type_text'] = '仅0元入驻';
                }
            }
            
            return json(['code' => 0, 'count' => $count, 'data' => $lists]);
        }
        
        return view();
    }
    
    /**
     * 批量设置商家类型权限
     */
    public function batchSet()
    {
        $post = $this->request->post();
        $auth_ids = $post['auth_ids'] ?? [];
        $merchant_types = $post['merchant_types'] ?? [];
        
        if (empty($auth_ids)) {
            return json(['code' => 1, 'msg' => '请选择要设置的权限']);
        }
        
        if (empty($merchant_types)) {
            return json(['code' => 1, 'msg' => '请选择商家类型']);
        }
        
        // 验证商家类型
        $valid_types = ['0', '1', '2'];
        foreach ($merchant_types as $type) {
            if (!in_array($type, $valid_types)) {
                return json(['code' => 1, 'msg' => '商家类型参数错误']);
            }
        }
        
        try {
            Db::startTrans();
            
            $merchant_types_str = implode(',', $merchant_types);
            $success_count = 0;
            
            foreach ($auth_ids as $auth_id) {
                // 检查权限是否存在
                $auth = ShopAuth::where('id', $auth_id)->where('del', 0)->find();
                if (!$auth) {
                    continue;
                }
                
                // 更新商家类型权限
                ShopAuth::where('id', $auth_id)->update(['merchant_types' => $merchant_types_str]);
                $success_count++;
            }
            
            Db::commit();
            
            return json(['code' => 0, 'msg' => "成功设置 {$success_count} 个权限的商家类型"]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '设置失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 单个权限设置
     */
    public function setSingle()
    {
        $post = $this->request->post();
        $auth_id = $post['auth_id'] ?? 0;
        $merchant_types = $post['merchant_types'] ?? [];
        
        if (empty($auth_id)) {
            return json(['code' => 1, 'msg' => '权限ID不能为空']);
        }
        
        if (empty($merchant_types)) {
            return json(['code' => 1, 'msg' => '请选择商家类型']);
        }
        
        // 验证商家类型
        $valid_types = ['0', '1', '2'];
        foreach ($merchant_types as $type) {
            if (!in_array($type, $valid_types)) {
                return json(['code' => 1, 'msg' => '商家类型参数错误']);
            }
        }
        
        try {
            // 检查权限是否存在
            $auth = ShopAuth::where('id', $auth_id)->where('del', 0)->find();
            if (!$auth) {
                return json(['code' => 1, 'msg' => '权限不存在']);
            }
            
            $merchant_types_str = implode(',', $merchant_types);
            ShopAuth::where('id', $auth_id)->update(['merchant_types' => $merchant_types_str]);
            
            return json(['code' => 0, 'msg' => '设置成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '设置失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取权限统计
     */
    public function getStats()
    {
        $stats = [
            'total' => 0,
            'all_merchants' => 0,
            'paid_only' => 0,
            'member_plus' => 0,
            'free_only' => 0
        ];
        
        $auths = ShopAuth::where('del', 0)->where('type', 1)->column('merchant_types');
        $stats['total'] = count($auths);
        
        foreach ($auths as $types) {
            $allowed_types = explode(',', $types ?? '0,1,2');
            
            if (count($allowed_types) == 3) {
                $stats['all_merchants']++;
            } elseif (in_array('2', $allowed_types) && in_array('1', $allowed_types) && !in_array('0', $allowed_types)) {
                $stats['paid_only']++;
            } elseif (in_array('1', $allowed_types) && !in_array('0', $allowed_types)) {
                $stats['member_plus']++;
            } elseif (count($allowed_types) == 1 && in_array('0', $allowed_types)) {
                $stats['free_only']++;
            }
        }
        
        return json(['code' => 0, 'data' => $stats]);
    }
}
