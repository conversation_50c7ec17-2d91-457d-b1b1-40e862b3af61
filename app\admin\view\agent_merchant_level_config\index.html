<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-form-query">
                <form class="layui-form" id="query-form">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">等级名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="level_name" placeholder="请输入等级名称" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" type="button" id="query-btn">查询</button>
                        </div>
                    </div>
                </form>
            </div>

            <table id="lists" lay-filter="lists"></table>

            <script type="text/html" id="toolbar">
                <a class="layui-btn layui-btn-sm" lay-event="add">添加规则</a>
            </script>

            <script type="text/html" id="bar">
                <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'pear'], function () {
        let table = layui.table;
        let form = layui.form;
        let pear = layui.pear;
        let $ = layui.jquery;

        let tableIns = table.render({
            elem: '#lists',
            url: '{:__URL("lists")}',
            page: true,
            toolbar: '#toolbar',
            cols: [[
                {field: 'level_id', title: '等级ID', width: 100, sort: true},
                {field: 'level_name', title: '等级名称'},
                {field: 'purchaser_total_count', title: '分配总人数', width: 150, sort: true},
                {field: 'purchaser_composition', title: '采购人员构成(JSON)', minWidth: 200},
                {field: 'daily_message_count', title: '每日群发消息数', width: 180, sort: true},
                {field: 'create_time', title: '创建时间', width: 180, templet: "<div>{{layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm:ss')}}</div>"},
                {field: 'update_time', title: '更新时间', width: 180, templet: "<div>{{d.update_time ? layui.util.toDateString(d.update_time * 1000, 'yyyy-MM-dd HH:mm:ss') : ''}}</div>"},
                {fixed: 'right', title: '操作', toolbar: '#bar', width: 150}
            ]]
        });

        // 工具栏事件
        table.on('toolbar(lists)', function (obj) {
            if (obj.event === 'add') {
                pear.open({
                    title: '添加规则',
                    url: '{:__URL("add")}',
                    width: 700,
                    height: 600,
                    end: function(){
                        tableIns.reload();
                    }
                });
            }
        });

        // 操作栏事件
        table.on('tool(lists)', function (obj) {
            let data = obj.data;
            if (obj.event === 'edit') {
                pear.open({
                    title: '编辑规则',
                    url: '{:__URL("edit", ["id" => "__id__"])}'.replace('__id__', data.id),
                    width: 700,
                    height: 600,
                    end: function(){
                        tableIns.reload();
                    }
                });
            } else if (obj.event === 'del') {
                layer.confirm('确定要删除这条规则吗？', function (index) {
                    $.post('{:__URL("del")}', {id: data.id}, function (res) {
                        layer.close(index);
                        if (res.code === 1) {
                            layer.msg(res.msg, {icon: 1});
                            tableIns.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                });
            }
        });

        // 查询按钮
        $('#query-btn').click(function () {
            tableIns.reload({
                where: $('#query-form').serializeJson(),
                page: {curr: 1}
            });
        });
    });
</script>
