<?php

namespace app\common\logic;

use think\facade\Db;
use think\facade\Log;

/**
 * 商家采购人员分配逻辑
 */
class PurchaserAssignmentLogic
{
    /**
     * @notes 商家升级/购买成功后触发分配
     * @param int $merchantUserId 商家用户ID
     * @param int $levelId 商家等级ID (对应 agent_order.order_type)
     * @return bool
     */
    public static function assignOnUpgrade(int $merchantUserId, int $levelId): bool
    {
        try {
            $currentYear = date('Y');

            // 1. 检查当年是否已经分配过
            $existingAssignment = Db::name('merchant_purchaser_relation')
                ->where('merchant_user_id', $merchantUserId)
                ->where('assign_year', $currentYear)
                ->find();

            if ($existingAssignment) {
                Log::info("商家 {$merchantUserId} 在 {$currentYear} 年已分配过采购人员，跳过此次分配。");
                return true;
            }

            // 2. 获取该等级的分配规则
            $config = Db::name('merchant_level_config')->where('level_id', $levelId)->find();
            if (!$config || $config['purchaser_total_count'] <= 0) {
                Log::info("等级 {$levelId} 未配置采购人员分配规则或分配数量为0，跳过分配。");
                return true;
            }

            $totalCount = $config['purchaser_total_count'];
            $composition = json_decode($config['purchaser_composition'], true);

            $allocatedPurchaserIds = [];
            $excludedUserIds = [$merchantUserId]; // 排除商家自己

            // 3. 按比例分配采购人员
            if (!empty($composition)) {
                foreach ($composition as $level => $percentage) {
                    $levelNumber = (int)filter_var($level, FILTER_SANITIZE_NUMBER_INT);
                    $neededCount = floor($totalCount * ($percentage / 100));
                    if ($neededCount <= 0) continue;

                    // 假设 user 表有 activity_level 和 is_purchaser 字段
                    $purchasers = Db::name('user')
                        ->where('is_purchaser', 1)
                        ->where('activity_level', $levelNumber)
                        ->whereNotIn('id', $excludedUserIds)
                        ->limit($neededCount)
                        ->column('id');
                    
                    $allocatedPurchaserIds = array_merge($allocatedPurchaserIds, $purchasers);
                    $excludedUserIds = array_merge($excludedUserIds, $purchasers);
                }
            }

            // 4. 如果分配后数量不足,用普通用户补充
            $remainingCount = $totalCount - count($allocatedPurchaserIds);
            if ($remainingCount > 0) {
                 // 优先用其他等级的采购员补充
                $supplementPurchasers = Db::name('user')
                    ->where('is_purchaser', 1)
                    ->whereNotIn('id', $excludedUserIds)
                    ->limit($remainingCount)
                    ->column('id');
                
                $allocatedPurchaserIds = array_merge($allocatedPurchaserIds, $supplementPurchasers);
                $excludedUserIds = array_merge($excludedUserIds, $supplementPurchasers);
                $remainingCount = $totalCount - count($allocatedPurchaserIds);

                // 还不够，用普通用户补充
                if($remainingCount > 0){
                    $supplementUsers = Db::name('user')
                        ->where('is_purchaser', 0) // 普通用户
                        ->whereNotIn('id', $excludedUserIds)
                        ->limit($remainingCount)
                        ->column('id');
                    $allocatedPurchaserIds = array_merge($allocatedPurchaserIds, $supplementUsers);
                }
            }

            // 5. 写入分配记录
            if (!empty($allocatedPurchaserIds)) {
                $relationData = [];
                $assignTime = time();
                foreach ($allocatedPurchaserIds as $purchaserId) {
                    $relationData[] = [
                        'merchant_user_id' => $merchantUserId,
                        'purchaser_user_id' => $purchaserId,
                        'assign_year' => $currentYear,
                        'create_time' => $assignTime
                    ];
                }
                Db::name('merchant_purchaser_relation')->insertAll($relationData);
                Log::info("成功为商家 {$merchantUserId} 分配了 " . count($relationData) . " 名采购人员。");
            }

            return true;

        } catch (\Exception $e) {
            Log::error('采购人员分配失败: ' . $e->getMessage());
            // 此处不向上抛出异常, 以免影响主支付流程
            return false;
        }
    }
}
