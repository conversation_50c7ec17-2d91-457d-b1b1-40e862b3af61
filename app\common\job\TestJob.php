<?php

namespace app\common\job;

use think\queue\Job;
use think\facade\Log;

/**
 * 测试队列任务
 * Class TestJob
 * @package app\common\job
 */
class TestJob
{
    /**
     * 执行测试任务
     * @param Job $job 队列任务对象
     * @param array $data 任务数据
     */
    public function fire(Job $job, $data)
    {
        Log::info('TestJob 开始执行', [
            'job_id' => $job->getJobId(),
            'attempts' => $job->attempts(),
            'data' => $data,
            'time' => date('Y-m-d H:i:s')
        ]);
        
        try {
            // 模拟一些处理时间
            sleep(2);
            
            Log::info('TestJob 执行成功', [
                'data' => $data,
                'time' => date('Y-m-d H:i:s')
            ]);
            
            // 删除任务
            $job->delete();
            
        } catch (\Exception $e) {
            Log::error('TestJob 执行失败: ' . $e->getMessage(), [
                'data' => $data,
                'attempts' => $job->attempts()
            ]);
            
            // 检查重试次数
            if ($job->attempts() >= 3) {
                Log::error("TestJob 达到最大重试次数");
                $job->delete();
            } else {
                $job->release(10); // 延迟10秒后重试
            }
        }
    }

    /**
     * 任务失败处理
     * @param array $data 任务数据
     */
    public function failed($data)
    {
        Log::error('TestJob 最终执行失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
    }
}
