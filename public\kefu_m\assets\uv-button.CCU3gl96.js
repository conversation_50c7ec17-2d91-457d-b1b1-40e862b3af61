var e,t,o,i;import{K as a,L as r,M as s,N as n,o as l,f as d,w as u,C as p,D as c,j as h,F as m,k as g,i as y,h as f,t as S,q as v,O as b,P as x,e as C,g as z,A as w,x as _}from"./index-KqVIYTFB.js";import{_ as T,r as $,d as k}from"./uni-app.es.elp5fm4t.js";const N=T({name:"uv-loading-icon",mixins:[a,r,{props:{show:{type:Boolean,default:!0},color:{type:String,default:"#909193"},textColor:{type:String,default:"#909193"},vertical:{type:Boolean,default:!1},mode:{type:String,default:"spinner"},size:{type:[String,Number],default:24},textSize:{type:[String,Number],default:15},textStyle:{type:Object,default:()=>({})},text:{type:[String,Number],default:""},timingFunction:{type:String,default:"linear"},duration:{type:[String,Number],default:1200},inactiveColor:{type:String,default:""},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.loadingIcon}}],data:()=>({array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}),computed:{otherBorderColor(){const e=s(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show(e){}},mounted(){this.init()},methods:{init(){setTimeout((()=>{}),20)},addEventListenerToWebview(){const e=n(),t=e[e.length-1].$getAppWebview();t.addEventListener("hide",(()=>{this.webviewHide=!0})),t.addEventListener("show",(()=>{this.webviewHide=!1}))}}},[["render",function(e,t,o,i,a,r){const s=v,n=b;return e.show?(l(),d(s,{key:0,class:p(["uv-loading-icon",[e.vertical&&"uv-loading-icon--vertical"]]),style:c([e.$uv.addStyle(e.customStyle)])},{default:u((()=>[a.webviewHide?y("",!0):(l(),d(s,{key:0,class:p(["uv-loading-icon__spinner",[`uv-loading-icon__spinner--${e.mode}`]]),ref:"ani",style:c({color:e.color,width:e.$uv.addUnit(e.size),height:e.$uv.addUnit(e.size),borderTopColor:e.color,borderBottomColor:r.otherBorderColor,borderLeftColor:r.otherBorderColor,borderRightColor:r.otherBorderColor,"animation-duration":`${e.duration}ms`,"animation-timing-function":"semicircle"===e.mode||"circle"===e.mode?e.timingFunction:""})},{default:u((()=>["spinner"===e.mode?(l(!0),h(m,{key:0},g(a.array12,((e,t)=>(l(),d(s,{key:t,class:"uv-loading-icon__dot"})))),128)):y("",!0)])),_:1},8,["class","style"])),e.text?(l(),d(n,{key:1,class:"uv-loading-icon__text",style:c([{fontSize:e.$uv.addUnit(e.textSize),color:e.textColor},e.$uv.addStyle(e.textStyle)])},{default:u((()=>[f(S(e.text),1)])),_:1},8,["style"])):y("",!0)])),_:1},8,["style","class"])):y("",!0)}],["__scopeId","data-v-d9c6b36a"]]);const B=T({name:"uv-button",mixins:[a,r,{props:{hairline:{type:Boolean,default:!0},type:{type:String,default:"info"},size:{type:String,default:"normal"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},loadingText:{type:[String,Number],default:""},loadingMode:{type:String,default:"spinner"},loadingSize:{type:[String,Number],default:14},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!0},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!0},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:0},hoverStartTime:{type:[String,Number],default:0},hoverStayTime:{type:[String,Number],default:200},text:{type:[String,Number],default:""},icon:{type:String,default:""},iconSize:{type:[String,Number],default:""},iconColor:{type:String,default:"#000000"},color:{type:String,default:""},customTextStyle:{type:[Object,String],default:""},...null==(i=null==(o=uni.$uv)?void 0:o.props)?void 0:i.button}}],emits:["click"],data:()=>({}),computed:{bemClass(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor(){return this.plain?this.color?this.color:"#3c9cff":"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor(){let e={};return this.color&&(e.color=this.plain?this.color:"white",this.plain||(e["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(e.borderTopWidth=0,e.borderRightWidth=0,e.borderBottomWidth=0,e.borderLeftWidth=0,this.plain||(e.backgroundImage=this.color)):(e.borderColor=this.color,e.borderWidth="1px",e.borderStyle="solid")),e},nvueTextStyle(){let e={};return"info"===this.type&&(e.color="#323233"),this.color&&(e.color=this.plain?this.color:"white"),e.fontSize=this.textSize+"px",e},textSize(){let e=14,{size:t}=this;return"large"===t&&(e=16),"normal"===t&&(e=14),"small"===t&&(e=12),"mini"===t&&(e=10),e},getIconSize(){const e=this.iconSize?this.iconSize:1.35*this.textSize;return this.$uv.addUnit(e)},btnWrapperStyle(){const e={},t=this.$uv.addStyle(this.customStyle);return t.width&&(e.width=t.width),e}},methods:{clickHandler(){this.disabled||this.loading||x((()=>{this.$emit("click")}),this.throttleTime)}}},[["render",function(e,t,o,i,a,r){const s=$(C("uv-loading-icon"),N),n=b,g=$(C("uv-icon"),k),x=_,T=v;return l(),d(T,{class:"uv-button-wrapper",style:c([r.btnWrapperStyle])},{default:u((()=>[z(x,{"hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.disabled||e.loading?"":"uv-button--active",class:p(["uv-button uv-reset-button",r.bemClass]),style:c([r.baseColor,e.$uv.addStyle(e.customStyle)]),onClick:r.clickHandler},{default:u((()=>[e.loading?(l(),h(m,{key:0},[z(s,{mode:e.loadingMode,size:1.15*e.loadingSize,color:r.loadingColor},null,8,["mode","size","color"]),z(n,{class:"uv-button__loading-text",style:c([{fontSize:r.textSize+"px"},e.$uv.addStyle(e.customTextStyle)])},{default:u((()=>[f(S(e.loadingText||e.text),1)])),_:1},8,["style"])],64)):(l(),h(m,{key:1},[e.icon?(l(),d(g,{key:0,name:e.icon,color:r.iconColorCom,size:r.getIconSize,customStyle:{marginRight:"2px"}},null,8,["name","color","size"])):y("",!0),w(e.$slots,"default",{},(()=>[z(n,{class:"uv-button__text",style:c([{fontSize:r.textSize+"px"},e.$uv.addStyle(e.customTextStyle)])},{default:u((()=>[f(S(e.text),1)])),_:1},8,["style"])]),!0),w(e.$slots,"suffix",{},void 0,!0)],64))])),_:3},8,["hover-start-time","hover-stay-time","form-type","open-type","app-parameter","hover-stop-propagation","send-message-title","send-message-path","lang","data-name","session-from","send-message-img","show-message-card","hover-class","style","onClick","class"])])),_:3},8,["style"])}],["__scopeId","data-v-74b376fb"]]);export{B as _};
