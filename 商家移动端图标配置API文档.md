# 商家移动端功能图标配置API文档

## 接口概述

本接口用于向商家移动端前端返回配置的功能图标列表，支持基于角色权限的动态过滤。

## 接口信息

- **接口地址**: `/shopapi/index/getIconConfig`
- **请求方式**: `GET`
- **是否需要登录**: 否（支持未登录访问，但返回内容会有差异）

## 请求参数

无需传递参数

## 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | string | 否 | 商家登录token，如果提供则返回基于权限过滤的图标列表 |

## 响应格式

### 成功响应

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "shop_id": 0,
                "icon_url": "http://domain.com/static/images/icons/shop_settings.png",
                "icon_name": "商家设置",
                "icon_path": "/pages/shop/settings",
                "auth_id": 1,
                "sort_order": 10,
                "status": 1
            },
            {
                "id": 2,
                "shop_id": 0,
                "icon_url": "http://domain.com/static/images/icons/goods_manage.png",
                "icon_name": "商品技术",
                "icon_path": "/pages/goods/manage",
                "auth_id": 5,
                "sort_order": 20,
                "status": 1
            }
        ],
        "total": 8,
        "is_login": true
    }
}
```

### 失败响应

```json
{
    "code": 0,
    "msg": "获取失败：错误信息",
    "data": []
}
```

## 响应字段说明

### data对象

| 字段名 | 类型 | 说明 |
|--------|------|------|
| list | array | 图标配置列表 |
| total | int | 图标总数 |
| is_login | bool | 是否已登录 |

### list数组中的图标对象

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 图标配置ID |
| shop_id | int | 商家ID（0表示全局配置） |
| icon_url | string | 图标完整URL地址 |
| icon_name | string | 图标显示名称 |
| icon_path | string | 点击图标后的跳转路径 |
| auth_id | int | 关联的权限ID |
| sort_order | int | 排序值（越小越靠前） |
| status | int | 状态（1-启用，0-禁用） |

## 权限控制逻辑

### 未登录用户
- 只返回全局配置（shop_id=0）的图标
- 只返回顶级菜单权限对应的图标
- 适用于展示基础功能入口

### 已登录用户
- 返回全局配置和当前商家特定配置的图标
- 基于用户角色权限进行过滤
- 超级管理员（root=1）可以看到所有图标
- 普通管理员只能看到其角色权限范围内的图标

## 使用示例

### JavaScript调用示例

```javascript
// 未登录状态调用
fetch('/shopapi/index/getIconConfig')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            console.log('图标列表:', data.data.list);
            // 渲染图标到页面
            renderIcons(data.data.list);
        }
    });

// 已登录状态调用
fetch('/shopapi/index/getIconConfig', {
    headers: {
        'token': 'your_login_token_here'
    }
})
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            console.log('个性化图标列表:', data.data.list);
            console.log('登录状态:', data.data.is_login);
        }
    });
```

### 小程序调用示例

```javascript
// 小程序中调用
wx.request({
    url: 'https://your-domain.com/shopapi/index/getIconConfig',
    method: 'GET',
    header: {
        'token': wx.getStorageSync('token') || ''
    },
    success: function(res) {
        if (res.data.code === 1) {
            const iconList = res.data.data.list;
            // 更新页面数据
            this.setData({
                iconList: iconList,
                isLogin: res.data.data.is_login
            });
        }
    }
});
```

## 数据库表结构

### ls_shop_icon_config 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| shop_id | int(11) | 商家ID（0为全局配置） |
| icon_url | varchar(255) | 图标URL |
| icon_name | varchar(50) | 图标名称 |
| icon_path | varchar(255) | 跳转路径 |
| auth_id | int(11) | 关联权限ID |
| sort_order | int(11) | 排序字段 |
| status | tinyint(1) | 状态（1-启用，0-禁用） |
| created_at | int(10) | 创建时间 |
| updated_at | int(10) | 更新时间 |

## 管理后台

系统提供了完整的管理后台界面，商家可以通过以下路径访问：

- **访问路径**: `/shop/icon_config/index`
- **权限要求**: 需要"功能图标配置"权限
- **功能包括**:
  - 查看图标配置列表
  - 添加新的图标配置
  - 编辑现有图标配置
  - 删除图标配置
  - 启用/禁用图标

## 注意事项

1. 图标URL会自动补全为完整的域名地址
2. 返回的图标列表已按sort_order字段排序
3. 只返回状态为启用（status=1）的图标
4. 权限验证基于ls_dev_shop_auth、ls_shop_role等表
5. 建议前端对图标URL进行缓存以提升性能
6. 图标路径支持相对路径和绝对路径格式
