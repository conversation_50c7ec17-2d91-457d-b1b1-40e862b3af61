<?php

namespace app\common\validate;

use app\common\basics\Validate;

/**
 * 商家等级验证器
 */
class ShopLevelValidate extends Validate
{
    protected $rule = [
        'target_level' => 'require|integer|between:0,2',
        'order_sn' => 'require|alphaNum|length:20,32',
        'pay_way' => 'integer|in:1,2,3',
        'from' => 'integer|in:1,2,3,4',
    ];

    protected $message = [
        'target_level.require' => '请选择目标等级',
        'target_level.integer' => '等级参数格式错误',
        'target_level.between' => '等级参数超出范围',
        'order_sn.require' => '订单号不能为空',
        'order_sn.alphaNum' => '订单号格式错误',
        'order_sn.length' => '订单号长度错误',
        'pay_way.integer' => '支付方式参数格式错误',
        'pay_way.in' => '不支持的支付方式',
        'from.integer' => '客户端类型参数格式错误',
        'from.in' => '不支持的客户端类型',
    ];

    protected $scene = [
        'select' => ['target_level'],
        'payment' => ['order_sn', 'pay_way', 'from'],
        'upgrade' => ['target_level'],
    ];
}
