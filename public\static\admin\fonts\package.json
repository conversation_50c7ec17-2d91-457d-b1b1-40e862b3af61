{"name": "kefu", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"core-js": "^3.6.5", "element-ui": "^2.3.6", "nprogress": "^0.2.0", "vue": "^2.6.11", "vue-router": "^3.2.0", "overlayscrollbars": "^1.13.1", "overlayscrollbars-vue": "^0.2.2", "vuex": "^3.4.0", "vuex-persist": "^3.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "axios": "^0.18.0", "file-loader": "^6.2.0", "node-sass": "^4.12.0", "sass-loader": "^8.0.2", "url-loader": "^4.1.1", "vue-cli-plugin-axios": "0.0.4", "vue-cli-plugin-element-ui": "~1.1.4", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}