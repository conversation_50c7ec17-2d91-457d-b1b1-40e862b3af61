<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket通知测试工具（兼容版）</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"], select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.disconnect {
            background-color: #f44336;
        }
        button.disconnect:hover {
            background-color: #d32f2f;
        }
        button.send {
            background-color: #2196F3;
        }
        button.send:hover {
            background-color: #0b7dda;
        }
        .log-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.sent {
            color: #2196F3;
        }
        .log-entry.received {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .log-entry.info {
            color: #9E9E9E;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            margin-left: 10px;
        }
        .status.connected {
            background-color: #4CAF50;
            color: white;
        }
        .status.disconnected {
            background-color: #f44336;
            color: white;
        }
        .status.connecting {
            background-color: #FFC107;
            color: black;
        }
        .row {
            display: flex;
            margin: 0 -10px;
        }
        .col {
            flex: 1;
            padding: 0 10px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket通知测试工具（兼容版） <span id="status" class="status disconnected">未连接</span></h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="websocket">WebSocket连接</div>
            <div class="tab" data-tab="notification">发送通知</div>
            <div class="tab" data-tab="api">API测试</div>
        </div>
        
        <div class="tab-content active" id="websocket">
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="url">WebSocket URL</label>
                        <input type="text" id="url" value="wss://kefu.huohanghang.cn" placeholder="wss://example.com">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="admin_id">管理员ID</label>
                        <input type="number" id="admin_id" value="1" placeholder="管理员ID">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="nickname">昵称</label>
                        <input type="text" id="nickname" value="管理员" placeholder="昵称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="token">Token</label>
                        <input type="text" id="token" value="admin_token" placeholder="Token">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button id="connect">连接</button>
                <button id="disconnect" class="disconnect" disabled>断开连接</button>
                <button id="ping" disabled>发送Ping</button>
                <button id="clear-log">清空日志</button>
            </div>
        </div>
        
        <div class="tab-content" id="notification">
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="event">事件名称</label>
                        <input type="text" id="event" value="admin_notification" placeholder="事件名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="message-type">消息类型</label>
                        <select id="message-type">
                            <option value="system">系统通知</option>
                            <option value="personal">个人通知</option>
                            <option value="admin_notification">默认通知</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="title">通知标题</label>
                        <input type="text" id="title" value="测试通知" placeholder="通知标题">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="icon">通知图标</label>
                        <select id="icon">
                            <option value="0">默认图标</option>
                            <option value="1">成功图标</option>
                            <option value="2">错误图标</option>
                            <option value="3">警告图标</option>
                            <option value="4">信息图标</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="content">通知内容</label>
                <textarea id="content" placeholder="通知内容">这是一条测试通知，请查收！</textarea>
            </div>

            <div class="form-group">
                <label for="url-link">跳转URL（可选）</label>
                <input type="text" id="url-link" placeholder="点击通知后跳转的URL">
            </div>

            <div class="form-group">
                <button id="send" class="send" disabled>发送通知</button>
                <button id="send-direct" class="send">直接显示通知（不需要连接）</button>
            </div>
        </div>
        
        <div class="tab-content" id="api">
            <div class="form-group">
                <label for="api-title">通知标题</label>
                <input type="text" id="api-title" value="API测试通知" placeholder="通知标题">
            </div>
            
            <div class="form-group">
                <label for="api-content">通知内容</label>
                <textarea id="api-content" placeholder="通知内容">这是一条通过API发送的测试通知，请查收！</textarea>
            </div>
            
            <div class="form-group">
                <label for="api-type">通知类型</label>
                <select id="api-type">
                    <option value="admin_notification">管理员通知</option>
                    <option value="system_notification">系统通知</option>
                    <option value="custom_notification">自定义通知</option>
                    <option value="error_notification">错误通知</option>
                </select>
            </div>
            
            <div class="form-group">
                <button id="send-api" class="send">通过API发送通知</button>
            </div>
        </div>

        <div class="log-container" id="log"></div>
    </div>

    <script>
        let socket = null;

        // DOM元素
        const connectBtn = document.getElementById('connect');
        const disconnectBtn = document.getElementById('disconnect');
        const pingBtn = document.getElementById('ping');
        const sendBtn = document.getElementById('send');
        const sendDirectBtn = document.getElementById('send-direct');
        const sendApiBtn = document.getElementById('send-api');
        const clearLogBtn = document.getElementById('clear-log');
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');

        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                this.classList.add('active');
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 连接WebSocket
        connectBtn.addEventListener('click', () => {
            if (socket) {
                addLog('已有连接，请先断开', 'error');
                return;
            }

            const url = document.getElementById('url').value;
            const adminId = document.getElementById('admin_id').value;
            const nickname = document.getElementById('nickname').value;
            const token = document.getElementById('token').value;

            if (!url) {
                addLog('请输入WebSocket URL', 'error');
                return;
            }

            if (!adminId) {
                addLog('请输入管理员ID', 'error');
                return;
            }

            // 构建URL
            let baseUrl = url;
            if (baseUrl.endsWith('/')) {
                baseUrl = baseUrl.slice(0, -1);
            }

            const fullUrl = `${baseUrl}?admin_id=${adminId}&nickname=${encodeURIComponent(nickname)}&token=${encodeURIComponent(token)}&type=admin&t=${Date.now()}`;

            addLog(`完整连接URL: ${fullUrl}`, 'info');
            statusEl.textContent = '连接中...';
            statusEl.className = 'status connecting';

            try {
                socket = new WebSocket(fullUrl);

                socket.onopen = (event) => {
                    addLog('连接已建立', 'info');
                    statusEl.textContent = '已连接';
                    statusEl.className = 'status connected';

                    // 启用按钮
                    disconnectBtn.disabled = false;
                    pingBtn.disabled = false;
                    sendBtn.disabled = false;
                    connectBtn.disabled = true;
                };

                socket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`收到消息: ${JSON.stringify(data, null, 2)}`, 'received');
                    } catch (e) {
                        addLog(`收到消息(非JSON): ${event.data}`, 'received');
                    }
                };

                socket.onclose = (event) => {
                    addLog(`连接已关闭: 代码=${event.code}, 原因=${event.reason || 'N/A'}`, 'info');
                    statusEl.textContent = '未连接';
                    statusEl.className = 'status disconnected';

                    // 禁用按钮
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    sendBtn.disabled = true;
                    connectBtn.disabled = false;

                    socket = null;
                };

                socket.onerror = (error) => {
                    addLog(`连接错误: ${error.message || 'Unknown error'}`, 'error');
                };
            } catch (e) {
                addLog(`创建WebSocket对象失败: ${e.message}`, 'error');
                statusEl.textContent = '未连接';
                statusEl.className = 'status disconnected';
            }
        });

        // 断开连接
        disconnectBtn.addEventListener('click', () => {
            if (!socket) {
                addLog('没有活动的连接', 'error');
                return;
            }

            socket.close();
            addLog('正在关闭连接...', 'info');
        });

        // 发送Ping
        pingBtn.addEventListener('click', () => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接', 'error');
                return;
            }

            const pingData = {
                event: 'ping',
                data: {
                    timestamp: Date.now()
                }
            };

            socket.send(JSON.stringify(pingData));
            addLog(`发送Ping: ${JSON.stringify(pingData)}`, 'sent');
        });

        // 发送通知
        sendBtn.addEventListener('click', () => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接', 'error');
                return;
            }

            const event = document.getElementById('event').value;
            const messageType = document.getElementById('message-type').value;
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const icon = document.getElementById('icon').value;
            const urlLink = document.getElementById('url-link').value;
            const adminId = document.getElementById('admin_id').value;

            if (!event) {
                addLog('请输入事件名称', 'error');
                return;
            }

            if (!content) {
                addLog('请输入通知内容', 'error');
                return;
            }

            const data = {
                event: event,
                data: {
                    type: messageType,
                    title: title,
                    content: content,
                    admin_id: adminId,
                    url: urlLink,
                    icon: parseInt(icon),
                    timestamp: Date.now()
                }
            };

            socket.send(JSON.stringify(data));
            addLog(`发送通知: ${JSON.stringify(data, null, 2)}`, 'sent');
        });

        // 直接显示通知（不需要连接）
        sendDirectBtn.addEventListener('click', () => {
            const messageType = document.getElementById('message-type').value;
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const icon = document.getElementById('icon').value;
            const urlLink = document.getElementById('url-link').value;

            if (!content) {
                addLog('请输入通知内容', 'error');
                return;
            }

            // 构建通知数据
            const notificationData = {
                type: messageType,
                title: title,
                content: content,
                url: urlLink,
                icon: parseInt(icon),
                timestamp: Date.now()
            };

            addLog(`直接显示通知: ${JSON.stringify(notificationData, null, 2)}`, 'info');

            // 使用parent.window访问父窗口的函数
            if (window.parent && window.parent.showNotification) {
                window.parent.showNotification(title, content, messageType, urlLink, parseInt(icon));
                addLog('通知已显示', 'info');
            } else {
                addLog('无法访问父窗口的showNotification函数', 'error');
                // 尝试使用alert显示
                alert(`通知: ${title}\n${content}`);
            }
        });

        // 通过API发送通知
        sendApiBtn.addEventListener('click', () => {
            const title = document.getElementById('api-title').value;
            const content = document.getElementById('api-content').value;
            const type = document.getElementById('api-type').value;

            if (!title || !content) {
                addLog('标题和内容不能为空', 'error');
                return;
            }

            addLog(`通过API发送通知: ${title}`, 'info');

            // 使用GET方法发送测试通知
            fetch(`/api/notification/testNotification?title=${encodeURIComponent(title)}&content=${encodeURIComponent(content)}&type=${encodeURIComponent(type)}`, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    addLog(`通知发送成功: ${data.msg || '成功'}`, 'sent');
                } else {
                    addLog(`通知发送失败: ${data.msg || '未知错误'}`, 'error');
                }
            })
            .catch(error => {
                addLog(`发送请求出错: ${error.message}`, 'error');
            });
        });

        // 清空日志
        clearLogBtn.addEventListener('click', () => {
            logEl.innerHTML = '';
        });

        // 添加日志
        function addLog(message, type) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;

            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            addLog('页面加载完成，可以开始测试通知功能', 'info');
        });
    </script>
</body>
</html>
