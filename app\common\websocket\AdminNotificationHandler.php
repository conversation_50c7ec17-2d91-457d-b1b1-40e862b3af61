<?php

namespace app\common\websocket;

use app\common\model\Admin;
use app\common\utils\Redis;
use Swoole\Server;
use Swoole\Websocket\Frame;
use think\App;
use think\Event;
use think\Request;
use think\swoole\Websocket;
use think\swoole\websocket\Room;
use think\facade\Db;
use think\facade\Log;

/**
 * 管理员通知WebSocket处理类
 * 专门用于处理管理员通知的WebSocket连接
 */
class AdminNotificationHandler extends Websocket
{
    protected $server;
    protected $room;
    protected $parser;
    protected $cache;
    protected $prefix;

    public function __construct(App $app, Server $server, Room $room, Event $event, Parser $parser, Redis $redis)
    {
        $this->server = $server;
        $this->room = $room;
        $this->parser = $parser;
        $this->cache = $redis;
        $this->prefix = config('default.websocket_prefix') . 'admin_';
        parent::__construct($app, $server, $room, $event);
    }

    /**
     * WebSocket连接建立时的处理函数
     * @param int $fd 连接标识符
     * @param Request $request 请求对象
     * @return bool|mixed|void
     */
    public function onOpen($fd, Request $request)
    {
        try {
            // 获取请求参数
            $admin_id = $request->get('admin_id/d', 0);
            $nickname = $request->get('nickname/s', '管理员');
            $token = $request->get('token/s', '');
            $type = $request->get('type/s', 'admin');

            // 记录详细日志
            // Log::info("WebSocket连接参数: admin_id={$admin_id}, nickname={$nickname}, type={$type}, token=" . (empty($token) ? '空' : '已设置'));

            // 验证参数
            if (empty($admin_id) || empty($type) || $type !== 'admin') {
                Log::error("WebSocket连接参数缺失: fd={$fd}, admin_id={$admin_id}, type={$type}");
                return $this->server->close($fd);
            }

            // 验证管理员身份 - 测试阶段暂时跳过严格验证
            // 记录尝试连接的管理员信息
            Log::info("WebSocket连接尝试: fd={$fd}, admin_id={$admin_id}, nickname={$nickname}, token={$token}");

            // 测试阶段，允许任何admin_id连接，不进行严格验证
            // 正式环境应该取消注释下面的代码进行严格验证
            /*
            $admin = Admin::where('id', $admin_id)->findOrEmpty();
            if ($admin->isEmpty() || $admin['disable']) {
                Log::error("WebSocket连接管理员不存在或已禁用: fd={$fd}, admin_id={$admin_id}");
                return $this->server->close($fd);
            }
            */

            // 绑定连接
            $this->bindAdminFd($admin_id, $nickname, $fd);

            // 发送连接成功消息
            return $this->pushData($fd, 'login', [
                'msg' => '',
                'admin_id' => $admin_id,
                'nickname' => $nickname,
                'timestamp' => time()
            ]);
        } catch (\Throwable $e) {
            Log::error('AdminNotificationHandler onOpen错误: ' . $e->getMessage());
            return $this->server->close($fd);
        }
    }

    /**
     * 接收WebSocket消息的处理函数
     * @param Frame $frame 消息帧
     * @return bool|mixed|void
     */
    public function onMessage(Frame $frame)
    {
        try {
            $param = $this->parser->decode($frame->data);

            // 记录接收到的消息
            Log::info("AdminNotificationHandler接收消息: fd={$frame->fd}, event={$param['event']}, data=" . json_encode($param['data']));

            // 处理心跳包
            if ('ping' === $param['event']) {
                return $this->ping($frame->fd);
            }

            // 处理通知事件
            if ('notification' === $param['event'] || 'admin_notification' === $param['event']) {
                Log::info("收到通知事件: " . json_encode($param));
                // 直接推送给所有管理员
                $adminFds = $this->room->getClients('admin_group');
                if (!empty($adminFds)) {
                    Log::info("推送通知给管理员组: " . count($adminFds) . "个连接");
                    return $this->pushData($adminFds, 'notification', $param['data']);
                } else {
                    Log::warning("管理员组中没有连接的客户端");
                }
                return true;
            }

            // 检查事件名称是否有效
            if (empty($param['event']) || !is_string($param['event'])) {
                Log::error("AdminNotificationHandler消息事件名称无效: fd={$frame->fd}, event=" . json_encode($param['event']) . ", 原始数据=" . $frame->data);
                return $this->pushData($frame->fd, 'error', [
                    'msg' => '消息格式错误：事件名称无效'
                ]);
            }

            // 处理其他事件
            $param['handle'] = $this;
            $param['fd'] = $frame->fd;

            return $this->triggerEvent($param['event'], $param);
        } catch (\Throwable $e) {
            Log::error('AdminNotificationHandler onMessage错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->pushData($frame->fd, 'error', [
                'msg' => $e->getMessage()
            ]);
        }
    }

    /**
     * WebSocket连接关闭时的处理函数
     * @param int $fd 连接标识符
     * @param int $reactorId 反应堆ID
     */
    public function onClose($fd, $reactorId)
    {
        Log::info("AdminNotificationHandler连接关闭: fd={$fd}");
        $this->removeAdminBind($fd);
        $this->server->close($fd);
    }

    /**
     * 绑定管理员与连接标识符
     * @param int $admin_id 管理员ID
     * @param string $nickname 管理员昵称
     * @param int $fd 连接标识符
     * @return bool
     */
    protected function bindAdminFd($admin_id, $nickname, $fd)
    {
        // 保存fd对应的管理员信息
        $fd_key = $this->prefix . 'fd_' . $fd;
        $admin_data = [
            'uid' => $admin_id,
            'nickname' => $nickname,
            'type' => 'admin',
            'fd' => $fd
        ];
        $this->cache->set($fd_key, json_encode($admin_data), 86400);

        // 保存管理员ID对应的fd
        $admin_key = $this->prefix . 'admin_' . $admin_id;
        $this->cache->set($admin_key, $fd, 86400);

        // 加入管理员组
        $this->room->add($fd, 'admin_group');

        Log::info("管理员绑定成功: admin_id={$admin_id}, nickname={$nickname}, fd={$fd}");
        return true;
    }

    /**
     * 推送到管理员组
     */
    public function sendToAdminGroup($gatewayData)
    {
        // 使用Swoole的Server实例发送消息
        if ($this->server instanceof Server) {
            // 获取所有在admin_group中的连接
            $connections = $this->room->getConnections('admin_group');

            // 构建要发送的消息
            $message = json_encode($gatewayData);

            // 遍历所有连接并发送消息
            foreach ($connections as $fd) {
                if ($this->isEstablished($fd)) {
                    $this->pushData($fd,'notification', $message);
                    Log::info('通知已通过Swoole推送给平台管理员，FD: ' . $fd);
                }
            }
            return true;
        } else {
            Log::warning('Swoole服务器实例不可用');
            return false;
        }
    }

    /**
     * 移除管理员与连接标识符的绑定
     * @param int $fd 连接标识符
     * @return bool
     */
    protected function removeAdminBind($fd)
    {
        // 获取fd对应的管理员信息
        $fd_key = $this->prefix . 'fd_' . $fd;
        $admin_data = $this->cache->get($fd_key);

        if (!empty($admin_data)) {
            $admin_data = json_decode($admin_data, true);
            $admin_id = $admin_data['uid'] ?? 0;

            // 删除管理员ID对应的fd
            if (!empty($admin_id)) {
                $admin_key = $this->prefix . 'admin_' . $admin_id;
                $this->cache->del($admin_key);
            }
        }

        // 删除fd对应的管理员信息
        $this->cache->del($fd_key);

        // 从管理员组中移除
        $this->room->delete($fd, 'admin_group');

        return true;
    }

    /**
     * 根据管理员ID获取连接标识符
     * @param int $admin_id 管理员ID
     * @return mixed
     */
    public function getFdByAdminId($admin_id)
    {
        $admin_key = $this->prefix . 'admin_' . $admin_id;
        return $this->cache->get($admin_key);
    }

    /**
     * 根据连接标识符获取管理员信息
     * @param int $fd 连接标识符
     * @return mixed
     */
    public function getAdminByFd($fd)
    {
        $fd_key = $this->prefix . 'fd_' . $fd;
        $result = $this->cache->get($fd_key);
        if (!empty($result)) {
            $result = json_decode($result, true);
        }
        return $result;
    }

    /**
     * 发送心跳包
     * @param int $fd 连接标识符
     * @return bool
     */
    public function ping($fd)
    {
        $admin = $this->getAdminByFd($fd);
        if (!empty($admin)) {
            return $this->pushData($fd, 'pong', [
                'server_time' => time(),
                'client_time' => time()
            ]);
        }
        return true;
    }

    /**
     * 推送数据
     * @param int|array $fd 连接标识符或连接标识符数组
     * @param string $event 事件名称
     * @param array $data 数据
     * @return bool
     */
    public function pushData($fd, $event, $data)
    {
        $data = $this->parser->encode($event, $data);

        // fd非数组时转为数组
        if (!is_array($fd)) {
            $fd = [$fd];
        }

        // 向fd发送消息
        foreach ($fd as $item) {
            try {
                if ($this->server->exist($item)) {
                    $this->server->push($item, $data);
                }
            } catch (\Throwable $e) {
                Log::error("推送消息失败: fd={$item}, error=" . $e->getMessage());
            }
        }
        return true;
    }

    /**
     * 触发事件
     * @param string $event 事件名称
     * @param array $data 数据
     * @return mixed
     */
    public function triggerEvent(string $event, array $data)
    {
        return $this->event->until('swoole.websocket.' . $event, $data);
    }


    /**
     * 发送通知给所有管理员
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param string $type 通知类型
     * @param string $url 跳转URL
     * @param int $icon 图标
     * @return bool
     */
    public function sendNotificationToAdmin($title, $content, $type = 'admin_notification', $url = '', $icon = 0)
    {
        try {
            // 构建通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            // 获取所有管理员的连接
            $adminFds = $this->room->getClients('admin_group');

            if (empty($adminFds)) {
                Log::warning("没有管理员在线，无法发送通知");
                return false;
            }

            Log::info("准备向" . count($adminFds) . "个管理员连接发送通知: " . json_encode($notificationData));

            // 发送通知
            return $this->pushData($adminFds, 'notification', $notificationData);
        } catch (\Throwable $e) {
            Log::error("发送管理员通知失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
}
