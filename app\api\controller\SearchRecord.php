<?php
namespace app\api\controller;

use app\api\logic\GoodsLogic;
use app\common\basics\Api;
use app\api\logic\SearchRecordLogic;
use app\common\server\JsonServer;
use app\common\server\WordUpdateServer;


class SearchRecord extends Api
{
    public $like_not_need_login = ['lists','findlists','getHotList','searchGoods','getBubble','getSearchHot','searchGoodsByMeili','getSearchSuggestions'];
    /**
     * 用户历史搜索记录
     */
    public function lists()
    {
        $lists = SearchRecordLogic::lists($this->user_id);
        return JsonServer::success('获取成功', $lists);
    }

    /**
     * 热销榜单
     */
    public function getHotList()
    {

        $get = $this->request->get();
        $get['user_id'] = $this->user_id;
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $data = GoodsLogic::getHotList($get); // 可变方法
        return JsonServer::success('获取成功', $data);
    }
    /**
     * 用户历史搜索记录
     */
    public function Findlists()
    {
        $lists = SearchRecordLogic::findlists($this->user_id);
        return JsonServer::success('获取成功', $lists);
    }

    /**
     * 清空用户搜索历史
     */
    public function clear()
    {
        if($this->request->isPost()) {
            $result = SearchRecordLogic::clear($this->user_id);
            if($result) {
                return JsonServer::success('清空成功');
            }
            return JsonServer::error(SearchRecordLogic::getError());
        }else{
            return JsonServer::error('请求类型错误');
        }
    }

    /*
     * 获取搜索气泡
     */
    public function getBubble(){

        $lists = SearchRecordLogic::getBubble($this->user_id);
        return JsonServer::success('获取成功', $lists);

    }

    /**
     * 根据关键词搜索商品
     */
    public function searchGoods()
    {
        // 获取请求中的关键词
        $keyword = $this->request->param('keywords');

        // 获取搜索选项
        $options = [];

        // 分页参数
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $options['offset'] = ($page - 1) * $limit;
        $options['limit'] = $limit;

        // 排序参数
        $sort = $this->request->param('sort');
        if (!empty($sort)) {
            $options['sort'] = [$sort];
        }

        // 过滤参数
        $filter = [];

        // 商品分类
        $cateId = $this->request->param('cate_id');
        if (!empty($cateId)) {
            $filter[] = "first_cate_id = {$cateId} OR second_cate_id = {$cateId} OR third_cate_id = {$cateId}";
        }

        // 店铺ID
        $shopId = $this->request->param('shop_id');
        if (!empty($shopId)) {
            $filter[] = "shop_id = {$shopId}";
        }

        // 品牌ID
        $brandId = $this->request->param('brand_id');
        if (!empty($brandId)) {
            $filter[] = "brand_id = {$brandId}";
        }

        // 价格范围
        $minPrice = $this->request->param('min_price');
        $maxPrice = $this->request->param('max_price');
        if (!empty($minPrice) && !empty($maxPrice)) {
            $filter[] = "min_price >= {$minPrice} AND min_price <= {$maxPrice}";
        } else if (!empty($minPrice)) {
            $filter[] = "min_price >= {$minPrice}";
        } else if (!empty($maxPrice)) {
            $filter[] = "min_price <= {$maxPrice}";
        }

        // 是否热门
        $isHot = $this->request->param('is_hot');
        if (!empty($isHot)) {
            $filter[] = "is_hot = {$isHot}";
        }

        // 是否推荐
        $isRecommend = $this->request->param('is_recommend');
        if (!empty($isRecommend)) {
            $filter[] = "is_recommend = {$isRecommend}";
        }

        // 合并过滤条件
        if (!empty($filter)) {
            $options['filter'] = implode(' AND ', $filter);
        }

        // 执行搜索
        $data = SearchRecordLogic::searchGoods($keyword, $this->user_id, $options);

        // 返回搜索结果
        return JsonServer::success('搜索成功', $data);
    }



    /*
     * 获取热榜
     * DM
     *
     */
    public function getSearchHot(){
        $data = SearchRecordLogic::getSearchHot($this->user_id);
        return JsonServer::success('获取成功', $data);
    }


    /**
     * 获取搜索建议
     */
    public function getSearchSuggestions()
    {
        // 获取请求中的关键词
        $keyword = $this->request->param('keywords');

        // 获取限制数量
        $limit = $this->request->param('limit', 10);

        // 获取搜索建议
        $suggestions = SearchRecordLogic::getSearchSuggestions($keyword, $limit);

        // 返回搜索建议
        return JsonServer::success('获取成功', $suggestions);
    }
}