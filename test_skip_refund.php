<?php
/**
 * 测试跳过退款的订单取消功能
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 测试跳过退款的订单取消功能 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 1. 使用原版本（会尝试退款，但有异常处理）
    echo "1. 推送订单取消任务（原版本，有退款异常处理）...\n";
    $orderData1 = [
        'order_id' => 335,
        'user_id' => 1168
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJob', $orderData1, 'orderCancel');
    echo "   ✓ 任务推送成功（会尝试退款，失败时跳过）\n";
    echo "   任务数据: " . json_encode($orderData1, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 2. 使用V2版本（可配置跳过退款）
    echo "2. 推送订单取消任务（V2版本，跳过退款）...\n";
    $orderData2 = [
        'order_id' => 335,
        'user_id' => 1168,
        'skip_refund' => true  // 跳过退款
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJobV2', $orderData2, 'orderCancel');
    echo "   ✓ 任务推送成功（跳过退款操作）\n";
    echo "   任务数据: " . json_encode($orderData2, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "3. 解决方案说明:\n";
    echo "   方案1: 修改原版本，添加退款异常处理\n";
    echo "   方案2: 使用V2版本，可配置是否跳过退款\n";
    echo "   方案3: 修复微信支付证书配置（推荐）\n\n";
    
    echo "4. 微信证书配置检查:\n";
    echo "   请检查以下配置文件中的微信支付证书路径:\n";
    echo "   - config/pay.php\n";
    echo "   - 微信支付证书文件是否存在\n";
    echo "   - 证书文件权限是否正确\n\n";
    
    echo "5. 启动队列监听:\n";
    echo "   php start_queue.php\n\n";
    
    echo "=== 测试完成 ===\n";
    echo "现在订单取消应该能正常工作，即使退款失败也不会影响订单状态更新！\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
