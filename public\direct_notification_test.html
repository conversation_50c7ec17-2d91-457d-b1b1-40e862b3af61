<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接通知测试</title>
    <link rel="stylesheet" href="/static/admin/css/layui.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #0066cc;
        }
        .log-entry.success {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>直接通知测试</h1>
        
        <div class="form-group">
            <label for="title">通知标题</label>
            <input type="text" id="title" value="测试通知" placeholder="通知标题">
        </div>
        
        <div class="form-group">
            <label for="content">通知内容</label>
            <textarea id="content" placeholder="通知内容">这是一条测试通知，请查收！</textarea>
        </div>
        
        <div class="form-group">
            <label for="type">通知类型</label>
            <select id="type">
                <option value="admin_notification">管理员通知</option>
                <option value="system">系统通知</option>
                <option value="personal">个人通知</option>
                <option value="system_notification">系统通知(新格式)</option>
                <option value="custom_notification">自定义通知</option>
                <option value="error_notification">错误通知</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="url">跳转URL（可选）</label>
            <input type="text" id="url" placeholder="点击通知后跳转的URL">
        </div>
        
        <div class="form-group">
            <label for="icon">通知图标</label>
            <select id="icon">
                <option value="0">默认图标</option>
                <option value="1">成功图标</option>
                <option value="2">错误图标</option>
                <option value="3">警告图标</option>
                <option value="4">信息图标</option>
            </select>
        </div>
        
        <div class="form-group">
            <button id="show-notification">显示通知</button>
            <button id="show-layer-msg">显示简单消息</button>
            <button id="show-alert">显示Alert</button>
            <button id="clear-log">清空日志</button>
        </div>
        
        <div class="log-container">
            <h3>操作日志</h3>
            <div id="log"></div>
        </div>
    </div>
    
    <script src="/static/admin/js/jquery.min.js"></script>
    <script src="/static/admin/js/layui.js"></script>
    <script>
        // 添加日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
        }
        
        // 显示通知
        function showNotification(title, content, type, url, icon) {
            addLog(`显示通知: ${title} - ${type}`, 'info');
            
            // 根据通知类型设置不同的样式
            var notificationSkin = 'layui-layer-molv'; // 默认墨绿皮肤
            var notificationIcon = icon || 0; // 使用传入的图标或默认图标
            
            switch (type) {
                case 'admin_notification':
                    notificationSkin = 'layui-layer-molv'; // 墨绿皮肤
                    break;
                case 'system':
                case 'system_notification':
                    notificationSkin = 'layui-layer-lan'; // 蓝色皮肤
                    notificationIcon = 1;
                    break;
                case 'personal':
                case 'custom_notification':
                    notificationSkin = 'layui-layer-rim'; // 简约风格
                    break;
                case 'error_notification':
                    notificationSkin = 'layui-layer-red'; // 红色皮肤
                    notificationIcon = 2;
                    break;
                default:
                    // 使用默认样式
            }
            
            try {
                // 显示 LayUI 通知
                var layerIndex = layer.open({
                    type: 1,
                    title: title,
                    content: '<div style="padding: 20px; line-height: 1.8;">' + content.replace(/\n/g, '<br>') + '</div>',
                    shade: 0,
                    offset: 'rb', // 右下角
                    anim: 2,      // 从最底部往上滑入
                    time: 15000,  // 15秒后自动关闭
                    area: ['350px', 'auto'], // 固定宽度，高度自适应
                    skin: notificationSkin,
                    btn: url ? ['查看详情', '关闭'] : ['关闭'],
                    yes: function(index) {
                        if (url) {
                            // 如果有URL，点击"查看详情"按钮时跳转
                            window.open(url, '_blank');
                        }
                        layer.close(index);
                    },
                    btn2: function(index) {
                        layer.close(index);
                        return false;
                    },
                    success: function(layero, index) {
                        addLog('通知显示成功，层索引: ' + index, 'success');
                        // 可以在这里添加点击事件等
                        $(layero).find('.layui-layer-content').css('max-height', '300px').css('overflow-y', 'auto');
                    },
                    cancel: function() {
                        addLog('用户关闭了通知', 'info');
                    }
                });
                
                addLog('通知层索引: ' + layerIndex, 'success');
                
                // 播放提示音
                playNotificationSound();
                
                return layerIndex;
            } catch (error) {
                addLog('显示通知时出错: ' + error.message, 'error');
                // 如果layer弹窗失败，尝试使用原生alert
                alert('通知: ' + title + '\n' + content);
            }
        }
        
        // 播放通知声音
        function playNotificationSound() {
            try {
                var audio = new Audio('/static/admin/sound/tomsg.mp3');
                audio.volume = 0.7; // 设置音量为70%
                var playPromise = audio.play();
                
                if (playPromise !== undefined) {
                    playPromise.then(function() {
                        addLog('提示音播放成功', 'success');
                    }).catch(function(error) {
                        addLog('播放提示音失败: ' + error.message, 'error');
                    });
                }
            } catch (audioError) {
                addLog('创建或播放音频对象时出错: ' + audioError.message, 'error');
            }
        }
        
        // 显示通知按钮点击事件
        document.getElementById('show-notification').addEventListener('click', function() {
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const type = document.getElementById('type').value;
            const url = document.getElementById('url').value;
            const icon = document.getElementById('icon').value;
            
            if (!title || !content) {
                addLog('标题和内容不能为空', 'error');
                return;
            }
            
            showNotification(title, content, type, url, parseInt(icon));
        });
        
        // 显示简单消息按钮点击事件
        document.getElementById('show-layer-msg').addEventListener('click', function() {
            const title = document.getElementById('title').value;
            const icon = document.getElementById('icon').value;
            
            if (!title) {
                addLog('标题不能为空', 'error');
                return;
            }
            
            addLog('显示简单消息: ' + title, 'info');
            
            try {
                layer.msg(title, {
                    icon: parseInt(icon),
                    time: 3000
                });
                
                addLog('简单消息显示成功', 'success');
            } catch (error) {
                addLog('显示简单消息时出错: ' + error.message, 'error');
            }
        });
        
        // 显示Alert按钮点击事件
        document.getElementById('show-alert').addEventListener('click', function() {
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            
            if (!title || !content) {
                addLog('标题和内容不能为空', 'error');
                return;
            }
            
            addLog('显示Alert: ' + title, 'info');
            
            alert(title + '\n' + content);
            
            addLog('Alert显示成功', 'success');
        });
        
        // 清空日志按钮点击事件
        document.getElementById('clear-log').addEventListener('click', function() {
            document.getElementById('log').innerHTML = '';
        });
        
        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('页面加载完成，可以开始测试通知功能', 'info');
            
            // 初始化LayUI
            layui.use(['layer'], function() {
                var layer = layui.layer;
                
                // 显示欢迎消息
                layer.msg('通知测试工具已加载', {icon: 1, time: 2000});
            });
        });
    </script>
</body>
</html>
