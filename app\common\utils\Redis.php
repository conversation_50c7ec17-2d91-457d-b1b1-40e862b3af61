<?php

namespace app\common\utils;

use think\facade\Cache;

class Redis
{
    public $redis = null;


    public function __construct()
    {
        $this->redis = Cache::store('redis')->handler();
    }


    /**
     * @notes 设置缓存
     * @param $key
     * @param $val
     * @param null $time
     * @return false
     * <AUTHOR>
     * @date 2021/12/20 12:13
     */
    public function set($key, $val, $time = null)
    {
        if (empty($key)) {
            return false;
        }
        if (is_array($val)) {
            $val = json_encode($val, JSON_UNESCAPED_UNICODE);
        }
        return $this->redis->set($key, $val, $time);
    }


    /**
     * @notes 获取缓存
     * @param $key
     * @return false
     * <AUTHOR>
     * @date 2021/12/20 12:14
     */
    public function get($key)
    {
        if (empty($key)) {
            return false;
        }
        return $this->redis->get($key);
    }


    /**
     * @notes 删除指定
     * @param $key
     * @return mixed
     * <AUTHOR>
     * @date 2021/12/20 12:02
     */
    public function del($key)
    {
        return $this->redis->del($key);
    }


    /**
     * @notes 清空
     * @return mixed
     * <AUTHOR>
     * @date 2021/12/20 12:02
     */
    public function flashAll()
    {
        return $this->redis->flushAll();
    }


    /**
     * @notes 获取集合
     * @param $key
     * @return mixed
     * <AUTHOR>
     * @date 2021/12/20 12:11
     */
    public function sMembers($key)
    {
        return $this->redis->sMembers($key);
    }


    /**
     * @notes 设置缓存时间
     * @param $key
     * @param $ttl
     * @return mixed
     * <AUTHOR>
     * @date 2021/12/20 12:02
     */
    public function expire($key, $ttl)
    {
        return $this->redis->expire($key, $ttl);
    }


    /**
     * @notes 向集合添加成员
     * @param $key
     * @param $val
     * @return mixed
     * <AUTHOR>
     * @date 2021/12/20 12:04
     */
    public function sadd($key, $val)
    {
        return $this->redis->sAdd($key, $val);
    }


    /**
     * @notes 移除集合成员
     * @param $key
     * @param $val
     * @return mixed
     * <AUTHOR>
     * @date 2021/12/20 12:04
     */
    public function srem($key, $val)
    {
        return $this->redis->sRem($key, $val);
    }

    /**
     * @notes 对象转数组
     * @param $key
     * @return array|false
     * <AUTHOR>
     * @date 2021/12/20 12:03
     */
    public function getSmembersArray($key)
    {
        $res = $this->sMembers($key);
        if (is_object($res)) {
            return (array)$res;
        }
        return $res;
    }


    /**
     * @notes 相似keys
     * @param $prefix
     * @return mixed
     * <AUTHOR>
     * @date 2021/12/20 12:02
     */
    public function keys($prefix)
    {
        return $this->redis->keys($prefix.'*');
    }

    /**
     * @notes 发布消息到频道
     * @param string $channel 频道名称
     * @param string $message 消息内容
     * @return int 接收到消息的订阅者数量
     */
    public function publish($channel, $message)
    {
        if (is_array($message) || is_object($message)) {
            $message = json_encode($message, JSON_UNESCAPED_UNICODE);
        }
        return $this->redis->publish($channel, $message);
    }

    /**
     * @notes 订阅频道
     * @param array|string $channels 频道名称或频道数组
     * @param callable $callback 回调函数
     * @return bool
     */
    public function subscribe($channels, $callback)
    {
        if (!is_array($channels)) {
            $channels = [$channels];
        }
        return $this->redis->subscribe($channels, $callback);
    }

    /**
     * @notes 检查Redis是否支持发布订阅功能
     * @return bool
     */
    public function supportsPubSub()
    {
        try {
            // 尝试发布一条测试消息
            $this->publish('test_pubsub', 'test');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}