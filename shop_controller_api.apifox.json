{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "Shop Controller API", "description": "根据 app/shopapi/controller/Shop.php 生成的接口文档", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "Shop Controller", "id": ********, "auth": {}, "parentId": 0, "description": "商家相关接口", "items": [{"name": "商家基础", "id": 10000001, "parentId": ********, "description": "商家基本信息及设置", "items": [{"name": "获取商家信息及余额", "api": {"id": "shop-getShopInfo", "method": "get", "path": "/shopapi/shop/getShopInfo", "description": "获取商家可提现余额等基础信息", "tags": ["商家基础"], "responses": [{"id": "resp-getShopInfo-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "设置商家信息", "api": {"id": "shop-shopSet", "method": "post", "path": "/shopapi/shop/shopSet", "description": "设置商家信息", "tags": ["商家基础"], "responses": [{"id": "resp-shopSet-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "修改密码", "api": {"id": "shop-changePwd", "method": "post", "path": "/shopapi/shop/changePwd", "description": "修改当前登录管理员密码", "tags": ["商家基础", "管理员"], "responses": [{"id": "resp-changePwd-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"old_password": {"type": "string"}, "password": {"type": "string"}, "password_confirm": {"type": "string"}}}}, "parameters": {}}}]}, {"name": "提现管理", "id": 10000002, "parentId": ********, "description": "商家提现相关接口", "items": [{"name": "获取提现配置信息", "api": {"id": "shop-getWithdrawInfo", "method": "get", "path": "/shopapi/shop/getWithdrawInfo", "description": "获取提现所需配置信息，如手续费、最低提现额等", "tags": ["提现管理"], "responses": [{"id": "resp-getWithdrawInfo-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "申请提现", "api": {"id": "shop-withdraw", "method": "post", "path": "/shopapi/shop/withdraw", "description": "提交提现申请", "tags": ["提现管理"], "responses": [{"id": "resp-withdraw-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"money": {"type": "number"}, "bank_id": {"type": "integer"}}}}, "parameters": {}}}, {"name": "提现记录", "api": {"id": "shop-withdrawLog", "method": "get", "path": "/shopapi/shop/withdrawLog", "description": "获取商家提现记录列表", "tags": ["提现管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-withdrawLog-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}, {"name": "银行卡管理", "id": ********, "parentId": ********, "description": "商家提现银行卡管理", "items": [{"name": "添加银行卡", "api": {"id": "shop-addBank", "method": "post", "path": "/shopapi/shop/addBank", "description": "添加提现银行卡账户", "tags": ["银行卡管理"], "responses": [{"id": "resp-addBank-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "获取银行卡详情", "api": {"id": "shop-getBank", "method": "get", "path": "/shopapi/shop/getBank", "description": "获取指定银行卡详情", "tags": ["银行卡管理"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-getBank-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "编辑银行卡", "api": {"id": "shop-editBank", "method": "post", "path": "/shopapi/shop/editBank", "description": "编辑银行卡信息", "tags": ["银行卡管理"], "responses": [{"id": "resp-editBank-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer"}}}}, "parameters": {}}}, {"name": "删除银行卡", "api": {"id": "shop-delBank", "method": "post", "path": "/shopapi/shop/delBank", "description": "删除银行卡", "tags": ["银行卡管理"], "responses": [{"id": "resp-delBank-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}]}, {"name": "运费模板", "id": ********, "parentId": ********, "description": "运费模板管理", "items": [{"name": "设置快递方式开关", "api": {"id": "freight-set", "method": "post", "path": "/shopapi/shop/set", "description": "设置是否启用快递发货", "tags": ["运费模板"], "responses": [{"id": "resp-freight-set-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["on", "off"]}}}}, "parameters": {}}}, {"name": "运费模板列表", "api": {"id": "freight-lists", "method": "get", "path": "/shopapi/shop/lists", "description": "获取运费模板列表及配置", "tags": ["运费模板"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-freight-lists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "添加运费模板", "api": {"id": "freight-add", "method": "post", "path": "/shopapi/shop/add", "description": "添加新的运费模板", "tags": ["运费模板"], "responses": [{"id": "resp-freight-add-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "删除运费模板", "api": {"id": "freight-del", "method": "post", "path": "/shopapi/shop/del", "description": "删除指定的运费模板", "tags": ["运费模板"], "responses": [{"id": "resp-freight-del-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "运费模板详情", "api": {"id": "freight-detail", "method": "get", "path": "/shopapi/shop/detail", "description": "获取运费模板详情", "tags": ["运费模板"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-freight-detail-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "编辑运费模板", "api": {"id": "freight-edit", "method": "post", "path": "/shopapi/shop/edit", "description": "编辑运费模板", "tags": ["运费模板"], "responses": [{"id": "resp-freight-edit-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer"}}}}, "parameters": {}}}, {"name": "获取地区树", "api": {"id": "freight-areaTree", "method": "get", "path": "/shopapi/shop/areaTree", "description": "获取省市区树状结构数据(无需登录)", "tags": ["运费模板", "公共"], "responses": [{"id": "resp-areaTree-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}]}, {"name": "发票管理", "id": 10000005, "parentId": ********, "description": "商家发票管理", "items": [{"name": "发票列表", "api": {"id": "invoice-Invoicelists", "method": "get", "path": "/shopapi/shop/Invoicelists", "description": "获取发票列表", "tags": ["发票管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-Invoicelists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "开具发票", "api": {"id": "invoice-setInvoice-post", "method": "post", "path": "/shopapi/shop/setInvoice", "description": "提交开票申请", "tags": ["发票管理"], "responses": [{"id": "resp-setInvoice-post-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "获取发票详情(用于开票)", "api": {"id": "invoice-setInvoice-get", "method": "get", "path": "/shopapi/shop/setInvoice", "description": "获取待开票订单的相关信息", "tags": ["发票管理"], "parameters": {"query": [{"name": "form", "type": "integer", "enum": [1], "required": true}, {"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-setInvoice-get-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "导出Excel(发票)", "api": {"id": "invoice-export", "method": "get", "path": "/shopapi/shop/export", "description": "导出符合条件的发票记录为Excel", "tags": ["发票管理"], "parameters": {"query": []}, "responses": [{"id": "resp-invoice-export-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}, {"name": "客服管理", "id": 10000006, "parentId": ********, "description": "客服账号及设置管理", "items": [{"name": "客服列表", "api": {"id": "kefu-kefulists", "method": "get", "path": "/shopapi/shop/kefulists", "description": "获取客服账号列表", "tags": ["客服管理"], "parameters": {"query": [{"name": "page", "type": "integer"}, {"name": "limit", "type": "integer"}]}, "responses": [{"id": "resp-kefulists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "添加客服", "api": {"id": "kefu-kef<PERSON>d", "method": "post", "path": "/shopapi/shop/kefuadd", "description": "添加新的客服账号", "tags": ["客服管理"], "responses": [{"id": "resp-kefuadd-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "编辑客服", "api": {"id": "kefu-kefuedit-post", "method": "post", "path": "/shopapi/shop/kefuedit", "description": "编辑客服账号信息", "tags": ["客服管理"], "responses": [{"id": "resp-kefuedit-post-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer"}}}}, "parameters": {}}}, {"name": "获取客服详情(用于编辑)", "api": {"id": "kefu-kefuedit-get", "method": "get", "path": "/shopapi/shop/kefuedit", "description": "获取客服账号详情", "tags": ["客服管理"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-kefuedit-get-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "删除客服", "api": {"id": "kefu-kefudel", "method": "post", "path": "/shopapi/shop/kefudel", "description": "删除客服账号", "tags": ["客服管理"], "responses": [{"id": "resp-kefudel-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "设置客服状态", "api": {"id": "kefu-status", "method": "post", "path": "/shopapi/shop/status", "description": "启用或禁用客服账号", "tags": ["客服管理"], "responses": [{"id": "resp-kefu-status-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}, "disable": {"type": "integer", "enum": [0, 1]}}}}, "parameters": {}}}, {"name": "获取客服详情", "api": {"id": "kefu-kefuDetail", "method": "get", "path": "/shopapi/shop/kefuDetail", "description": "获取单个客服账号的详细信息", "tags": ["客服管理"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-kefuDetail-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "获取客服联系方式设置", "api": {"id": "kefu-getCustomerService", "method": "get", "path": "/shopapi/shop/getCustomerService", "description": "获取店铺对外展示的客服联系方式（移动端接口）", "tags": ["客服管理", "店铺设置"], "responses": [{"id": "resp-getCustomerService-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "设置客服联系方式", "api": {"id": "kefu-setCustomerService", "method": "post", "path": "/shopapi/shop/setCustomerService", "description": "设置店铺对外展示的客服联系方式（移动端接口）", "tags": ["客服管理", "店铺设置"], "responses": [{"id": "resp-setCustomerService-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"type": {"type": "integer"}, "wechat": {"type": "string"}, "phone": {"type": "string"}, "business_time": {"type": "string"}, "image": {"type": "string"}}}}, "parameters": {}}}, {"name": "获取客服自动回复设置", "api": {"id": "kefu-getAutoReply", "method": "get", "path": "/shopapi/shop/kefuAutoReply", "description": "获取客服自动回复内容及状态", "tags": ["客服管理", "客服设置"], "responses": [{"id": "resp-get<PERSON>utoReply-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "设置客服自动回复", "api": {"id": "kefu-setAutoReply", "method": "post", "path": "/shopapi/shop/kefuAutoReply", "description": "设置客服自动回复内容及状态", "tags": ["客服管理", "客服设置"], "responses": [{"id": "resp-set<PERSON><PERSON>Reply-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"content": {"type": "string"}, "status": {"type": "integer", "enum": [0, 1]}}}}, "parameters": {}}}]}, {"name": "客服话术", "id": 10000007, "parentId": ********, "description": "客服常用话术管理", "items": [{"name": "客服话术分组列表", "api": {"id": "kefulang-groupList", "method": "get", "path": "/shopapi/shop/kefuLangGroupList", "description": "获取所有客服话术分组", "tags": ["客服话术"], "responses": [{"id": "resp-kefuLangGroupList-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "新增客服话术分组", "api": {"id": "kefulang-groupAdd", "method": "post", "path": "/shopapi/shop/kefuLangGroupAdd", "description": "添加新的客服话术分组", "tags": ["客服话术"], "responses": [{"id": "resp-kefuLangGroupAdd-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"name": {"type": "string"}}}}, "parameters": {}}}, {"name": "客服话术列表", "api": {"id": "kefulang-lists", "method": "get", "path": "/shopapi/shop/kefuLangLists", "description": "获取客服话术列表（可按分组筛选）", "tags": ["客服话术"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}, {"name": "group_id", "type": "integer"}]}, "responses": [{"id": "resp-kefuLangLists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "客服话术详情", "api": {"id": "kefulang-detail", "method": "get", "path": "/shopapi/shop/kefuLangDetail", "description": "获取单条客服话术详情", "tags": ["客服话术"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-kefuLangDetail-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "添加客服话术", "api": {"id": "kefulang-add", "method": "post", "path": "/shopapi/shop/kefuLangAdd", "description": "添加新的客服话术", "tags": ["客服话术"], "responses": [{"id": "resp-kefuLangAdd-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "编辑客服话术", "api": {"id": "kefulang-edit", "method": "post", "path": "/shopapi/shop/kefuLangEdit", "description": "编辑客服话术", "tags": ["客服话术"], "responses": [{"id": "resp-kefuLangEdit-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer"}}}}, "parameters": {}}}, {"name": "删除客服话术", "api": {"id": "kefulang-del", "method": "post", "path": "/shopapi/shop/kefuLangDel", "description": "删除客服话术", "tags": ["客服话术"], "responses": [{"id": "resp-kefuLangDel-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}]}, {"name": "集采购", "id": 10000008, "parentId": ********, "description": "集采购订单管理", "items": [{"name": "集采购订单列表", "api": {"id": "jcai-orderLists", "method": "get", "path": "/shopapi/shop/jcaiOrderLists", "description": "获取集采购订单列表", "tags": ["集采购"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-jcaiOrderLists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "集采购订单详情", "api": {"id": "jcai-orderDetail", "method": "get", "path": "/shopapi/shop/jcaiOrderDetail", "description": "获取集采购订单详情", "tags": ["集采购"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-jcaiOrderDetail-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}, {"name": "退货地址", "id": 10000009, "parentId": ********, "description": "退货地址管理", "items": [{"name": "退货地址列表", "api": {"id": "refundAddress-list", "method": "get", "path": "/shopapi/shop/refundAddressList", "description": "获取商家设置的退货地址列表", "tags": ["退货地址"], "responses": [{"id": "resp-refundAddressList-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "添加退货地址", "api": {"id": "refundAddress-add", "method": "post", "path": "/shopapi/shop/refundAddressAdd", "description": "添加新的退货地址", "tags": ["退货地址"], "responses": [{"id": "resp-refundAddressAdd-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "编辑退货地址", "api": {"id": "refundAddress-edit", "method": "post", "path": "/shopapi/shop/refundAddressEdit", "description": "编辑退货地址", "tags": ["退货地址"], "responses": [{"id": "resp-refundAddressEdit-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer"}}}}, "parameters": {}}}, {"name": "删除退货地址", "api": {"id": "refundAddress-del", "method": "post", "path": "/shopapi/shop/refundAddressDel", "description": "删除退货地址", "tags": ["退货地址"], "responses": [{"id": "resp-refundAddressDel-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "设置默认退货地址", "api": {"id": "refundAddress-set<PERSON>efault", "method": "post", "path": "/shopapi/shop/setDefaultRefundAddress", "description": "设置默认的退货地址", "tags": ["退货地址"], "responses": [{"id": "resp-setDefaultRefundAddress-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}]}, {"name": "会员管理", "id": 10000010, "parentId": ********, "description": "查看店铺会员信息", "items": [{"name": "会员列表", "api": {"id": "user-lists", "method": "get", "path": "/shopapi/shop/Userlists", "description": "获取店铺关联的会员列表", "tags": ["会员管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-Userlists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "会员详情", "api": {"id": "user-info", "method": "get", "path": "/shopapi/shop/Userinfo", "description": "获取单个会员的详细信息", "tags": ["会员管理"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-Userinfo-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}, {"name": "管理员管理", "id": 10000011, "parentId": ********, "description": "商家子账号管理", "items": [{"name": "管理员列表", "api": {"id": "admin-lists", "method": "get", "path": "/shopapi/shop/adminLists", "description": "获取商家子管理员列表", "tags": ["管理员管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-adminLists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "添加管理员", "api": {"id": "admin-add", "method": "post", "path": "/shopapi/shop/Adminadd", "description": "添加商家子管理员账号", "tags": ["管理员管理"], "responses": [{"id": "resp-Adminadd-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}, {"name": "编辑管理员", "api": {"id": "admin-edit-post", "method": "post", "path": "/shopapi/shop/Adminedit", "description": "编辑商家子管理员信息", "tags": ["管理员管理"], "responses": [{"id": "resp-Adminedit-post-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"admin_id": {"type": "integer"}}}}, "parameters": {}}}, {"name": "获取管理员详情(用于编辑)", "api": {"id": "admin-edit-get", "method": "get", "path": "/shopapi/shop/Adminedit", "description": "获取管理员信息及可选角色列表", "tags": ["管理员管理"], "parameters": {"query": [{"name": "admin_id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-Adminedit-get-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "删除管理员", "api": {"id": "admin-del", "method": "post", "path": "/shopapi/shop/Admindel", "description": "删除商家子管理员账号", "tags": ["管理员管理"], "responses": [{"id": "resp-<PERSON><PERSON><PERSON>-<PERSON>", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"admin_id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "修改(指定)管理员密码", "api": {"id": "admin-password", "method": "post", "path": "/shopapi/shop/password", "description": "修改指定管理员的密码(通常是当前登录的管理员修改自己的)", "tags": ["管理员管理", "商家基础"], "responses": [{"id": "resp-admin-password-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"password": {"type": "string"}}}}, "parameters": {}}}]}, {"name": "角色权限", "id": 10000012, "parentId": ********, "description": "商家管理员角色与权限管理", "items": [{"name": "角色列表", "api": {"id": "role-lists", "method": "get", "path": "/shopapi/shop/roleLists", "description": "获取商家自定义的角色列表", "tags": ["角色权限"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-roleLists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "添加角色", "api": {"id": "role-add", "method": "post", "path": "/shopapi/shop/roleAdd", "description": "添加新的管理员角色", "tags": ["角色权限"], "responses": [{"id": "resp-roleAdd-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"name": {"type": "string"}, "menu_auth": {"type": "array", "items": {"type": "integer"}}}}}}, "parameters": {}}, {"name": "编辑角色", "api": {"id": "role-edit", "method": "post", "path": "/shopapi/shop/roleEdit", "description": "编辑角色信息及权限", "tags": ["角色权限"], "responses": [{"id": "resp-roleEdit-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "menu_auth": {"type": "array", "items": {"type": "integer"}}}}}}, "parameters": {}}, {"name": "删除角色", "api": {"id": "role-del", "method": "post", "path": "/shopapi/shop/roleDel", "description": "删除角色", "tags": ["角色权限"], "responses": [{"id": "resp-roleDel-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "获取角色详情", "api": {"id": "role-info", "method": "get", "path": "/shopapi/shop/roleInfo", "description": "获取角色详细信息（用于编辑回显）", "tags": ["角色权限"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-roleInfo-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "获取权限树", "api": {"id": "role-auth<PERSON><PERSON>", "method": "get", "path": "/shopapi/shop/roleAuthTree", "description": "获取可分配的权限菜单树（用于添加/编辑角色）", "tags": ["角色权限"], "parameters": {"query": [{"name": "role_id", "type": "integer", "description": "编辑时传入角色ID以选中已有权限"}]}, "responses": [{"id": "resp-role<PERSON><PERSON><PERSON><PERSON>-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}, {"name": "售后管理", "id": 10000013, "parentId": ********, "description": "处理订单售后申请", "items": [{"name": "售后列表", "api": {"id": "aftersale-lists", "method": "get", "path": "/shopapi/shop/afterSaleLists", "description": "获取售后申请列表", "tags": ["售后管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-afterSaleLists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "售后详情", "api": {"id": "aftersale-detail", "method": "get", "path": "/shopapi/shop/afterSaleDetail", "description": "获取售后申请详细信息", "tags": ["售后管理"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-afterSaleDetail-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "同意售后申请", "api": {"id": "aftersale-agree", "method": "post", "path": "/shopapi/shop/afterSaleAgree", "description": "同意用户的售后申请（退款/退货退款）", "tags": ["售后管理"], "responses": [{"id": "resp-afterSaleAgree-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "拒绝售后申请", "api": {"id": "aftersale-refuse", "method": "post", "path": "/shopapi/shop/afterSaleRefuse", "description": "拒绝用户的售后申请", "tags": ["售后管理"], "responses": [{"id": "resp-afterSaleRefuse-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}, "refuse_reason": {"type": "string", "required": true}}}}, "parameters": {}}}, {"name": "确认收货(退货)", "api": {"id": "aftersale-take", "method": "post", "path": "/shopapi/shop/afterSaleTake", "description": "商家确认收到用户退回的商品", "tags": ["售后管理"], "responses": [{"id": "resp-afterSaleTake-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "拒绝收货(退货)", "api": {"id": "aftersale-refuseGoods", "method": "post", "path": "/shopapi/shop/afterSaleRefuseGoods", "description": "商家拒绝签收用户退回的商品", "tags": ["售后管理"], "responses": [{"id": "resp-afterSaleRefuseGoods-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}, "refund_reason": {"type": "string", "required": true}}}}, "parameters": {}}}, {"name": "确认退款", "api": {"id": "aftersale-confirm", "method": "post", "path": "/shopapi/shop/afterSaleConfirm", "description": "最终确认执行退款操作", "tags": ["售后管理"], "responses": [{"id": "resp-afterSaleConfirm-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "售后导出Excel", "api": {"id": "aftersale-export", "method": "get", "path": "/shopapi/shop/afterSaleExport", "description": "导出符合条件的售后记录为Excel", "tags": ["售后管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-afterSaleExport-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}, {"name": "支付管理", "id": 10000014, "parentId": ********, "description": "支付相关接口", "items": [{"name": "统一支付入口", "api": {"id": "pay-unifiedPay", "method": "post", "path": "/shopapi/shop/unifiedPay", "description": "用于处理商家入驻费、保证金、广告位购买等多种支付场景", "tags": ["支付管理", "入驻缴费", "保证金", "广告位"], "responses": [{"id": "resp-unifiedPay-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"order_sn": {"type": "string", "required": true}, "pay_way": {"type": "integer", "required": true}, "from": {"type": "string", "required": true}, "user_id": {"type": "integer"}, "mobile": {"type": "string"}}}}, "parameters": {}}}, {"name": "缴纳入驻费(专用)", "api": {"id": "pay-ruzhucharge", "method": "post", "path": "/shopapi/shop/ruzhucharge", "description": "专门用于商家缴纳入驻费的接口", "tags": ["支付管理", "入驻缴费"], "responses": [{"id": "resp-ruzhu<PERSON>rge-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"mobile": {"type": "string", "required": true}, "pay_way": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "获取入驻费模板", "api": {"id": "pay-getuzhuTemplate", "method": "get", "path": "/shopapi/shop/getuzhuTemplate", "description": "获取商家入驻需要缴纳的费用标准", "tags": ["支付管理", "入驻缴费"], "responses": [{"id": "resp-getuzhuTemplate-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "缴纳保证金(专用)", "api": {"id": "pay-bondcharge", "method": "post", "path": "/shopapi/shop/bondcharge", "description": "专门用于商家缴纳保证金的接口", "tags": ["支付管理", "保证金"], "responses": [{"id": "resp-bondcharge-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"money": {"type": "number", "required": true}, "pay_way": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "补缴保证金(专用)", "api": {"id": "pay-replenishDeposit", "method": "post", "path": "/shopapi/shop/replenishDeposit", "description": "专门用于商家补缴保证金的接口", "tags": ["支付管理", "保证金"], "responses": [{"id": "resp-replenishDeposit-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"money": {"type": "number", "required": true}, "pay_way": {"type": "integer", "required": true}}}}, "parameters": {}}}, {"name": "购买广告位(专用)", "api": {"id": "pay-purchaseAdSlot", "method": "post", "path": "/shopapi/shop/purchaseAdSlot", "description": "专门用于商家购买广告位的接口", "tags": ["支付管理", "广告位"], "responses": [{"id": "resp-purchaseAdSlot-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"ad_position_id": {"type": "integer", "required": true}, "pay_way": {"type": "integer", "required": true}}}}, "parameters": {}}}]}, {"name": "规则协议与反馈", "id": 10000015, "parentId": ********, "description": "查看平台规则协议及提交反馈", "items": [{"name": "规则协议列表", "api": {"id": "protocol-lists", "method": "get", "path": "/shopapi/shop/protocolLists", "description": "获取平台发布的规则协议列表", "tags": ["规则协议与反馈"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-protocolLists-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "规则协议详情", "api": {"id": "protocol-detail", "method": "get", "path": "/shopapi/shop/protocolDetail", "description": "获取单个规则协议的详细内容", "tags": ["规则协议与反馈"], "parameters": {"query": [{"name": "id", "type": "integer", "required": true}]}, "responses": [{"id": "resp-protocolDetail-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "意见反馈", "api": {"id": "feedback-add", "method": "post", "path": "/shopapi/shop/feedback", "description": "商家提交意见反馈", "tags": ["规则协议与反馈"], "responses": [{"id": "resp-feedback-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {}}}, "parameters": {}}}]}, {"name": "文件上传", "id": 10000016, "parentId": ********, "description": "文件上传接口", "items": [{"name": "上传图片(form)", "api": {"id": "file-uploadImage", "method": "post", "path": "/shopapi/shop/uploadImage", "description": "通过 form-data 方式上传图片（商家端专用）", "tags": ["文件上传"], "responses": [{"id": "resp-uploadImage-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "multipart/form-data", "parameters": [{"name": "file", "type": "file", "required": true}]}, "parameters": {}}}]}, {"name": "保证金管理", "id": 10000017, "parentId": ********, "description": "商家保证金查询与缴纳", "items": [{"name": "获取保证金配置及信息", "api": {"id": "deposit-getConfig", "method": "get", "path": "/shopapi/shop/getDepositConfig", "description": "获取平台保证金配置及当前商家已缴纳信息", "tags": ["保证金管理"], "responses": [{"id": "resp-getDepositConfig-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}, "parameters": {}}}, {"name": "获取保证金明细", "api": {"id": "deposit-details", "method": "get", "path": "/shopapi/shop/depositDetails", "description": "获取商家保证金的收支明细记录", "tags": ["保证金管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-depositDetails-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}, {"name": "广告位管理", "id": 10000018, "parentId": ********, "description": "商家广告位购买与管理", "items": [{"name": "获取可购买广告位列表", "api": {"id": "adslot-listAvailable", "method": "get", "path": "/shopapi/shop/listAvailableAdSlots", "description": "查看当前平台开放的可购买广告位", "tags": ["广告位管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-listAvailableAdSlots-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "获取我的广告位列表", "api": {"id": "adslot-listMy", "method": "get", "path": "/shopapi/shop/listMyAdSlots", "description": "获取商家已购买的广告位列表", "tags": ["广告位管理"], "parameters": {"query": [{"name": "page_no", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-listMyAdSlots-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}, {"name": "更新我的广告内容", "api": {"id": "adslot-updateMy", "method": "post", "path": "/shopapi/shop/updateMyAdSlot", "description": "更新已购买广告位展示的内容（图片、链接等）", "tags": ["广告位管理"], "responses": [{"id": "resp-updateMyAdSlot-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "json", "jsonSchema": {"type": "object", "properties": {"ad_order_id": {"type": "integer", "required": true}, "image": {"type": "string", "required": true}, "link_type": {"type": "integer", "required": true}}}}, "parameters": {}}}]}, {"name": "入驻缴费", "id": 10000019, "parentId": ********, "description": "商家入驻缴费相关", "items": [{"name": "获取入驻费及权益模板", "api": {"id": "shop-getAdTemplate", "method": "get", "path": "/shopapi/shop/getAdTemplate", "description": "获取商家入驻费及组合购买权益模板信息", "tags": ["入驻缴费"], "parameters": {"query": [{"name": "page", "type": "integer"}, {"name": "page_size", "type": "integer"}]}, "responses": [{"id": "resp-getAdTemplate-ok", "code": 200, "name": "成功"}], "requestBody": {"type": "none"}}}]}]}]}