<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知主测试工具</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .header {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 20px;
        }
        .tabs {
            display: flex;
            background-color: #f1f1f1;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 2px solid #4CAF50;
        }
        .iframe-container {
            flex: 1;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .actions {
            display: flex;
            gap: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>通知主测试工具</h1>
            <div class="actions">
                <button id="refresh-iframe">刷新当前页面</button>
                <button id="open-all">在新窗口打开所有测试页面</button>
                <button id="test-notification">发送测试通知</button>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-iframe="test_websocket.html">原始测试页面</div>
            <div class="tab" data-iframe="direct_websocket_test.html">直接WebSocket测试</div>
            <div class="tab" data-iframe="direct_notification_test.html">直接通知测试</div>
            <div class="tab" data-iframe="api_notification_test.html">API通知测试</div>
            <div class="tab" data-iframe="test_websocket_compatible.html">兼容测试页面</div>
            <div class="tab" data-iframe="admin_notification_test.html">高级测试页面</div>
        </div>
        
        <div class="iframe-container">
            <iframe id="test-iframe" src="test_websocket.html"></iframe>
        </div>
    </div>
    
    <script>
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签页的active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                
                // 添加当前标签页的active类
                this.classList.add('active');
                
                // 更新iframe的src
                const iframeSrc = this.getAttribute('data-iframe');
                document.getElementById('test-iframe').src = iframeSrc;
            });
        });
        
        // 刷新iframe
        document.getElementById('refresh-iframe').addEventListener('click', function() {
            document.getElementById('test-iframe').src = document.getElementById('test-iframe').src;
        });
        
        // 在新窗口打开所有测试页面
        document.getElementById('open-all').addEventListener('click', function() {
            const testPages = [
                'test_websocket.html',
                'direct_websocket_test.html',
                'direct_notification_test.html',
                'api_notification_test.html',
                'test_websocket_compatible.html',
                'admin_notification_test.html'
            ];
            
            testPages.forEach(page => {
                window.open(page, '_blank');
            });
        });
        
        // 发送测试通知
        document.getElementById('test-notification').addEventListener('click', function() {
            // 构建随机通知内容
            const timestamp = new Date().toLocaleTimeString();
            const title = `测试通知 ${timestamp}`;
            const content = `这是一条测试通知，发送时间: ${timestamp}`;
            
            // 发送GET请求到测试通知API
            fetch(`/api/notification/testNotification?title=${encodeURIComponent(title)}&content=${encodeURIComponent(content)}&type=admin_notification`, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    alert(`通知发送成功: ${data.msg || '成功'}`);
                } else {
                    alert(`通知发送失败: ${data.msg || '未知错误'}`);
                }
            })
            .catch(error => {
                alert(`发送请求出错: ${error.message}`);
            });
        });
    </script>
</body>
</html>
