<?php
/**
 * 测试微信证书路径修复
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 测试微信证书路径修复 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 1. 检查是否在CLI环境
    echo "1. 环境检查:\n";
    echo "   是否CLI环境: " . (is_cli() ? '是' : '否') . "\n";
    echo "   项目根路径: " . app()->getRootPath() . "\n\n";
    
    // 2. 获取微信支付配置
    echo "2. 获取微信支付配置:\n";
    $config = \app\common\server\WeChatServer::getPayConfig('mnp');
    echo "   证书路径: " . ($config['cert_path'] ?? '未配置') . "\n";
    echo "   密钥路径: " . ($config['key_path'] ?? '未配置') . "\n";
    
    // 3. 检查证书文件是否存在
    echo "\n3. 证书文件检查:\n";
    if (!empty($config['cert_path'])) {
        $certExists = file_exists($config['cert_path']);
        echo "   证书文件存在: " . ($certExists ? '是' : '否') . "\n";
        if (!$certExists) {
            echo "   证书文件路径: " . $config['cert_path'] . "\n";
        }
    }
    
    if (!empty($config['key_path'])) {
        $keyExists = file_exists($config['key_path']);
        echo "   密钥文件存在: " . ($keyExists ? '是' : '否') . "\n";
        if (!$keyExists) {
            echo "   密钥文件路径: " . $config['key_path'] . "\n";
        }
    }
    
    // 4. 推送订单取消任务测试
    echo "\n4. 推送订单取消任务测试:\n";
    $orderData = [
        'order_id' => 335,
        'user_id' => 1168
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJob', $orderData, 'orderCancel');
    echo "   ✓ 订单取消任务推送成功\n";
    echo "   任务数据: " . json_encode($orderData, JSON_UNESCAPED_UNICODE) . "\n";
    
    echo "\n5. 修复说明:\n";
    echo "   ✓ 修复了CLI环境中的证书路径问题\n";
    echo "   ✓ 使用app()->getRootPath()获取正确的项目根路径\n";
    echo "   ✓ 移除了临时的退款异常处理代码\n";
    echo "   ✓ 现在微信退款应该能正常工作\n\n";
    
    echo "6. 启动队列监听:\n";
    echo "   php start_queue.php\n\n";
    
    echo "=== 测试完成 ===\n";
    echo "现在微信退款功能应该能正常工作了！\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
