<?php
/**
 * 队列测试脚本
 * 用于测试订单取消队列是否正常工作
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

try {
    echo "开始测试队列配置...\n";
    
    // 测试队列连接
    $queue = \think\facade\Queue::connection('redis');
    echo "队列连接成功\n";
    
    // 测试推送一个简单任务到队列
    \think\facade\Queue::push('app\common\job\OrderCancelJob', [
        'order_id' => 999999, // 测试订单ID
        'user_id' => 1        // 测试用户ID
    ], 'orderCancel');
    
    echo "测试任务已推送到队列\n";
    echo "队列配置正常！\n";
    echo "\n";
    echo "要启动队列监听，请运行以下命令：\n";
    echo "php think queue:listen --queue=orderCancel\n";
    echo "或者：\n";
    echo "php think queue:work --queue=orderCancel\n";
    
} catch (\Exception $e) {
    echo "队列测试失败: " . $e->getMessage() . "\n";
    echo "请检查Redis连接和队列配置\n";
}
