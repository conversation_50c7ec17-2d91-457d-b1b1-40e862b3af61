var e,i;import{K as o,L as c,o as n,f as l,w as u,D as t,C as a,h as r,t as v,i as s,p as f,O as d,q as p,ak as h,al as m,am as y,an as b,ao as g,ap as w,aq as k}from"./index-KqVIYTFB.js";const S={"uvicon-level":"e68f","uvicon-checkbox-mark":"e659","uvicon-folder":"e694","uvicon-movie":"e67c","uvicon-star-fill":"e61e","uvicon-star":"e618","uvicon-phone-fill":"e6ac","uvicon-phone":"e6ba","uvicon-apple-fill":"e635","uvicon-backspace":"e64d","uvicon-attach":"e640","uvicon-empty-data":"e671","uvicon-empty-address":"e68a","uvicon-empty-favor":"e662","uvicon-empty-car":"e657","uvicon-empty-order":"e66b","uvicon-empty-list":"e672","uvicon-empty-search":"e677","uvicon-empty-permission":"e67d","uvicon-empty-news":"e67e","uvicon-empty-history":"e685","uvicon-empty-coupon":"e69b","uvicon-empty-page":"e60e","uvicon-empty-wifi-off":"e6cc","uvicon-reload":"e627","uvicon-order":"e695","uvicon-server-man":"e601","uvicon-search":"e632","uvicon-more-dot-fill":"e66f","uvicon-scan":"e631","uvicon-map":"e665","uvicon-map-fill":"e6a8","uvicon-tags":"e621","uvicon-tags-fill":"e613","uvicon-eye":"e664","uvicon-eye-fill":"e697","uvicon-eye-off":"e69c","uvicon-eye-off-outline":"e688","uvicon-mic":"e66d","uvicon-mic-off":"e691","uvicon-calendar":"e65c","uvicon-trash":"e623","uvicon-trash-fill":"e6ce","uvicon-play-left":"e6bf","uvicon-play-right":"e6b3","uvicon-minus":"e614","uvicon-plus":"e625","uvicon-info-circle":"e69f","uvicon-info-circle-fill":"e6a7","uvicon-question-circle":"e622","uvicon-question-circle-fill":"e6bc","uvicon-close":"e65a","uvicon-checkmark":"e64a","uvicon-checkmark-circle":"e643","uvicon-checkmark-circle-fill":"e668","uvicon-setting":"e602","uvicon-setting-fill":"e6d0","uvicon-heart":"e6a2","uvicon-heart-fill":"e68b","uvicon-camera":"e642","uvicon-camera-fill":"e650","uvicon-more-circle":"e69e","uvicon-more-circle-fill":"e684","uvicon-chat":"e656","uvicon-chat-fill":"e63f","uvicon-bag":"e647","uvicon-error-circle":"e66e","uvicon-error-circle-fill":"e655","uvicon-close-circle":"e64e","uvicon-close-circle-fill":"e666","uvicon-share":"e629","uvicon-share-fill":"e6bb","uvicon-share-square":"e6c4","uvicon-shopping-cart":"e6cb","uvicon-shopping-cart-fill":"e630","uvicon-bell":"e651","uvicon-bell-fill":"e604","uvicon-list":"e690","uvicon-list-dot":"e6a9","uvicon-zhifubao-circle-fill":"e617","uvicon-weixin-circle-fill":"e6cd","uvicon-weixin-fill":"e620","uvicon-qq-fill":"e608","uvicon-qq-circle-fill":"e6b9","uvicon-moments-circel-fill":"e6c2","uvicon-moments":"e6a0","uvicon-car":"e64f","uvicon-car-fill":"e648","uvicon-warning-fill":"e6c7","uvicon-warning":"e6c1","uvicon-clock-fill":"e64b","uvicon-clock":"e66c","uvicon-edit-pen":"e65d","uvicon-edit-pen-fill":"e679","uvicon-email":"e673","uvicon-email-fill":"e683","uvicon-minus-circle":"e6a5","uvicon-plus-circle":"e603","uvicon-plus-circle-fill":"e611","uvicon-file-text":"e687","uvicon-file-text-fill":"e67f","uvicon-pushpin":"e6d1","uvicon-pushpin-fill":"e6b6","uvicon-grid":"e68c","uvicon-grid-fill":"e698","uvicon-play-circle":"e6af","uvicon-play-circle-fill":"e62a","uvicon-pause-circle-fill":"e60c","uvicon-pause":"e61c","uvicon-pause-circle":"e696","uvicon-gift-fill":"e6b0","uvicon-gift":"e680","uvicon-kefu-ermai":"e660","uvicon-server-fill":"e610","uvicon-coupon-fill":"e64c","uvicon-coupon":"e65f","uvicon-integral":"e693","uvicon-integral-fill":"e6b1","uvicon-home-fill":"e68e","uvicon-home":"e67b","uvicon-account":"e63a","uvicon-account-fill":"e653","uvicon-thumb-down-fill":"e628","uvicon-thumb-down":"e60a","uvicon-thumb-up":"e612","uvicon-thumb-up-fill":"e62c","uvicon-lock-fill":"e6a6","uvicon-lock-open":"e68d","uvicon-lock-opened-fill":"e6a1","uvicon-lock":"e69d","uvicon-red-packet":"e6c3","uvicon-photo-fill":"e6b4","uvicon-photo":"e60d","uvicon-volume-off-fill":"e6c8","uvicon-volume-off":"e6bd","uvicon-volume-fill":"e624","uvicon-volume":"e605","uvicon-download":"e670","uvicon-arrow-up-fill":"e636","uvicon-arrow-down-fill":"e638","uvicon-play-left-fill":"e6ae","uvicon-play-right-fill":"e6ad","uvicon-arrow-downward":"e634","uvicon-arrow-leftward":"e63b","uvicon-arrow-rightward":"e644","uvicon-arrow-upward":"e641","uvicon-arrow-down":"e63e","uvicon-arrow-right":"e63c","uvicon-arrow-left":"e646","uvicon-arrow-up":"e633","uvicon-skip-back-left":"e6c5","uvicon-skip-forward-right":"e61f","uvicon-arrow-left-double":"e637","uvicon-man":"e675","uvicon-woman":"e626","uvicon-en":"e6b8","uvicon-twitte":"e607","uvicon-twitter-circle-fill":"e6cf"},x=(e,i)=>{const o=e.__vccOpts||e;for(const[c,n]of i)o[c]=n;return o};const $=x({name:"uv-icon",emits:["click"],mixins:[o,c,{props:{name:{type:String,default:""},color:{type:String,default:"#606266"},size:{type:[String,Number],default:"16px"},bold:{type:Boolean,default:!1},index:{type:[String,Number],default:null},hoverClass:{type:String,default:""},customPrefix:{type:String,default:"uvicon"},label:{type:[String,Number],default:""},labelPos:{type:String,default:"right"},labelSize:{type:[String,Number],default:"15px"},labelColor:{type:String,default:"#606266"},space:{type:[String,Number],default:"3px"},imgMode:{type:String,default:"aspectFit"},width:{type:[String,Number],default:""},height:{type:[String,Number],default:""},top:{type:[String,Number],default:0},stop:{type:Boolean,default:!1},...null==(i=null==(e=uni.$uv)?void 0:e.props)?void 0:i.icon}}],data:()=>({colorType:["primary","success","info","error","warning"]}),computed:{uClasses(){let e=[];return e.push(this.customPrefix),e.push(this.customPrefix+"-"+this.name),this.color&&this.colorType.includes(this.color)&&e.push("uv-icon__icon--"+this.color),e},iconStyle(){let e={};return e={fontSize:this.$uv.addUnit(this.size),lineHeight:this.$uv.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:this.$uv.addUnit(this.top)},this.color&&!this.colorType.includes(this.color)&&(e.color=this.color),e},isImg(){const e=this.name.indexOf("data:")>-1&&this.name.indexOf("base64")>-1;return-1!==this.name.indexOf("/")||e},imgStyle(){let e={};return e.width=this.width?this.$uv.addUnit(this.width):this.$uv.addUnit(this.size),e.height=this.height?this.$uv.addUnit(this.height):this.$uv.addUnit(this.size),e},icon(){const e=S["uvicon-"+this.name];return e?unescape(`%u${e}`):["uvicon"].indexOf(this.customPrefix)>-1?this.name:""}},methods:{clickHandler(e){this.$emit("click",this.index),this.stop&&this.preventEvent(e)}}},[["render",function(e,i,o,c,h,m){const y=f,b=d,g=p;return n(),l(g,{class:a(["uv-icon",["uv-icon--"+e.labelPos]]),onClick:m.clickHandler},{default:u((()=>[m.isImg?(n(),l(y,{key:0,class:"uv-icon__img",src:e.name,mode:e.imgMode,style:t([m.imgStyle,e.$uv.addStyle(e.customStyle)])},null,8,["src","mode","style"])):(n(),l(b,{key:1,class:a(["uv-icon__icon",m.uClasses]),style:t([m.iconStyle,e.$uv.addStyle(e.customStyle)]),"hover-class":e.hoverClass},{default:u((()=>[r(v(m.icon),1)])),_:1},8,["class","style","hover-class"])),""!==e.label?(n(),l(b,{key:2,class:"uv-icon__label",style:t({color:e.labelColor,fontSize:e.$uv.addUnit(e.labelSize),marginLeft:"right"==e.labelPos?e.$uv.addUnit(e.space):0,marginTop:"bottom"==e.labelPos?e.$uv.addUnit(e.space):0,marginRight:"left"==e.labelPos?e.$uv.addUnit(e.space):0,marginBottom:"top"==e.labelPos?e.$uv.addUnit(e.space):0})},{default:u((()=>[r(v(e.label),1)])),_:1},8,["style"])):s("",!0)])),_:1},8,["onClick","class"])}],["__scopeId","data-v-553bbf60"]]);function _(e,i){return"string"==typeof e?i:e}const U=e=>(i,o=y())=>{!h&&m(e,i,o)},z=U(b),P=U(g),q=U(w),C=U(k);export{x as _,z as a,q as b,C as c,$ as d,P as o,_ as r};
