/**
 * <PERSON><PERSON><PERSON>dmin 主题样式文件
 * 用于iframe内容页面的主题同步
 */

/* 主题变量定义 */
:root {
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --border-color: #e2e8f0;
  --accent-primary: #3b82f6;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --border-color: #475569;
  --accent-primary: #60a5fa;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
  --gradient-primary: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
}

/* 基础样式重置 */
html, body {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  transition: all 0.3s ease;
}

/* 通用容器样式 */
.layui-container,
.layui-fluid {
  background: var(--bg-primary) !important;
}

/* 卡片样式 */
.layui-card {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-sm) !important;
  border-radius: 8px !important;
}

.layui-card-header {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.layui-card-body {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* 表格样式 */
.layui-table {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color) !important;
}

.layui-table thead tr {
  background: var(--bg-tertiary) !important;
}

.layui-table thead tr th {
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.layui-table tbody tr {
  background: var(--bg-secondary) !important;
}

.layui-table tbody tr:hover {
  background: var(--bg-tertiary) !important;
}

.layui-table tbody tr td {
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* 表单样式 */
.layui-form-item {
  color: var(--text-primary) !important;
}

.layui-form-label {
  color: var(--text-primary) !important;
  background: var(--bg-tertiary) !important;
  border-color: var(--border-color) !important;
}

.layui-input,
.layui-textarea,
.layui-select {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.layui-input:focus,
.layui-textarea:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* 按钮样式 */
.layui-btn {
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.layui-btn-primary {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.layui-btn-primary:hover {
  background: var(--bg-tertiary) !important;
  border-color: var(--accent-primary) !important;
}

/* 面板样式 */
.layui-panel {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 8px !important;
}

/* 导航样式 */
.layui-nav {
  background: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
}

.layui-nav .layui-nav-item > a {
  color: var(--text-primary) !important;
}

.layui-nav .layui-nav-item > a:hover {
  color: var(--accent-primary) !important;
}

.layui-nav .layui-this > a {
  background: var(--accent-primary) !important;
  color: white !important;
}

/* 分页样式 */
.layui-laypage {
  color: var(--text-primary) !important;
}

.layui-laypage a {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.layui-laypage a:hover {
  background: var(--bg-tertiary) !important;
  color: var(--accent-primary) !important;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: var(--accent-primary) !important;
  color: white !important;
}

/* 选项卡样式 */
.layui-tab {
  background: var(--bg-secondary) !important;
}

.layui-tab-title {
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.layui-tab-title li {
  color: var(--text-secondary) !important;
}

.layui-tab-title li:hover {
  color: var(--text-primary) !important;
}

.layui-tab-title .layui-this {
  color: var(--accent-primary) !important;
  border-bottom-color: var(--accent-primary) !important;
}

.layui-tab-content {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* 进度条样式 */
.layui-progress {
  background: var(--bg-tertiary) !important;
}

.layui-progress-bar {
  background: var(--accent-primary) !important;
}

/* 徽章样式 */
.layui-badge {
  background: var(--accent-primary) !important;
}

/* 时间线样式 */
.layui-timeline-item {
  color: var(--text-primary) !important;
}

.layui-timeline-axis {
  color: var(--text-muted) !important;
}

/* 面包屑样式 */
.layui-breadcrumb {
  color: var(--text-secondary) !important;
}

.layui-breadcrumb a {
  color: var(--text-secondary) !important;
}

.layui-breadcrumb a:hover {
  color: var(--accent-primary) !important;
}

/* 统计卡片样式 */
.layui-row .layui-col-md3 .layui-card {
  transition: all 0.3s ease !important;
}

.layui-row .layui-col-md3 .layui-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-md) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .layui-card {
    margin: 8px !important;
    border-radius: 6px !important;
  }
  
  .layui-table {
    font-size: 12px !important;
  }
}
