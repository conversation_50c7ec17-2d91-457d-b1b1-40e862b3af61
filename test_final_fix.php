<?php
/**
 * 测试最终修复的订单取消功能
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP应用
$app = new \think\App();
$app->initialize();

echo "=== 测试最终修复的订单取消功能 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 1. 推送订单取消任务
    echo "1. 推送订单取消任务...\n";
    $orderData = [
        'order_id' => 335,  // 使用真实的订单ID
        'user_id' => 1168   // 使用真实的用户ID
    ];
    
    \think\facade\Queue::push('app\common\job\OrderCancelJob', $orderData, 'orderCancel');
    echo "   ✓ 订单取消任务推送成功\n";
    echo "   任务数据: " . json_encode($orderData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "2. 最终修复内容:\n";
    echo "   ✓ 修复了JsonServer返回值类型错误\n";
    echo "   ✓ 将Json对象改为数组返回\n";
    echo "   ✓ 修复了结果检查逻辑\n";
    echo "   ✓ 确保所有返回值都是数组格式\n\n";
    
    echo "3. 返回值格式:\n";
    echo "   成功: ['code' => 1, 'msg' => '取消成功']\n";
    echo "   失败: ['code' => 0, 'msg' => '错误信息']\n\n";
    
    echo "4. 启动队列监听:\n";
    echo "   php start_queue.php\n\n";
    
    echo "=== 测试完成 ===\n";
    echo "现在应该不会再有类型错误了！\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
